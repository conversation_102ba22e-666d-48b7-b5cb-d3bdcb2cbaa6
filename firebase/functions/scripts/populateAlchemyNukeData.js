const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

const USER_ID = 'r86ubKyHnfZx7wJthDfgWNCj06u1';
const USER_EMAIL = '<EMAIL>';

async function populateUserData() {
    try {
        /*
        // 1. Set up user role
        await db.doc(`users_roles/${USER_ID}`).set({
            email: USER_EMAIL,
            role: 'patient',
            status: 'active',
            created_at: admin.firestore.Timestamp.fromDate(new Date('2025-03-03')),
            lastProfileUpdate: admin.firestore.FieldValue.serverTimestamp()
        });

        // 2. Create patient profile
        await db.doc(`patients/${USER_ID}`).set({
            name: "Alchemy Nuke",
            email: USER_EMAIL,
            age: 35,
            height: 178, // cm
            weight: 75,  // kg
            bmi: (75 / Math.pow(178 / 100, 2)).toFixed(1),
            smokingStatus: "non-smoker",
            createdAt: admin.firestore.Timestamp.fromDate(new Date('2025-03-03')),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        */

        // 3. Generate readings for the last 30 days
        const now = new Date();
        for (let i = 0; i < 30; i++) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            
            // Create 3 readings per day
            for (let j = 0; j < 3; j++) {
                const readingTime = new Date(date);
                readingTime.setHours(8 + (j * 4)); // 8am, 12pm, 4pm

                const reading = {
                    timestamp: admin.firestore.Timestamp.fromDate(readingTime),
                    respiratoryRate: 16 + Math.random() * 4,    // 16-20
                    oxygenSaturation: 96 + Math.random() * 2,   // 96-98
                    heartRate: 65 + Math.random() * 15,         // 65-80
                    temperature: 36.6 + (Math.random() * 0.6),  // 36.6-37.2
                    riskLevel: "low",
                    processedAt: admin.firestore.FieldValue.serverTimestamp()
                };

                await db.collection(`patients/${USER_ID}/readings`).add(reading);

                // Update daily metrics
                await db.doc(`patients/${USER_ID}/daily_metrics/${dateStr}`).set({
                    respiratoryRate: reading.respiratoryRate,
                    oxygenSaturation: reading.oxygenSaturation,
                    heartRate: reading.heartRate,
                    temperature: reading.temperature,
                    riskLevel: reading.riskLevel,
                    readingCount: admin.firestore.FieldValue.increment(1),
                    lastUpdated: reading.timestamp
                }, { merge: true });
            }
        }

        // 4. Set latest metrics in profile
        await db.doc(`patients/${USER_ID}/profile/metrics`).set({
            latestReading: {
                respiratoryRate: 18,
                oxygenSaturation: 97,
                heartRate: 72,
                temperature: 36.8,
                riskLevel: "low",
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            }
        });

        // 5. Add health records summary
        await db.doc(`patients/${USER_ID}/health_records/readings`).set({
            latest: {
                respiratoryRate: 18,
                oxygenSaturation: 97,
                heartRate: 72,
                temperature: 36.8,
                timestamp: admin.firestore.FieldValue.serverTimestamp(),
                riskLevel: "low"
            },
            recent: [
                {
                    respiratoryRate: 17,
                    oxygenSaturation: 96,
                    heartRate: 70,
                    temperature: 36.7,
                    timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 3600000)),
                    riskLevel: "low"
                },
                {
                    respiratoryRate: 19,
                    oxygenSaturation: 98,
                    heartRate: 73,
                    temperature: 36.9,
                    timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 7200000)),
                    riskLevel: "low"
                }
            ]
        });

        // 6. Add some activities
        const activities = [
            {
                id: Date.now().toString(),
                name: "Walking",
                date: new Date().toLocaleDateString(),
                value: "45",
                measure: "min",
                color: "green",
                type: "physical_activity"
            },
            {
                id: (Date.now() + 1).toString(),
                name: "Breathing Exercise",
                date: new Date().toLocaleDateString(),
                value: "20",
                measure: "min",
                color: "blue",
                type: "respiratory_exercise"
            }
        ];

        for (const activity of activities) {
            await db.collection(`patients/${USER_ID}/activities`).add(activity);
        }

        console.log('Data populated successfully for user:', USER_EMAIL);

    } catch (error) {
        console.error('Error populating data:', error);
        throw error;
    }
}

// Run the population script
populateUserData()
    .then(() => {
        console.log('Script completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Script failed:', error);
        process.exit(1);
    });