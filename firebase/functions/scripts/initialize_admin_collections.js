const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function initializeCollections() {
  try {
    console.log('🔄 Starting collection initialization...');

    // Initialize overview_stats with actual data
    await db.doc('overview_stats/latest').set({
      totalPatients: 25,
      activePatients: 18,
      criticalAlerts: 3,
      dailyReadings: 72,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ overview_stats initialized');

    // Initialize system_health with realistic metrics
    await db.doc('system_health/current').set({
      status: 'healthy',
      lastUpdate: admin.firestore.FieldValue.serverTimestamp(),
      metrics: {
        cpuUsage: 45.2,
        memoryUsage: 68.7,
        activeConnections: 12,
        apiLatency: 245 // milliseconds
      }
    });
    console.log('✅ system_health initialized');

    // Initialize admin_settings with default thresholds
    await db.doc('admin_settings/general').set({
      alertThresholds: {
        respiratory: { warning: 25, critical: 30 },
        oxygen: { warning: 92, critical: 88 },
        heartRate: { warning: 100, critical: 120 },
        temperature: { warning: 38, critical: 39 }
      },
      notificationSettings: {
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: false
      },
      dataRetentionDays: 90,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ admin_settings initialized');

    // Create some sample alerts
    const alertsCollection = db.collection('alerts');
    const sampleAlerts = [
      {
        patientId: 'SAMPLE001',
        type: 'HIGH_RESPIRATORY_RATE',
        severity: 'critical',
        message: 'Respiratory rate exceeds critical threshold',
        value: 32,
        threshold: 30,
        status: 'active',
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        patientId: 'SAMPLE002',
        type: 'LOW_OXYGEN',
        severity: 'warning',
        message: 'Oxygen saturation below warning threshold',
        value: 91,
        threshold: 92,
        status: 'active',
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    for (const alert of sampleAlerts) {
      await alertsCollection.add(alert);
    }
    console.log('✅ sample alerts created');

    // Create sample daily metrics for the past 7 days
    const today = new Date();
    const dailyMetricsCollection = db.collection('daily_metrics');
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      await dailyMetricsCollection.doc(dateStr).set({
        date: dateStr,
        totalReadings: Math.floor(60 + Math.random() * 40),
        averageMetrics: {
          respiratoryRate: Math.floor(16 + Math.random() * 4),
          oxygenSaturation: Math.floor(95 + Math.random() * 3),
          heartRate: Math.floor(70 + Math.random() * 10),
          temperature: (36.5 + Math.random()).toFixed(1)
        },
        criticalAlerts: Math.floor(Math.random() * 3),
        activePatients: Math.floor(15 + Math.random() * 5)
      });
    }
    console.log('✅ daily metrics initialized');

    // Create indexes for commonly queried fields
    await db.collection('daily_metrics').doc('_index').set({
      fields: ['date', 'patientId', 'readings']
    });

    await db.collection('alerts').doc('_index').set({
      fields: ['timestamp', 'severity', 'patientId', 'status']
    });
    console.log('✅ indexes created');

    console.log('🎉 Successfully initialized all admin collections with sample data');
  } catch (error) {
    console.error('❌ Error initializing collections:', error);
    throw error;
  }
}

// Run the initialization script
initializeCollections()
  .then(() => {
    console.log('✨ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
