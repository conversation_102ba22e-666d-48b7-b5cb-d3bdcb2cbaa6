{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "12"}, "main": "index.js", "dependencies": {"firebase-admin": "^9.2.0", "firebase-functions": "^3.11.0"}, "devDependencies": {"eslint": "^7.6.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^0.2.0"}, "private": true}