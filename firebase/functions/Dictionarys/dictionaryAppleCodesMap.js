/** Dictionary with all Apple HKtypes display format */
exports.dictionaryAppleMap = {    
    "HKQuantityTypeIdentifierAppleExerciseTime":{
        code: "HKQuantityTypeIdentifierAppleExerciseTime",
        display: "Apple Exercise Time"
    },
    "HKQuantityTypeIdentifierAppleStandTime":{
        code: "HKQuantityTypeIdentifierAppleStandTime",
        display: "Apple Stand Time"
    },
    "HKQuantityTypeIdentifierEnvironmentalAudioExposure":{
        code: "HKQuantityTypeIdentifierEnvironmentalAudioExposure",
        display: "Environmental Audio Exposure"
    },
    "HKCategoryTypeIdentifierAbdominalCramps":{
        code: "HKCategoryTypeIdentifierAbdominalCramps",
        display: "Abdominal Cramps"
    },
    "HKCategoryTypeIdentifierAcne":{
        code: "HKCategoryTypeIdentifierAcne",
        display: "Acne as a symptom"
    },
    "HKCategoryTypeIdentifierAppetiteChanges":{
        code: "HKCategoryTypeIdentifierAppetiteChanges",
        display: "changes in appetite"
    },
    "HKCategoryTypeIdentifierBladderIncontinence":{
        code: "HKCategoryTypeIdentifierBladderIncontinence",
        display: "Bladder Incontinence"
    },
    "HKCategoryTypeIdentifierBloating":{
        code: "HKCategoryTypeIdentifierBloating",
        display: "Bloating"
    },
    "HKCategoryTypeIdentifierBreastPain":{
        code: "HKCategoryTypeIdentifierBreastPain",
        display: "Breast Pain"
    },
    "HKCategoryTypeIdentifierCervicalMucusQuality":{
        code: "HKCategoryTypeIdentifierCervicalMucusQuality",
        display: "the quality of the user’s cervical mucus"
    },
    "HKCategoryTypeIdentifierChestTightnessOrPain":{
        code: "HKCategoryTypeIdentifierChestTightnessOrPain",
        display: "Chest tightness or pain"
    },
    "HKCategoryTypeIdentifierChills":{
        code: "HKCategoryTypeIdentifierChills",
        display: "Chills"
    },
    "HKCategoryTypeIdentifierConstipation":{
        code: "HKCategoryTypeIdentifierConstipation",
        display: "Constipation"
    },
    "HKCategoryTypeIdentifierCoughing":{
        code: "HKCategoryTypeIdentifierCoughing",
        display: "Coughing"
    },
    "HKCategoryTypeIdentifierDiarrhea":{
        code: "HKCategoryTypeIdentifierDiarrhea",
        display: "Diarrhea"
    },
    "HKCategoryTypeIdentifierDizziness":{
        code: "HKCategoryTypeIdentifierDizziness",
        display: "Dizziness"
    },
    "HKCategoryTypeIdentifierDrySkin":{
        code: "HKCategoryTypeIdentifierDrySkin",
        display: "Dry skin"
    },
    "HKCategoryTypeIdentifierFainting":{
        code: "HKCategoryTypeIdentifierFainting",
        display: "Fainting"
    },
    "HKCategoryTypeIdentifierFatigue":{
        code: "HKCategoryTypeIdentifierFatigue",
        display: "Fatigue"
    },
    "HKCategoryTypeIdentifierFever":{
        code: "HKCategoryTypeIdentifierFever",
        display: "Fever"
    },
    "HKCategoryTypeIdentifierGeneralizedBodyAche":{
        code: "HKCategoryTypeIdentifierGeneralizedBodyAche",
        display: "Body Ache"
    },
    "HKCategoryTypeIdentifierHairLoss":{
        code: "HKCategoryTypeIdentifierHairLoss",
        display: "HairLoss"
    },
    "HKCategoryTypeIdentifierHandwashingEvent":{
        code: "HKCategoryTypeIdentifierHandwashingEvent",
        display: "Handwashing events"
    },
    "HKCategoryTypeIdentifierHeadache":{
        code: "HKCategoryTypeIdentifierHeadache",
        display: "Headache"
    },
    "HKCategoryTypeIdentifierHeartburn":{
        code: "HKCategoryTypeIdentifierHeartburn",
        display: "Heartburn"
    },
    "HKCategoryTypeIdentifierHotFlashes":{
        code: "HKCategoryTypeIdentifierHotFlashes",
        display: "Hot flashes"
    },
    "HKCategoryTypeIdentifierIntermenstrualBleeding":{
        code: "HKCategoryTypeIdentifierIntermenstrualBleeding",
        display: "Spotting outside the normal menstruation period"
    },
    "HKCategoryTypeIdentifierLossOfSmell":{
        code: "HKCategoryTypeIdentifierLossOfSmell",
        display: "Loss of smell"
    },
    "HKCategoryTypeIdentifierLossOfTaste":{
        code: "HKCategoryTypeIdentifierLossOfTaste",
        display: "Loss of taste"
    },
    "HKCategoryTypeIdentifierLowerBackPain":{
        code: "HKCategoryTypeIdentifierLowerBackPain",
        display: "Lower back pain"
    },
    "HKCategoryTypeIdentifierMemoryLapse":{
        code: "HKCategoryTypeIdentifierMemoryLapse",
        display: "Memory lapse"
    },
    "HKCategoryTypeIdentifierMenstrualFlow":{
        code: "HKCategoryTypeIdentifierMenstrualFlow",
        display: "Menstrual Cycles"
    },
    "HKCategoryTypeIdentifierMindfulSession":{
        code: "HKCategoryTypeIdentifierMindfulSession",
        display: "Minful session"
    },
    "HKCategoryTypeIdentifierMoodChanges":{
        code: "HKCategoryTypeIdentifierMoodChanges",
        display: "Mood changes"
    },
    "HKCategoryTypeIdentifierNausea":{
        code: "HKCategoryTypeIdentifierNausea",
        display: "Nausea"
    },
    "HKCategoryTypeIdentifierNightSweats":{
        code: "HKCategoryTypeIdentifierNightSweats",
        display: "Night sweats"
    },
    "HKCategoryTypeIdentifierPelvicPain":{
        code: "HKCategoryTypeIdentifierPelvicPain",
        display: "Pelvic pain"
    },
    "HKCategoryTypeIdentifierRapidPoundingOrFlutteringHeartbeat":{
        code: "HKCategoryTypeIdentifierRapidPoundingOrFlutteringHeartbeat",
        display: "Rapid, pounding, or fluttering heartbeat"
    },
    "HKCategoryTypeIdentifierRunnyNose":{
        code: "HKCategoryTypeIdentifierRunnyNose",
        display: "Runny nose"
    },
    "HKCategoryTypeIdentifierSexualActivity":{
        code: "HKCategoryTypeIdentifierSexualActivity",
        display: "Sexual Activity"
    },
    "HKCategoryTypeIdentifierShortnessOfBreath":{
        code: "HKCategoryTypeIdentifierShortnessOfBreath",
        display: "Shortness of breath"
    },
    "HKCategoryTypeIdentifierSinusCongestion":{
        code: "HKCategoryTypeIdentifierSinusCongestion",
        display: "Sinus congestion"
    },
    "HKCategoryTypeIdentifierSkippedHeartbeat":{
        code: "HKCategoryTypeIdentifierSkippedHeartbeat",
        display: "Skipped heartbeat"
    },
    "HKCategoryTypeIdentifierSleepAnalysis":{
        code: "HKCategoryTypeIdentifierSleepAnalysis",
        display: "Sleep analysis information"
    },
    "HKCategoryTypeIdentifierSleepChanges":{
        code: "HKCategoryTypeIdentifierSleepChanges",
        display: "Sleep changes"
    },
    "HKCategoryTypeIdentifierSoreThroat":{
        code: "HKCategoryTypeIdentifierSoreThroat",
        display: "Sore Throat"
    },
    "HKCategoryTypeIdentifierToothbrushingEvent":{
        code: "HKCategoryTypeIdentifierToothbrushingEvent",
        display: "Toothbrushing events"
    },
    "HKCategoryTypeIdentifierVaginalDryness":{
        code: "HKCategoryTypeIdentifierVaginalDryness",
        display: "Vaginal dryness"
    },
    "HKCategoryTypeIdentifierVomiting":{
        code: "HKCategoryTypeIdentifierVomiting",
        display: "Vomitting"
    },
    "HKCategoryTypeIdentifierWheezing":{
        code: "HKCategoryTypeIdentifierWheezing",
        display: "Wheezing"
    },
    "HKQuantityTypeIdentifierBloodAlcoholContent":{
        code: "HKQuantityTypeIdentifierBloodAlcoholContent",
        display: "Blood alcohol content"
    },
    "HKQuantityTypeIdentifierBloodPressureDiastolic":{
        code: "HKQuantityTypeIdentifierBloodPressureDiastolic",
        display: "Diastolic blood pressure"
    },
    "HKQuantityTypeIdentifierBloodPressureSystolic":{
        code: "HKQuantityTypeIdentifierBloodPressureSystolic",
        display: "Systolic blood pressure"
    },
    "HKQuantityTypeIdentifierBodyMassIndex":{
        code: "HKQuantityTypeIdentifierBodyMassIndex",
        display: "Body mass index"
    },
    "HKQuantityTypeIdentifierDietaryBiotin":{
        code: "HKQuantityTypeIdentifierDietaryBiotin",
        display: "Biotin (vitamin B7) consumed"
    },
    "HKQuantityTypeIdentifierDietaryCaffeine":{
        code: "HKQuantityTypeIdentifierDietaryCaffeine",
        display: "Caffeine consumed"
    },
    "HKQuantityTypeIdentifierDietaryCalcium":{
        code: "HKQuantityTypeIdentifierDietaryCalcium",
        display: "Calcium consumed"
    },
    "HKQuantityTypeIdentifierDietaryCarbohydrates":{
        code: "HKQuantityTypeIdentifierDietaryCarbohydrates",
        display: "Carbohydrates consumed"
    },
    "HKQuantityTypeIdentifierDietaryChloride":{
        code: "HKQuantityTypeIdentifierDietaryChloride",
        display: "Chloride consumed"
    },
    "HKQuantityTypeIdentifierDietaryCholesterol":{
        code: "HKQuantityTypeIdentifierDietaryCholesterol",
        display: "Cholesterol consumed"
    },
    "HKQuantityTypeIdentifierDietaryChromium":{
        code: "HKQuantityTypeIdentifierDietaryChromium",
        display: "Chromium consumed"
    },
    "HKQuantityTypeIdentifierDietaryCopper":{
        code: "HKQuantityTypeIdentifierDietaryCopper",
        display: "Copper consumed"
    },
    "HKQuantityTypeIdentifierDietaryEnergyConsumed":{
        code: "HKQuantityTypeIdentifierDietaryEnergyConsumed",
        display: "Energy consumed"
    },
    "HKQuantityTypeIdentifierDietaryFatMonounsaturated":{
        code: "HKQuantityTypeIdentifierDietaryFatMonounsaturated",
        display: "Monounsaturated fat consumed"
    },
    "HKQuantityTypeIdentifierDietaryFatPolyunsaturated":{
        code: "HKQuantityTypeIdentifierDietaryFatPolyunsaturated",
        display: "Polyunsaturated fat consumed"
    },
    "HKQuantityTypeIdentifierDietaryFatSaturated":{
        code: "HKQuantityTypeIdentifierDietaryFatSaturated",
        display: "Saturated fat consumed"
    },
    "HKQuantityTypeIdentifierDietaryFatTotal":{
        code: "HKQuantityTypeIdentifierDietaryFatTotal",
        display: "Total amount of fat consumed"
    },
    "HKQuantityTypeIdentifierDietaryFiber":{
        code: "HKQuantityTypeIdentifierDietaryFiber",
        display: "Fiber consumed"
    },
    "HKQuantityTypeIdentifierDietaryFolate":{
        code: "HKQuantityTypeIdentifierDietaryFolate",
        display: "Folate (folic acid) consumed"
    },
    "HKQuantityTypeIdentifierDietaryIodine":{
        code: "HKQuantityTypeIdentifierDietaryIodine",
        display: "Iodine consumed"
    },
    "HKQuantityTypeIdentifierDietaryIron":{
        code: "HKQuantityTypeIdentifierDietaryIron",
        display: "Iron consumed"
    },
    "HKQuantityTypeIdentifierDietaryMagnesium":{
        code: "HKQuantityTypeIdentifierDietaryMagnesium",
        display: "Magnesium consumed"
    },
    "HKQuantityTypeIdentifierDietaryManganese":{
        code: "HKQuantityTypeIdentifierDietaryManganese",
        display: "Manganese consumed"
    },
    "HKQuantityTypeIdentifierDietaryMolybdenum":{
        code: "HKQuantityTypeIdentifierDietaryMolybdenum",
        display: "Molybdenum consumed"
    },
    "HKQuantityTypeIdentifierDietaryNiacin":{
        code: "HKQuantityTypeIdentifierDietaryNiacin",
        display: "Niacin (vitamin B3) consumed"
    },
    "HKQuantityTypeIdentifierDietaryPantothenicAcid":{
        code: "HKQuantityTypeIdentifierDietaryPantothenicAcid",
        display: "Pantothenic acid (vitamin B5) consumed"
    },
    "HKQuantityTypeIdentifierDietaryPhosphorus":{
        code: "HKQuantityTypeIdentifierDietaryPhosphorus",
        display: "Phosphorus consumed"
    },
    "HKQuantityTypeIdentifierDietaryPotassium":{
        code: "HKQuantityTypeIdentifierDietaryPotassium",
        display: "Potassium consume"
    },
    "HKQuantityTypeIdentifierDietaryProtein":{
        code: "HKQuantityTypeIdentifierDietaryProtein",
        display: "Protein consumed"
    },
    "HKQuantityTypeIdentifierDietaryRiboflavin":{
        code: "HKQuantityTypeIdentifierDietaryRiboflavin",
        display: "Riboflavin (vitamin B2) consumed"
    },
    "HKQuantityTypeIdentifierDietarySelenium":{
        code: "HKQuantityTypeIdentifierDietarySelenium",
        display: "Selenium consumed"
    },
    "HKQuantityTypeIdentifierDietarySodium":{
        code: "HKQuantityTypeIdentifierDietarySodium",
        display: "Sodium consumed"
    },
    "HKQuantityTypeIdentifierDietarySugar":{
        code: "HKQuantityTypeIdentifierDietarySugar",
        display: "Sugar consumed"
    },
    "HKQuantityTypeIdentifierDietaryThiamin":{
        code: "HKQuantityTypeIdentifierDietaryThiamin",
        display: "Thiamin (vitamin B1) consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminA":{
        code: "HKQuantityTypeIdentifierDietaryVitaminA",
        display: "Vitamin A consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminB12":{
        code: "HKQuantityTypeIdentifierDietaryVitaminB12",
        display: "Cyanocobalamin (vitamin B12) consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminB6":{
        code: "HKQuantityTypeIdentifierDietaryVitaminB6",
        display: "Pyridoxine (vitamin B6) consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminC":{
        code: "HKQuantityTypeIdentifierDietaryVitaminC",
        display: "Vitamin C consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminD":{
        code: "HKQuantityTypeIdentifierDietaryVitaminD",
        display: "Vitamin D consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminE":{
        code: "HKQuantityTypeIdentifierDietaryVitaminE",
        display: "vitamin E consumed"
    },
    "HKQuantityTypeIdentifierDietaryVitaminK":{
        code: "HKQuantityTypeIdentifierDietaryVitaminK",
        display: "Vitamin K consumed"
    },
    "HKQuantityTypeIdentifierDietaryWater":{
        code: "HKQuantityTypeIdentifierDietaryWater",
        display: "Water consumed"
    },
    "HKQuantityTypeIdentifierDietaryZinc":{
        code: "HKQuantityTypeIdentifierDietaryZinc",
        display: "Zinc consumed"
    },
    "HKQuantityTypeIdentifierDistanceCycling":{
        code: "HKQuantityTypeIdentifierDistanceCycling",
        display: "Distance moved by cycling"
    },
    "HKQuantityTypeIdentifierDistanceDownhillSnowSports":{
        code: "HKQuantityTypeIdentifierDistanceDownhillSnowSports",
        display: "Distance traveled while skiing or snowboarding"
    },
    "HKQuantityTypeIdentifierDistanceSwimming":{
        code: "HKQuantityTypeIdentifierDistanceSwimming",
        display: "Distance moved while swimming"
    },
    "HKQuantityTypeIdentifierDistanceWalkingRunning":{
        code: "HKQuantityTypeIdentifierDistanceWalkingRunning",
        display: "Distance moved by walking or running"
    },
    "HKQuantityTypeIdentifierDistanceWheelchair":{
        code: "HKQuantityTypeIdentifierDistanceWheelchair",
        display: "Distance moved using a wheelchair"
    },
    "HKQuantityTypeIdentifierElectrodermalActivity":{
        code: "HKQuantityTypeIdentifierElectrodermalActivity",
        display: "Electrodermal activity"
    },
    "HKQuantityTypeIdentifierFlightsClimbed":{
        code: "HKQuantityTypeIdentifierFlightsClimbed",
        display: "Number flights of stairs climbed"
    },
    "HKQuantityTypeIdentifierForcedExpiratoryVolume1":{
        code: "HKQuantityTypeIdentifierForcedExpiratoryVolume1",
        display: "Amount of air that can be forcibly exhaled from the lungs during the first second of a forced exhalation"
    },
    "HKQuantityTypeIdentifierForcedVitalCapacity":{
        code: "HKQuantityTypeIdentifierForcedVitalCapacity",
        display: "Amount of air that can be forcibly exhaled from the lungs after taking the deepest breath possible"
    },
    "HKQuantityTypeIdentifierHeartRateVariabilitySDNN":{
        code: "HKQuantityTypeIdentifierHeartRateVariabilitySDNN",
        display: "Standard deviation of heartbeat intervals"
    },
    "HKQuantityTypeIdentifierInhalerUsage":{
        code: "HKQuantityTypeIdentifierInhalerUsage",
        display: "Number of puffs taken from the inhaler"
    },
    "HKQuantityTypeIdentifierInsulinDelivery":{
        code: "HKQuantityTypeIdentifierInsulinDelivery",
        display: "Insulin delivered"
    },
    "HKQuantityTypeIdentifierLeanBodyMass":{
        code: "HKQuantityTypeIdentifierLeanBodyMass",
        display: "Lean body mass"
    },
    "HKQuantityTypeIdentifierNumberOfTimesFallen":{
        code: "HKQuantityTypeIdentifierNumberOfTimesFallen",
        display: "Number of times it has fallen"
    },
    "HKQuantityTypeIdentifierPeakExpiratoryFlowRate":{
        code: "HKQuantityTypeIdentifierPeakExpiratoryFlowRate",
        display: "Maximum flow rate generated during a forceful exhalation"
    },
    "HKQuantityTypeIdentifierPeripheralPerfusionIndex":{
        code: "HKQuantityTypeIdentifierPeripheralPerfusionIndex",
        display: "Peripheral perfusion index"
    },
    "HKQuantityTypeIdentifierPushCount":{
        code: "HKQuantityTypeIdentifierPushCount",
        display: "Number of pushes made in a wheelchair"
    },
    "HKQuantityTypeIdentifierRestingHeartRate":{
        code: "HKQuantityTypeIdentifierRestingHeartRate",
        display: "Resting heart rate"
    },
    "HKQuantityTypeIdentifierSixMinuteWalkTestDistance":{
        code: "HKQuantityTypeIdentifierSixMinuteWalkTestDistance",
        display: "Distance that can be walked during a six-minute walk test"
    },
    "HKQuantityTypeIdentifierStairAscentSpeed":{
        code: "HKQuantityTypeIdentifierStairAscentSpeed",
        display: "Speed while climbing a flight of stairs"
    },
    "HKQuantityTypeIdentifierStairDescentSpeed":{
        code: "HKQuantityTypeIdentifierStairDescentSpeed",
        display: "Speed while descending a flight of stairs"
    },
    "HKQuantityTypeIdentifierSwimmingStrokeCount":{
        code: "HKQuantityTypeIdentifierSwimmingStrokeCount",
        display: "Number of strokes performed while swimming"
    },
    "HKQuantityTypeIdentifierUVExposure":{
        code: "HKQuantityTypeIdentifierUVExposure",
        display: "Exposure to UV radiation"
    },
    "HKQuantityTypeIdentifierVO2Max":{
        code: "HKQuantityTypeIdentifierVO2Max",
        display: "Maximal oxygen consumption during exercise"
    },
    "HKQuantityTypeIdentifierWaistCircumference":{
        code: "HKQuantityTypeIdentifierWaistCircumference",
        display: "Waist circumference"
    },
    "HKQuantityTypeIdentifierWalkingAsymmetryPercentage":{
        code: "HKQuantityTypeIdentifierWalkingAsymmetryPercentage",
        display: "Percentage of steps in which one foot moves at a different speed than the other when walking steadily over flat ground"
    },
    "HKQuantityTypeIdentifierWalkingDoubleSupportPercentage":{
        code: "HKQuantityTypeIdentifierWalkingDoubleSupportPercentage",
        display: "Percentage of time when both of the user’s feet are touching the ground while walking steadily over flat ground"
    },
    "HKQuantityTypeIdentifierWalkingSpeed":{
        code: "HKQuantityTypeIdentifierWalkingSpeed",
        display: "Average speed when walking steadily over flat ground."
    },
    "HKQuantityTypeIdentifierWalkingStepLength":{
        code: "HKQuantityTypeIdentifierWalkingStepLength",
        display: "Average stride length when steadily walking on flat ground"
    },
    "HKCategoryTypeIdentifierAppleStandHour":{
        code: "HKCategoryTypeIdentifierAppleStandHour",
        display: "Number of hours of standing and moving for at least one minute per hour"
    },
    "rr-interval":{
        code: "HKQuantityTypeIdentifierHeartRateVariabilitySDNN",
        display: "Heart rate variability"
    },
    "heart-rate": {
        code: "HKQuantityTypeIdentifierHeartRate",
        display: "Heart rate"
    },
    "step-count":{
        code: "HKQuantityTypeIdentifierStepCount",
        display: "Number of steps"
    },
    "blood-glucose":{
        code: "HKQuantityTypeIdentifierBloodGlucose",
        display: "Glucose Mass/volume in Blood"
    },
    "oxygen-saturation": {
        code: "HKQuantityTypeIdentifierOxygenSaturation",
        display: "Oxygen saturation in Arterial blood by Pulse oximetry"
    },
    "body-weight": {
        code: "HKQuantityTypeIdentifierBodyMass",
        display: "Body weight"
    },
    "body-temperature": {
        code: "HKQuantityTypeIdentifierBodyTemperature",
        display: "Body temperature"
    },
    "respiratory-rate":{
        code: "HKQuantityTypeIdentifierRespiratoryRate",
        display: "Respiratory rate"
    },
    "body-height":{
        code: "HKQuantityTypeIdentifierHeight",
        display: "Body height"
    },
    "blood-presure":{
        code: "HKCorrelationTypeIdentifierBloodPressure",
        display: "Blood pressure panel"
    },
    "diastolic-blood-pressure": {
        code: "HKQuantityTypeIdentifierBloodPressureDiastolic",
        display: "Diastolic blood pressure"
    },
    "systolic-blood-pressure":{
        code: "HKQuantityTypeIdentifierBloodPressureSystolic",
        display: "Systolic blood pressure"
    },
    "calories-burned":{
        code: "HKQuantityTypeIdentifierActiveEnergyBurned",
        display: "Calories burned"
    },
}