/** Dictionary to mapping apple codes into Fhir Format with ionic system*/
exports.dictionaryLonicCodesMap = {
    "HKCharacteristicTypeIdentifierBiologicalSex":{	
        code: "76689-9"	, 
        display:"Sex assigned at birth", 
        system:"http://loinc.org"
    },
    "HKCharacteristicTypeIdentifierBloodType":{	
        code: "882-1"	, 
        display:"ABO and Rh group [Type] in Blood", 
        system:"http://loinc.org"
    },
    "HKCharacteristicTypeIdentifierDateOfBirth":{	
        code: "21112-8"	, 
        display:"Birth Date", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBodyMassIndex":{	
        code: "39156-5"	, 
        display:"Body mass index (BMI) [Ratio]", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBodyFatPercentage":{	
        code: "41982-0"	, 
        display:"Percentage of body fat Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierHeight":{	
        code: "8302-2"	, 
        display:"Body Height", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBodyMass":{	
        code: "29463-7"	, 
        display:"Body Weight", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierLeanBodyMass":{	
        code: "88334-8"	, 
        display:"Lean body weight Calculated", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierWaistCircumference":{	
        code: "8281-8"	, 
        display:"Waist Circumference at umbilicus by US", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierStepCount":{	
        code: "55423-8"	, 
        display:"Number of steps in unspecified time Pedometer", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDistanceWalkingRunning":{	
        code: "5430-3"	, 
        display:"Distance walked in unspecified time Pedometer", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierActiveEnergyBurned":{	
        code: "41981-2"	, 
        display:"Calories burned", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierFlightsClimbed":{	
        code: "55411-3"	, 
        display:"Exercise duration", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierVO2Max":{	
        code: "60842-2"	, 
        display:"Oxygen consumption (VO2)", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierHeartRate":{	
        code: "8867-4"	, 
        display:"Heart rate", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBodyTemperature":{	
        code: "8310-5"	, 
        display:"Body temperature", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBloodPressureSystolic":{	
        code: "8480-6"	, 
        display:"Systolic blood pressure", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBloodPressureDiastolic":{	
        code: "8462-4"	, 
        display:"Diastolic blood pressure", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierRespiratoryRate":{	
        code: "9279-1"	, 
        display:"Respiratory rate", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierRestingHeartRate":{	
        code: "40443-4"	, 
        display:"Heart rate --resting", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierWalkingHeartRateAverage":{	
        code: "89273-7"	, 
        display:"Heart rate --W exercise", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierHeartRateVariabilitySDNN":{	
        code: "80404-7"	, 
        display:"R-R interval.standard deviation (Heart rate variability)", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierOxygenSaturation":{	
        code: "20564-1"	, 
        display:"Oxygen saturation in Blood", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierPeripheralPerfusionIndex":{	
        code: "61006-3"	, 
        display:"Perfusion index Tissue by Pulse oximetry", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBloodGlucose":{	
            code: "2339-0"	, 
            display:"Glucose [Mass/volume] in Blood", 
            system:"http://loinc.org"
        },
    "HKQuantityTypeIdentifierNumberOfTimesFallen":{	
        code: "52552-7"	, 
        display:"Falls in the past year [CMS Assessment]", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierInhalerUsage":{	
        code: "82672-7"	, 
        display:"How often have you used your rescue inhaler or nebulizer medication (such as albuterol) during the past 4 weeks [ACT]", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierBloodAlcoholContent":{	
            code: "5640-8"	, 
            display:"Ethanol [Mass/volume] in Blood", 
            system:"http://loinc.org"
        },
    "HKQuantityTypeIdentifierForcedVitalCapacity":{	
        code: "19870-5"	, 
        display:"Forced vital capacity [Volume] Respiratory system",
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierForcedExpiratoryVolume1":{	
        code: "20150-9"	, 
        display:"FEV1", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierPeakExpiratoryFlowRate":{	
        code: "33452-4"	, 
        display:"Maximum expiratory gas flow Respiratory system airway",
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFatTotal":{	
        code: "9067-0"	, 
        display:"Fat intake Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFatPolyunsaturated":{	
        code: "81036-6"	, 
        display:"Polyunsaturated fat intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFatMonounsaturated":{	
        code: "81899-7"	, 
        display:"Monounsaturated fat intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFatSaturated":{	
        code: "81136-4"	, 
        display:"Saturated fat intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryCholesterol":{	
        code: "81022-6"	, 
        display:"Cholesterol intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietarySodium":{	
        code: "81012-7"	, 
        display:"Sodium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryCarbohydrates":{	
        code: "81953-2"	, 
        display:"Carbohydrate intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFiber":{	
        code: "81057-2"	, 
        display:"Fiber intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryEnergyConsumed":{	
        code: "80494-8"	, 
        display:"Calorie intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryProtein":{	
        code: "82156-1"	, 
        display:"Protein intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminA":{	
        code: "81073-9"	, 
        display:"Vitamin A intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminB6":{	
        code: "81065-5"	, 
        display:"Vitamin B6 intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminB12":{	
        code: "81063-0"	, 
        display:"Vitamin B12 intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminC":{	
        code: "81075-4"	, 
        display:"Vitamin C intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminD":{	
        code: "81930-0"	, 
        display:"Vitamin D intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminE":{	
        code: "81077-0"	, 
        display:"Vitamin E intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryVitaminK":{	
        code: "81079-6"	, 
        display:"Vitamin K intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryCalcium":{	
        code: "80975-6"	, 
        display:"Calcium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryIron":{	
        code: "81083-8"	, 
        display:"Iron intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryThiamin":{	
        code: "81928-4"	, 
        display:"Vitamin B1 (Thiamine) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryRiboflavin":{	
        code: "81071-3"	, 
        display:"Vitamin B2 (Riboflavin) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryNiacin":{	
        code: "81067-1"	, 
        display:"Vitamin B3 (Niacin) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryFolate":{	
        code: "81134-9"	, 
        display:"Vitamin B9 (Folate) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryBiotin":{	
        code: "81018-4"	, 
        display:"Vitamin B7 (Biotin) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryPantothenicAcid":{	
        code: "81069-7"	, 
        display:"Vitamin B5 (Pantothenate) intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryPhosphorus":{	
        code: "81946-6"	, 
        display:"Phosphorus intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryIodine":{	
        code: "81004-4"	, 
        display:"Iodine intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryMagnesium":{	
        code: "81006-9"	, 
        display:"Magnesium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryZinc":{	
        code: "81088-7"	, 
        display:"Zinc intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietarySelenium":{	
        code: "81086-1"	, 
        display:"Selenium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryCopper":{	
        code: "81028-3"	, 
        display:"Copper intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryManganese":{	
        code: "81008-5"	, 
        display:"Manganese intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryChromium":{	
        code: "81024-2"	, 
        display:"Chromium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryMolybdenum":{	
        code: "81085-3"	, 
        display:"Molybdenum intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryChloride":{	
        code: "80983-0"	, 
        display:"Chloride intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryPotassium":{	
        code: "81009-3"	, 
        display:"Potassium intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKQuantityTypeIdentifierDietaryCaffeine":{	
        code: "80490-6"	, 
        display:"Caffeine intake 24 hour Measured", 
        system:"http://loinc.org"
    },
    "HKCategoryTypeIdentifierCervicalMucusQuality":{	
        code: "10570-0"	, 
        display:"Consistency of Cervical mucus", 
        system:"http://loinc.org"
    },
    "HKCategoryTypeIdentifierMenstrualFlow":{	
        code: "3146-8"	, 
        display:"Menstrual status", 
        system:"http://loinc.org"
    },
    'acceleration':{
        code:"80493-0",
        display:"Activity level [Acceleration]",
        system:"http://loinc.org"
      },
      'ambient-temperature':{
          code:"60832-3",
          display:"Room temperature",
          system:"http://loinc.org"
      },
      'blood-glucose':{
          code:"2339-0",
          display:"Glucose Mass/volume in Blood",
          system:"http://loinc.org"
      },
      'blood-presure':{
          code:"85354-9",
          display:"Blood pressure panel with all children optional",
          system:"http://loinc.org"
      },
      'body-fat-percentage':{
          code:"41982-0",
          display:"Percentage of body fat Measured",
          system:"http://loinc.org"
      },
      'body-height':{
          code:"8302-2",
          display:"Body height",
          system:"http://loinc.org"
      },
      'body-mass-index':{
          code:"39156-5",
          display:"Body mass index (BMI) Ratio",
          system:"http://loinc.org"
      },
      'body-temperature':{
          code:"8310-5",
          display:"Body temperature",
          system:"http://loinc.org"
      },
      'body-weight':{
          code:"29463-7",
          display:"Body weight",
          system:"http://loinc.org"
      },
      'breath-carbon-monoxide':{
          code:"251900003",
          display:"Expired carbon monoxide concentration (observable entity)",
          system:"http://snomed.info/id"
      },
      'calories-burned':{
          code:"41981-2",
          display:"Calories burned",
          system:"http://loinc.org"
      },
      'diastolic-blood-pressure':{
          code:"8462-4",
          display:"Diastolic blood pressure",
          system:"http://loinc.org"
      },
      'expiratory-time':{
          code:"60739-0",
          display:"Expiration Time Respiratory system",
          system:"http://loinc.org"
      },
      'geoposition':{
          code:"geoposition",
          display:"Geoposition",
          system:"http://www.fhir.org/guides/omhtofhir/datapoint-type"
      },
      'heart-rate':{
          code:"8867-4",
          display:"Heart rate",
          system:"http://loinc.org"
      },
      'inspiratory-time':{
          code:"60740-8",
          display:"Inspiration Time Respiratory system",
          system:"http://loinc.org"
      },
      'magnetic-force':{
          code:"magnetic-force",
          display:"Magnetic Force Panel",
          system:"http://www.fhir.org/guides/omhtofhir/datapoint-type"
      },
      'medication-adherence-percent':{
          code:"418633004",
          display:"Medication compliance (observable entity)",
          system:"http://snomed.info/id"
      },
      'minute-volume':{
          code:"20139-2",
          display:"Volume expired 1 minute",
          system:"http://loinc.org"
      },
      'minute-moderate-activity':{
          code:"408581006",
          display:"Physical activity target moderate exercise (finding)",
          system:"http://snomed.info/id"
      },
      'orientation':{
          code:"orientation",
          display:"Gyroscope measurement Panel",
          system:"http://www.fhir.org/guides/omhtofhir/datapoint-type"
      },
      'oxygen-saturation':{
          code:"59408-5",
          display:"Oxygen saturation in Arterial blood by Pulse oximetry",
          system:"http://loinc.org"
      },
      'pace':{
          code:"pace",
          display:"Pace",
          system:"http://www.fhir.org/guides/omhtofhir/datapoint-type"
      },
      'physical-activity':{
          code:"68130003",
          display:"Physical activity (observable entity)",
          system:"http://snomed.info/id"
      },
      'respiratory-rate':{
          code:"9279-1",
          display:"Respiratory Rate",
          system:"http://loinc.org"
      },
      'rr-interval':{
          code:"8637-1",
          display:"R-R interval by EKG",
          system:"http://loinc.org"
      },
      'sleep-duration':{
          code:"248263006",
          display:"Duration of sleep (observable entity)",
          system:"http://snomed.info/id"
      },
      'sleep-episode':{
          code:"258158006",
          display:"Sleep, function (observable entity)",
          system:"http://snomed.info/id"
      },
      'speed':{
          code:"C0678536",
          display:"Speed",
          system:"http://ncimeta.nci.nih.gov"
      },
      'step-count':{
          code:"55423-8",
          display:"Number of steps in unspecified time Pedometer",
          system:"http://loinc.org"
      },
      'systolic-blood-pressure':{
          code:"8480-6",
          display:"Systolic blood pressure",
          system:"http://loinc.org"
      },
      'ventilation-cycle-time':{
          code:"250818005",
          display:"Ventilation cycle time (observable entity)",
          system:"http://snomed.info/id"
      }
}