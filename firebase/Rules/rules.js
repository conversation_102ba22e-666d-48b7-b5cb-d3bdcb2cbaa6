rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check user roles
    function hasRole(role) {
      let userDoc = get(/databases/$(database)/documents/users_roles/$(request.auth.uid));
      return userDoc != null && userDoc.data.role == role;
    }

    function isAdmin() {
      let userDoc = get(/databases/$(database)/documents/users_roles/$(request.auth.uid));
      return userDoc != null && (userDoc.data.role == 'admin' || userDoc.data.role == 'superAdmin');
    }

    // Users roles collection
    match /users_roles/{userId} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        isAdmin()
      );
      allow list: if request.auth != null && isAdmin();
      allow write: if request.auth != null && isAdmin();
    }

    match /patients/{patientId} {
      // Base patient profile
      allow read: if request.auth != null && (
        request.auth.uid == patientId || 
        hasRole('practitioner') || 
        isAdmin()
      );
      allow write: if request.auth != null && (
        request.auth.uid == patientId ||
        isAdmin()
      );
      
      // Health readings subcollection
      match /readings/{readingId} {
        allow read: if request.auth != null && (
          request.auth.uid == patientId || 
          hasRole('practitioner') ||
          isAdmin()
        );
        allow write: if request.auth != null && (
          request.auth.uid == patientId ||
          hasRole('practitioner') ||
          isAdmin()
        );
      }
      
      // Daily metrics subcollection
      match /daily_metrics/{date} {
        allow read: if request.auth != null && (
          request.auth.uid == patientId || 
          hasRole('practitioner') ||
          isAdmin()
        );
        allow write: if request.auth != null && (
          request.auth.uid == patientId ||
          hasRole('practitioner') ||
          isAdmin()
        );
      }
    }

    // Practitioners collection
    match /practitioners/{practitionerId} {
      allow read: if request.auth != null && (
        request.auth.uid == practitionerId || 
        isAdmin()
      );
      allow write: if request.auth != null && isAdmin();
    }

    // Allow admins to read all collections
    match /{document=**} {
      allow read: if request.auth != null && isAdmin();
    }
  }
}
