/**
 * Run Patient-Practitioner Linking Tests
 *
 * This script runs the tests for the patient-practitioner linking functionality.
 * It uses a simplified approach that doesn't rely on Jest.
 *
 * Usage:
 * 1. Make sure Firebase emulators are running
 * 2. Run: node scripts/run-linking-tests.js
 */

// Check if Firebase emulators are running
const http = require('http');

function checkEmulators() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      host: '127.0.0.1',
      port: 8080,
      path: '/',
      method: 'GET',
      timeout: 1000
    }, (res) => {
      resolve(true);
    });

    req.on('error', (err) => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function main() {
  console.log('Starting Patient-Practitioner Linking Tests...');

  // Check if emulators are running
  const emulatorsRunning = await checkEmulators();
  if (!emulatorsRunning) {
    console.error('\x1b[31mError: Firebase emulators are not running.\x1b[0m');
    console.error('\x1b[33mPlease start the emulators with: firebase emulators:start\x1b[0m');
    process.exit(1);
  }

  console.log('\x1b[32mFirebase emulators are running!\x1b[0m');
  console.log('---------------------------------------------');

  try {
    // Import the test module
    const { runTests } = require('../tests/patient-practitioner-linking.test');

    // Run the tests
    await runTests();
    console.log('Tests completed.');
    process.exit(0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the main function
main();
