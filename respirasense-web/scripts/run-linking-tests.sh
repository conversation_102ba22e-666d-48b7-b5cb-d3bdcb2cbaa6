#!/bin/bash

# Run Patient-Practitioner Linking Tests
# This script runs the automated tests for the patient-practitioner linking functionality

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Patient-Practitioner Linking Tests...${NC}"

# Check if Firebase emulators are running
if ! curl -s http://localhost:8080 > /dev/null; then
  echo -e "${RED}Error: Firebase emulators are not running.${NC}"
  echo -e "Please start the emulators with: ${YELLOW}firebase emulators:start${NC}"
  exit 1
fi

# Setup test data
echo -e "${YELLOW}Setting up test data...${NC}"
node setup-linking-test-data.js

# Run the tests
echo -e "${YELLOW}Running automated tests...${NC}"
node run-linking-tests.js

# Check if tests passed
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Automated tests passed!${NC}"
  echo -e "${YELLOW}Now you can run the manual tests as described in:${NC}"
  echo -e "${YELLOW}tests/manual-linking-test.md${NC}"
else
  echo -e "${RED}Automated tests failed.${NC}"
  echo -e "${YELLOW}Please check the test output for details.${NC}"
  exit 1
fi

echo -e "${GREEN}Test process complete!${NC}"
