/**
 * Setup Test Data for Patient-Practitioner Linking Tests
 *
 * This script creates test users and data in Firebase for testing the
 * patient-practitioner linking functionality.
 *
 * Usage:
 * 1. Make sure Firebase emulators are running
 * 2. Run: node scripts/setup-linking-test-data.js
 */

const admin = require('firebase-admin');

// Set up environment variables for emulator
process.env.FIRESTORE_EMULATOR_HOST = '127.0.0.1:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';

// Initialize Firebase Admin SDK without credentials for emulator use
admin.initializeApp({
  projectId: 'respirasense-test'
});

console.log('Using Firebase emulators');

// Get Firestore and Auth instances
const db = admin.firestore();
const auth = admin.auth();

// Test user data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'TestPassword123',
    displayName: 'Test Practitioner',
    role: 'practitioner'
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123',
    displayName: 'Test Patient',
    role: 'patient'
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123',
    displayName: 'Test Admin',
    role: 'admin'
  }
];

/**
 * Create test users in Firebase Auth
 */
async function createTestUsers() {
  console.log('Creating test users...');

  // Generate random UIDs for test users if Auth emulator is not available
  const userIds = {
    '<EMAIL>': 'practitioner-' + Math.random().toString(36).substring(2, 15),
    '<EMAIL>': 'patient-' + Math.random().toString(36).substring(2, 15),
    '<EMAIL>': 'admin-' + Math.random().toString(36).substring(2, 15)
  };

  for (const user of testUsers) {
    try {
      let userId;

      // Try to create user in Firebase Auth
      try {
        // Check if user already exists
        try {
          const userRecord = await auth.getUserByEmail(user.email);
          console.log(`User ${user.email} already exists with ID: ${userRecord.uid}`);
          userId = userRecord.uid;
        } catch (error) {
          // User doesn't exist, proceed with creation
          const userRecord = await auth.createUser({
            email: user.email,
            password: user.password,
            displayName: user.displayName,
            emailVerified: true
          });

          console.log(`Created user ${user.email} with ID: ${userRecord.uid}`);
          userId = userRecord.uid;
        }
      } catch (error) {
        // Auth emulator might not be available, use generated ID
        console.log(`Auth emulator not available, using generated ID for ${user.email}`);
        userId = userIds[user.email];
      }

      // Create user document in Firestore
      await db.collection('users_roles').doc(userId).set({
        email: user.email,
        name: user.displayName,
        role: user.role,
        status: 'active',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`Created user_roles document for ${user.email} with ID: ${userId}`);

      // Create additional documents based on role
      if (user.role === 'patient') {
        await db.collection('patients').doc(userId).set({
          email: user.email,
          name: user.displayName,
          lastUpdate: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Created patient document for ${user.email}`);

        // Create profile subcollection
        await db.collection('patients').doc(userId)
          .collection('profile').doc('details').set({
            name: user.displayName,
            email: user.email,
            age: 45,
            height: 175,
            weight: 70,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

        console.log(`Created patient profile for ${user.email}`);
      } else if (user.role === 'practitioner') {
        await db.collection('practitioners').doc(userId).set({
          email: user.email,
          name: user.displayName,
          specialization: 'General Practice',
          licenseNumber: 'LIC12345',
          status: 'active',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          lastUpdate: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Created practitioner document for ${user.email}`);
      }

      // Store the user ID for later use
      userIds[user.email] = userId;
    } catch (error) {
      console.error(`Error creating user ${user.email}:`, error);
    }
  }

  return userIds;
}

/**
 * Create sample linking requests
 */
async function createSampleRequests(userIds) {
  console.log('Creating sample linking requests...');

  try {
    let practitionerId;
    let patientId;

    // Try to get user IDs from Auth
    try {
      const practitionerRecord = await auth.getUserByEmail('<EMAIL>');
      const patientRecord = await auth.getUserByEmail('<EMAIL>');

      practitionerId = practitionerRecord.uid;
      patientId = patientRecord.uid;
    } catch (error) {
      // Auth emulator might not be available, use the IDs from createTestUsers
      console.log('Auth emulator not available, using generated IDs for requests');
      practitionerId = userIds['<EMAIL>'];
      patientId = userIds['<EMAIL>'];
    }

    console.log(`Using practitioner ID: ${practitionerId}`);
    console.log(`Using patient ID: ${patientId}`);

    // Create a practitioner-initiated request
    await db.collection('patient_requests').add({
      patientId: patientId,
      patientEmail: '<EMAIL>',
      practitionerId: practitionerId,
      practitionerName: 'Test Practitioner',
      requestType: 'practitioner_initiated',
      status: 'pending',
      message: 'Please approve this linking request for testing.',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('Created practitioner-initiated request');

    // Create a patient-initiated request
    await db.collection('patient_requests').add({
      patientId: patientId,
      patientEmail: '<EMAIL>',
      patientName: 'Test Patient',
      practitionerId: practitionerId,
      practitionerEmail: '<EMAIL>',
      requestType: 'patient_initiated',
      status: 'pending',
      message: 'I would like to connect with you as my practitioner.',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('Created patient-initiated request');

    return { practitionerId, patientId };
  } catch (error) {
    console.error('Error creating sample requests:', error);
    return null;
  }
}

/**
 * Main function to run the script
 */
async function main() {
  try {
    const userIds = await createTestUsers();
    const requestIds = await createSampleRequests(userIds);

    // Write the IDs to a file for the test script to use
    const fs = require('fs');
    const testData = {
      userIds,
      requestIds
    };

    try {
      fs.writeFileSync('./test-data.json', JSON.stringify(testData, null, 2));
      console.log('Test data saved to test-data.json');
    } catch (err) {
      console.log('Could not save test data to file:', err);
    }

    console.log('Test data setup complete!');
  } catch (error) {
    console.error('Error setting up test data:', error);
  } finally {
    // Terminate the Firebase Admin app
    try {
      await admin.app().delete();
    } catch (err) {
      // Ignore errors when terminating the app
    }
  }
}

// Run the script
main();
