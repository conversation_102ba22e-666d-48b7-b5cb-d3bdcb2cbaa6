/**
 * Patient-Practitioner Linking Test Script
 *
 * This script tests the bidirectional linking functionality between patients and practitioners.
 * It covers both scenarios:
 * 1. Practitioner initiates linking with a patient
 * 2. Patient initiates linking with a practitioner
 *
 * Prerequisites:
 * - Firebase emulators running (auth, firestore)
 * - Test users created in the auth emulator
 */

// Import Firebase Admin SDK
const admin = require('firebase-admin');

// Set up environment variables for emulator
process.env.FIRESTORE_EMULATOR_HOST = '127.0.0.1:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';

// Initialize Firebase Admin SDK without credentials for emulator use
admin.initializeApp({
  projectId: 'respirasense-test'
});

// Get Firestore and Auth instances
const db = admin.firestore();
const auth = admin.auth();

// Test user credentials
const PRACTITIONER_EMAIL = '<EMAIL>';
const PATIENT_EMAIL = '<EMAIL>';

// Load test data if available
let testData = {};
try {
  const fs = require('fs');
  const testDataFile = fs.readFileSync('./test-data.json', 'utf8');
  testData = JSON.parse(testDataFile);
  console.log('Loaded test data from file');
} catch (err) {
  console.log('Could not load test data from file, will generate IDs during tests');
}

// Test data
let practitionerId = testData.requestIds?.practitionerId;
let patientId = testData.requestIds?.patientId;
let requestId;

/**
 * Helper function to get a user by email
 */
async function getUserByEmail(email) {
  try {
    // Try to get user from Auth
    try {
      const userRecord = await auth.getUserByEmail(email);
      return userRecord;
    } catch (error) {
      // Auth emulator might not be available, use the IDs from test data
      if (email === PRACTITIONER_EMAIL && testData.userIds && testData.userIds[email]) {
        return { uid: testData.userIds[email] };
      } else if (email === PATIENT_EMAIL && testData.userIds && testData.userIds[email]) {
        return { uid: testData.userIds[email] };
      } else {
        // Generate a random ID if not found
        const randomId = email.includes('practitioner')
          ? 'practitioner-' + Math.random().toString(36).substring(2, 15)
          : 'patient-' + Math.random().toString(36).substring(2, 15);
        return { uid: randomId };
      }
    }
  } catch (error) {
    console.error(`Error getting user by email ${email}:`, error);
    return null;
  }
}

/**
 * Helper function to create a linking request
 */
async function createLinkingRequest(patientEmail, practitionerId, requestType) {
  const requestData = {
    patientEmail,
    practitionerId,
    practitionerName: 'Test Practitioner',
    requestType,
    status: 'pending',
    createdAt: admin.firestore.FieldValue.serverTimestamp()
  };

  const requestRef = await db.collection('patient_requests').add(requestData);
  return requestRef.id;
}

/**
 * Helper function to approve a linking request
 */
async function approveLinkingRequest(requestId, patientId, practitionerId) {
  // Update request status
  await db.doc(`patient_requests/${requestId}`).update({
    status: 'approved',
    responseAt: admin.firestore.FieldValue.serverTimestamp()
  });

  // Update patient document
  await db.doc(`patients/${patientId}`).update({
    practitionerId,
    practitionerAssignedDate: admin.firestore.FieldValue.serverTimestamp()
  });

  // Add patient to practitioner's patients subcollection
  await db.doc(`practitioners/${practitionerId}/patients/${patientId}`).set({
    name: 'Test Patient',
    email: PATIENT_EMAIL,
    lastUpdate: admin.firestore.FieldValue.serverTimestamp()
  });
}

/**
 * Helper function to reject a linking request
 */
async function rejectLinkingRequest(requestId) {
  // Update request status
  await db.doc(`patient_requests/${requestId}`).update({
    status: 'rejected',
    responseAt: admin.firestore.FieldValue.serverTimestamp()
  });

  return true;
}

/**
 * Helper function to cancel a linking request
 */
async function cancelLinkingRequest(requestId) {
  // Update request status
  await db.doc(`patient_requests/${requestId}`).update({
    status: 'cancelled',
    responseAt: admin.firestore.FieldValue.serverTimestamp()
  });

  return true;
}

// Create a simple test suite that can be run with Node.js
// This is a simplified test that doesn't rely on Jest's test runner
// It can be run with a simple Node.js script

console.log('Starting Patient-Practitioner Linking Tests');

// Test functions
async function testPractitionerInitiatedLinking() {
    console.log('\n--- Testing Practitioner-Initiated Linking ---');

    try {
      // 1. Get practitioner user
      console.log('1. Getting practitioner user...');
      const practitioner = await getUserByEmail(PRACTITIONER_EMAIL);
      if (!practitioner) {
        console.log('   ❌ Practitioner user not found');
        return false;
      }
      practitionerId = practitioner.uid;
      console.log(`   Found practitioner: ${practitionerId}`);

      // 2. Create linking request
      console.log('2. Creating linking request...');
      requestId = await createLinkingRequest(PATIENT_EMAIL, practitionerId, 'practitioner_initiated');
      console.log(`   Created request: ${requestId}`);

      // 3. Verify request was created
      console.log('3. Verifying request...');
      const requestDoc = await db.doc(`patient_requests/${requestId}`).get();
      if (requestDoc.exists && requestDoc.data().status === 'pending') {
        console.log('   ✅ Request created successfully');
      } else {
        console.log('   ❌ Failed to create request');
        return false;
      }

      // 4. Get patient user
      console.log('4. Getting patient user...');
      const patient = await getUserByEmail(PATIENT_EMAIL);
      if (!patient) {
        console.log('   ❌ Patient user not found');
        return false;
      }
      patientId = patient.uid;
      console.log(`   Found patient: ${patientId}`);

      // 5. Approve the request
      console.log('5. Approving request...');
      await approveLinkingRequest(requestId, patientId, practitionerId);
      console.log('   Request approved');

      // 6. Verify linking
      console.log('6. Verifying linking...');
      const patientDoc = await db.doc(`patients/${patientId}`).get();
      if (patientDoc.exists && patientDoc.data().practitionerId === practitionerId) {
        console.log('   ✅ Patient linked to practitioner successfully');
        return true;
      } else {
        console.log('   ❌ Failed to link patient to practitioner');
        return false;
      }
    } catch (error) {
      console.error('Error in practitioner-initiated linking test:', error);
      return false;
    }
  }

  async function testPatientInitiatedLinking() {
    console.log('\n--- Testing Patient-Initiated Linking ---');

    try {
      // 1. Get patient user
      console.log('1. Getting patient user...');
      const patient = await getUserByEmail(PATIENT_EMAIL);
      if (!patient) {
        console.log('   ❌ Patient user not found');
        return false;
      }
      patientId = patient.uid;
      console.log(`   Found patient: ${patientId}`);

      // 2. Create linking request
      console.log('2. Creating linking request...');
      requestId = await createLinkingRequest(PATIENT_EMAIL, practitionerId, 'patient_initiated');
      console.log(`   Created request: ${requestId}`);

      // 3. Update request with patient details
      console.log('3. Updating request with patient details...');
      await db.doc(`patient_requests/${requestId}`).update({
        patientId,
        patientName: 'Test Patient'
      });
      console.log('   Request updated');

      // 4. Get practitioner user
      console.log('4. Getting practitioner user...');
      const practitioner = await getUserByEmail(PRACTITIONER_EMAIL);
      if (!practitioner) {
        console.log('   ❌ Practitioner user not found');
        return false;
      }
      practitionerId = practitioner.uid;
      console.log(`   Found practitioner: ${practitionerId}`);

      // 5. Approve the request
      console.log('5. Approving request...');
      await approveLinkingRequest(requestId, patientId, practitionerId);
      console.log('   Request approved');

      // 6. Verify linking
      console.log('6. Verifying linking...');
      const practitionerPatientDoc = await db.doc(`practitioners/${practitionerId}/patients/${patientId}`).get();
      if (practitionerPatientDoc.exists) {
        console.log('   ✅ Patient added to practitioner\'s patients collection');
        return true;
      } else {
        console.log('   ❌ Failed to add patient to practitioner\'s patients collection');
        return false;
      }
    } catch (error) {
      console.error('Error in patient-initiated linking test:', error);
      return false;
    }
  }

  async function testRequestRejection() {
    console.log('\n--- Testing Request Rejection ---');

    try {
      // 1. Get practitioner user
      console.log('1. Getting practitioner user...');
      const practitioner = await getUserByEmail(PRACTITIONER_EMAIL);
      if (!practitioner) {
        console.log('   ❌ Practitioner user not found');
        return false;
      }
      practitionerId = practitioner.uid;
      console.log(`   Found practitioner: ${practitionerId}`);

      // 2. Get patient user
      console.log('2. Getting patient user...');
      const patient = await getUserByEmail(PATIENT_EMAIL);
      if (!patient) {
        console.log('   ❌ Patient user not found');
        return false;
      }
      patientId = patient.uid;
      console.log(`   Found patient: ${patientId}`);

      // 3. Create linking request
      console.log('3. Creating linking request...');
      requestId = await createLinkingRequest(PATIENT_EMAIL, practitionerId, 'practitioner_initiated');
      console.log(`   Created request: ${requestId}`);

      // 4. Reject the request
      console.log('4. Rejecting request...');
      await rejectLinkingRequest(requestId);
      console.log('   Request rejected');

      // 5. Verify rejection
      console.log('5. Verifying rejection...');
      const requestDoc = await db.doc(`patient_requests/${requestId}`).get();
      if (requestDoc.exists && requestDoc.data().status === 'rejected') {
        console.log('   ✅ Request was successfully rejected');
        return true;
      } else {
        console.log('   ❌ Failed to reject request');
        return false;
      }
    } catch (error) {
      console.error('Error in request rejection test:', error);
      return false;
    }
  }

  async function testRequestCancellation() {
    console.log('\n--- Testing Request Cancellation ---');

    try {
      // 1. Get patient user
      console.log('1. Getting patient user...');
      const patient = await getUserByEmail(PATIENT_EMAIL);
      if (!patient) {
        console.log('   ❌ Patient user not found');
        return false;
      }
      patientId = patient.uid;
      console.log(`   Found patient: ${patientId}`);

      // 2. Get practitioner user
      console.log('2. Getting practitioner user...');
      const practitioner = await getUserByEmail(PRACTITIONER_EMAIL);
      if (!practitioner) {
        console.log('   ❌ Practitioner user not found');
        return false;
      }
      practitionerId = practitioner.uid;
      console.log(`   Found practitioner: ${practitionerId}`);

      // 3. Create linking request
      console.log('3. Creating linking request...');
      requestId = await createLinkingRequest(PATIENT_EMAIL, practitionerId, 'patient_initiated');
      console.log(`   Created request: ${requestId}`);

      // 4. Update request with patient details
      console.log('4. Updating request with patient details...');
      await db.doc(`patient_requests/${requestId}`).update({
        patientId,
        patientName: 'Test Patient'
      });
      console.log('   Request updated');

      // 5. Cancel the request
      console.log('5. Cancelling request...');
      await cancelLinkingRequest(requestId);
      console.log('   Request cancelled');

      // 6. Verify cancellation
      console.log('6. Verifying cancellation...');
      const requestDoc = await db.doc(`patient_requests/${requestId}`).get();
      if (requestDoc.exists && requestDoc.data().status === 'cancelled') {
        console.log('   ✅ Request was successfully cancelled');
        return true;
      } else {
        console.log('   ❌ Failed to cancel request');
        return false;
      }
    } catch (error) {
      console.error('Error in request cancellation test:', error);
      return false;
    }
  }

  // Run the tests
  async function runTests() {
    const practitionerInitiatedResult = await testPractitionerInitiatedLinking();
    const patientInitiatedResult = await testPatientInitiatedLinking();
    const requestRejectionResult = await testRequestRejection();
    const requestCancellationResult = await testRequestCancellation();

    console.log('\n--- Test Results ---');
    console.log(`Practitioner-Initiated Linking: ${practitionerInitiatedResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Patient-Initiated Linking: ${patientInitiatedResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Request Rejection: ${requestRejectionResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Request Cancellation: ${requestCancellationResult ? '✅ PASS' : '❌ FAIL'}`);

    if (practitionerInitiatedResult && patientInitiatedResult && requestRejectionResult && requestCancellationResult) {
      console.log('\n✅ All tests passed!');
    } else {
      console.log('\n❌ Some tests failed.');
    }
  }

// Export the test runner
module.exports = { runTests };
