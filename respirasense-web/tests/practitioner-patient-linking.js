/**
 * Test script for practitioner-patient linking functionality
 * 
 * This script tests the following functionality:
 * 1. Creating a practitioner account
 * 2. Creating a patient account
 * 3. Linking a patient to a practitioner
 * 4. Removing a patient from a practitioner
 */

import { db, auth } from '../src/plugins/firebase/firebase';
import { 
  doc, 
  getDoc, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword 
} from 'firebase/auth';
import { 
  assignPatientToPractitioner, 
  removePatientFromPractitioner 
} from '../src/utils/firestore-helpers';

// Test configuration
const config = {
  practitioner: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Practitioner',
    specialty: 'General Practitioner'
  },
  patient: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Patient'
  }
};

// Helper function to create a test practitioner
async function createTestPractitioner() {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      config.practitioner.email,
      config.practitioner.password
    );
    
    const userId = userCredential.user.uid;
    const now = new Date().toISOString();
    
    // Create user_roles document
    await setDoc(doc(db, 'users_roles', userId), {
      email: config.practitioner.email,
      name: config.practitioner.name,
      role: 'practitioner',
      specialty: config.practitioner.specialty,
      status: 'active',
      created_at: now,
      updatedAt: now,
      last_login: now,
      provider: 'password'
    });
    
    // Create practitioner document
    await setDoc(doc(db, 'practitioners', userId), {
      name: config.practitioner.name,
      email: config.practitioner.email,
      specialty: config.practitioner.specialty,
      status: 'active',
      createdAt: now,
      lastUpdate: now,
      patientCount: 0
    });
    
    console.log('✅ Test practitioner created:', userId);
    return userId;
  } catch (error) {
    console.error('❌ Error creating test practitioner:', error);
    throw error;
  }
}

// Helper function to create a test patient
async function createTestPatient() {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      config.patient.email,
      config.patient.password
    );
    
    const userId = userCredential.user.uid;
    const now = new Date().toISOString();
    
    // Create user_roles document
    await setDoc(doc(db, 'users_roles', userId), {
      email: config.patient.email,
      name: config.patient.name,
      role: 'patient',
      status: 'active',
      created_at: now,
      updatedAt: now,
      last_login: now,
      provider: 'password'
    });
    
    // Create patient document
    await setDoc(doc(db, 'patients', userId), {
      name: config.patient.name,
      email: config.patient.email,
      createdAt: now,
      lastUpdate: now
    });
    
    console.log('✅ Test patient created:', userId);
    return userId;
  } catch (error) {
    console.error('❌ Error creating test patient:', error);
    throw error;
  }
}

// Test linking a patient to a practitioner
async function testLinkPatientToPractitioner(patientId, practitionerId) {
  try {
    await assignPatientToPractitioner(patientId, practitionerId);
    
    // Verify the patient has the practitionerId
    const patientRef = doc(db, 'patients', patientId);
    const patientDoc = await getDoc(patientRef);
    
    if (!patientDoc.exists()) {
      throw new Error('Patient document not found');
    }
    
    const patientData = patientDoc.data();
    if (patientData.practitionerId !== practitionerId) {
      throw new Error(`Patient has incorrect practitionerId: ${patientData.practitionerId}`);
    }
    
    // Verify the practitioner has the patient in their subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);
    const practitionerPatientDoc = await getDoc(practitionerPatientRef);
    
    if (!practitionerPatientDoc.exists()) {
      throw new Error('Practitioner patient reference not found');
    }
    
    console.log('✅ Patient successfully linked to practitioner');
    return true;
  } catch (error) {
    console.error('❌ Error linking patient to practitioner:', error);
    throw error;
  }
}

// Test removing a patient from a practitioner
async function testRemovePatientFromPractitioner(patientId, practitionerId) {
  try {
    await removePatientFromPractitioner(patientId, practitionerId);
    
    // Verify the patient no longer has the practitionerId
    const patientRef = doc(db, 'patients', patientId);
    const patientDoc = await getDoc(patientRef);
    
    if (!patientDoc.exists()) {
      throw new Error('Patient document not found');
    }
    
    const patientData = patientDoc.data();
    if (patientData.practitionerId !== null) {
      throw new Error(`Patient still has practitionerId: ${patientData.practitionerId}`);
    }
    
    // Verify the practitioner no longer has the patient in their subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);
    const practitionerPatientDoc = await getDoc(practitionerPatientRef);
    
    if (practitionerPatientDoc.exists()) {
      throw new Error('Practitioner patient reference still exists');
    }
    
    console.log('✅ Patient successfully removed from practitioner');
    return true;
  } catch (error) {
    console.error('❌ Error removing patient from practitioner:', error);
    throw error;
  }
}

// Run the tests
async function runTests() {
  try {
    console.log('🧪 Starting practitioner-patient linking tests...');
    
    // Create test users
    const practitionerId = await createTestPractitioner();
    const patientId = await createTestPatient();
    
    // Test linking
    await testLinkPatientToPractitioner(patientId, practitionerId);
    
    // Test removing
    await testRemovePatientFromPractitioner(patientId, practitionerId);
    
    console.log('✅ All tests passed!');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Uncomment to run the tests
// runTests();
