const functions = require('firebase-functions');
const admin = require('firebase-admin');
const cors = require('cors')({origin: true});

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// Import our user document creation function
const { createUserDocument } = require('./createUserDocument');

// Export the function
exports.createUserDocument = createUserDocument;

/**
 * Cloud Function to process patient readings and calculate risk levels
 */
exports.processPatientReading = functions.firestore
  .document('patients/{patientId}/readings/{readingId}')
  .onCreate(async (snapshot, context) => {
    const patientId = context.params.patientId;
    const reading = snapshot.data();

    try {
      // Calculate COPD risk level
      const riskLevel = calculateRiskLevel(reading);

      // Update the reading with risk level
      await snapshot.ref.update({
        riskLevel,
        processedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Update daily metrics
      const dateStr = new Date().toISOString().split('T')[0];
      const dailyMetricsRef = db.doc(`patients/${patientId}/daily_metrics/${dateStr}`);

      await dailyMetricsRef.set({
        respiratoryRate: reading.respiratoryRate,
        oxygenSaturation: reading.oxygenSaturation,
        heartRate: reading.heartRate,
        temperature: reading.temperature,
        riskLevel: riskLevel,
        readingCount: admin.firestore.FieldValue.increment(1),
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      // Update patient's profile with latest metrics
      await db.doc(`patients/${patientId}/profile/metrics`).set({
        latestReading: {
          respiratoryRate: reading.respiratoryRate,
          oxygenSaturation: reading.oxygenSaturation,
          heartRate: reading.heartRate,
          temperature: reading.temperature,
          riskLevel: riskLevel,
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        }
      }, { merge: true });

    } catch (error) {
      console.error('Error processing reading:', error);
    }
});

/**
 * Helper function to calculate risk level based on patient readings
 */
function calculateRiskLevel(reading) {
  const thresholds = {
    respiratoryRate: { warning: 20, danger: 25 },
    oxygenSaturation: { warning: 94, danger: 90 },
    heartRate: { warning: 100, danger: 120 },
    temperature: { warning: 37.8, danger: 38.5 }
  };

  if (
    reading.respiratoryRate > thresholds.respiratoryRate.danger ||
    reading.oxygenSaturation < thresholds.oxygenSaturation.danger ||
    reading.heartRate > thresholds.heartRate.danger ||
    reading.temperature > thresholds.temperature.danger
  ) {
    return 'danger';
  } else if (
    reading.respiratoryRate > thresholds.respiratoryRate.warning ||
    reading.oxygenSaturation < thresholds.oxygenSaturation.warning ||
    reading.heartRate > thresholds.heartRate.warning ||
    reading.temperature > thresholds.temperature.warning
  ) {
    return 'warning';
  }
  return 'normal';
}

/**
 * Cloud Function to update patient profile in users_roles collection
 */
exports.onPatientProfileUpdate = functions.firestore
  .document('patients/{patientId}')
  .onWrite(async (change, context) => {
    const patientId = context.params.patientId;
    const newData = change.after.exists ? change.after.data() : null;
    const oldData = change.before.exists ? change.before.data() : null;

    if (!newData) return null;

    try {
      // Update the users_roles document
      await admin.firestore().doc(`users_roles/${patientId}`).update({
        lastProfileUpdate: admin.firestore.FieldValue.serverTimestamp(),
        name: newData.name,
        email: newData.email,
        role: 'patient',
        status: 'active',
        practitionerId: newData.practitionerId // Add practitioner reference
      });

      // Create/update practitioner's patient list
      if (newData.practitionerId) {
        await admin.firestore()
          .doc(`practitioners/${newData.practitionerId}/patients/${patientId}`)
          .set({
            name: newData.name,
            email: newData.email,
            lastUpdate: admin.firestore.FieldValue.serverTimestamp()
          }, { merge: true });
      }

      // Create a health summary document
      const healthSummaryRef = admin.firestore()
        .doc(`patients/${patientId}/health_summary/latest`);

      await healthSummaryRef.set({
        age: newData.age,
        height: newData.height,
        weight: newData.weight,
        bmi: (newData.weight / Math.pow(newData.height / 100, 2)).toFixed(1),
        smokingStatus: newData.smokingStatus,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      return null;
    } catch (error) {
      console.error('Error updating patient profile:', error);
      return null;
    }
});

/**
 * Cloud Function to delete a Firebase Auth user
 * This function requires admin authentication
 */
exports.deleteAuthUser = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an authenticated user with admin role
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  try {
    // Check if the caller has admin privileges
    const callerUid = context.auth.uid;
    const callerDoc = await admin.firestore().doc(`users_roles/${callerUid}`).get();

    if (!callerDoc.exists) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'User role not found.'
      );
    }

    const callerData = callerDoc.data();
    const isAdmin = callerData.role === 'admin' || callerData.role === 'superAdmin';
    const isSelfDelete = callerUid === data.userId;

    // Only allow admins to delete other users, or users to delete themselves
    if (!isAdmin && !isSelfDelete) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can delete other users.'
      );
    }

    // Get the user ID to delete
    const userId = data.userId;
    if (!userId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'The function must be called with a valid userId.'
      );
    }

    // Delete the user from Firebase Auth
    await admin.auth().deleteUser(userId);

    return { success: true, message: 'User successfully deleted from Firebase Auth.' };
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new functions.https.HttpsError(
      'internal',
      error.message || 'An error occurred while deleting the user.'
    );
  }
});

/**
 * Cloud Function to clean up Firestore data when a user is deleted from Firebase Auth
 */
exports.onUserDeleted = functions.auth.user().onDelete(async (user) => {
  try {
    const userId = user.uid;
    console.log(`User ${userId} was deleted from Firebase Auth. Cleaning up Firestore data...`);

    const batch = admin.firestore().batch();

    // Check user role
    const userRoleRef = admin.firestore().doc(`users_roles/${userId}`);
    const userRoleDoc = await userRoleRef.get();

    if (userRoleDoc.exists) {
      const userData = userRoleDoc.data();
      const userRole = userData.role;

      // Delete role-specific documents
      if (userRole === 'patient') {
        // Delete from patients collection
        const patientRef = admin.firestore().doc(`patients/${userId}`);
        batch.delete(patientRef);

        // If patient has a practitioner, remove the reference
        if (userData.practitionerId) {
          const practitionerPatientRef = admin.firestore()
            .doc(`practitioners/${userData.practitionerId}/patients/${userId}`);
          batch.delete(practitionerPatientRef);
        }

        // Delete patient readings subcollection
        const readingsSnapshot = await admin.firestore()
          .collection(`patients/${userId}/readings`)
          .get();

        readingsSnapshot.forEach(doc => {
          batch.delete(doc.ref);
        });

        // Delete health summary
        const healthSummaryRef = admin.firestore()
          .doc(`patients/${userId}/health_summary/latest`);
        batch.delete(healthSummaryRef);

        // Delete daily metrics
        const dailyMetricsSnapshot = await admin.firestore()
          .collection(`patients/${userId}/daily_metrics`)
          .get();

        dailyMetricsSnapshot.forEach(doc => {
          batch.delete(doc.ref);
        });

      } else if (userRole === 'practitioner') {
        // Delete from practitioners collection
        const practitionerRef = admin.firestore().doc(`practitioners/${userId}`);
        batch.delete(practitionerRef);

        // Update patients that were assigned to this practitioner
        const patientsSnapshot = await admin.firestore()
          .collection('patients')
          .where('practitionerId', '==', userId)
          .get();

        patientsSnapshot.forEach(doc => {
          batch.update(doc.ref, { practitionerId: null });
        });

        // Delete practitioner's patients subcollection
        const practitionerPatientsSnapshot = await admin.firestore()
          .collection(`practitioners/${userId}/patients`)
          .get();

        practitionerPatientsSnapshot.forEach(doc => {
          batch.delete(doc.ref);
        });
      }

      // Delete user_roles document
      batch.delete(userRoleRef);
    }

    // Delete user metrics
    const metricsRef = admin.firestore().doc(`user_metrics/${userId}`);
    batch.delete(metricsRef);

    // Delete patient requests
    const patientRequestsSnapshot = await admin.firestore()
      .collection('patient_requests')
      .where('patientId', '==', userId)
      .get();

    patientRequestsSnapshot.forEach(doc => {
      batch.delete(doc.ref);
    });

    // Delete practitioner requests
    const practitionerRequestsSnapshot = await admin.firestore()
      .collection('patient_requests')
      .where('practitionerId', '==', userId)
      .get();

    practitionerRequestsSnapshot.forEach(doc => {
      batch.delete(doc.ref);
    });

    // Commit all the Firestore deletions
    await batch.commit();

    console.log(`✅ Firestore data for user ${userId} successfully cleaned up`);
    return null;
  } catch (error) {
    console.error('❌ Error cleaning up Firestore data:', error);
    return null;
  }
});
