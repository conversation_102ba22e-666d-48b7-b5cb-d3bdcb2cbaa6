const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Cloud Function to create a user document in Firestore when a new user signs in with Google
 * This function is triggered when a new user is created in Firebase Auth
 */
exports.createUserDocument = functions.auth.user().onCreate(async (user) => {
  try {
    // Check if the user already has a document in users_roles collection
    const userDoc = await db.collection('users_roles').doc(user.uid).get();
    
    if (userDoc.exists) {
      console.log(`User document already exists for ${user.uid}`);
      return null;
    }
    
    // Create a new user document with default fields
    const now = new Date().toISOString();
    const provider = user.providerData && user.providerData.length > 0 
      ? user.providerData[0].providerId 
      : 'password';
    
    const userData = {
      email: user.email,
      role: 'patient', // Default role
      status: 'active',
      createdAt: now,
      updatedAt: now,
      last_login: now,
      name: user.displayName || (user.email ? user.email.split('@')[0] : 'New User'),
      provider: provider
    };
    
    // Create the user document
    await db.collection('users_roles').doc(user.uid).set(userData);
    
    console.log(`Created new user document for ${user.uid}`);
    return null;
  } catch (error) {
    console.error('Error creating user document:', error);
    return null;
  }
});
