# Respirasense Web Database Structure

This document outlines the Firestore database structure used in the Respirasense Web application, focusing on the relationships between collections and how to efficiently retrieve data.

## Main Collections

### users_roles
- **Purpose**: Stores user authentication information and roles
- **Document ID**: User ID (from Firebase Auth)
- **Fields**:
  - `role`: String - User role (admin, practitioner, patient)
  - `email`: String - User email
  - `name`: String - User name
  - `status`: String - User status (active, pending, inactive)
  - `createdAt`: Timestamp - When the user was created
  - `updatedAt`: Timestamp - When the user was last updated

### patients
- **Purpose**: Stores patient information
- **Document ID**: Patient ID (same as user ID)
- **Fields**:
  - `practitionerId`: String - ID of the assigned practitioner
  - `lastUpdate`: Timestamp - When the patient was last updated
  - Various patient-specific fields

#### Subcollections:
- **profile**: Contains patient profile details
  - Document ID: `details` - Basic profile information
  - Document ID: `metrics` - Latest health metrics
- **readings**: Contains health readings with timestamps
  - Document ID: Auto-generated
  - Fields:
    - `timestamp`: Timestamp - When the reading was taken
    - `respiratoryRate`: Number
    - `oxygenSaturation`: Number
    - `heartRate`: Number
    - `temperature`: Number
    - `riskLevel`: String - Calculated risk level
- **daily_metrics**: Contains aggregated daily health data
  - Document ID: Date string (YYYY-MM-DD)
  - Fields:
    - `respiratoryRate`: Number - Average for the day
    - `oxygenSaturation`: Number - Average for the day
    - `heartRate`: Number - Average for the day
    - `temperature`: Number - Average for the day
    - `riskLevel`: String - Highest risk level for the day
    - `readingCount`: Number - Number of readings for the day
    - `lastUpdated`: Timestamp
- **health_records/history**: Contains historical health data
- **health_summary**: Contains latest health summary
  - Document ID: `latest`
  - Fields:
    - `age`: Number
    - `height`: Number
    - `weight`: Number
    - `bmi`: Number - Calculated BMI
    - `smokingStatus`: String
    - `lastUpdated`: Timestamp

### practitioners
- **Purpose**: Stores practitioner information
- **Document ID**: Practitioner ID (same as user ID)
- **Fields**:
  - `name`: String - Practitioner name
  - `email`: String - Practitioner email
  - `specialization`: String - Medical specialization
  - `licenseNumber`: String - Professional license number
  - `status`: String - Practitioner status (active, inactive)
  - `createdAt`: Timestamp - When the practitioner was created
  - `lastUpdate`: Timestamp - When the practitioner was last updated

#### Subcollections:
- **patients**: References to patients assigned to this practitioner
  - Document ID: Patient ID
  - Fields:
    - `name`: String - Patient name
    - `email`: String - Patient email
    - `lastUpdate`: Timestamp - When the reference was last updated

### overview_stats
- **Purpose**: Stores system-wide statistics
- **Document ID**: `latest`
- **Fields**:
  - `totalPatients`: Number
  - `activePatients`: Number
  - `criticalAlerts`: Number
  - `dailyReadings`: Number
  - `lastUpdated`: Timestamp

### system_health
- **Purpose**: Stores system health information
- **Document ID**: `current`
- **Fields**:
  - `status`: String - System status (healthy, warning, critical)
  - `lastUpdate`: Timestamp
  - `metrics`: Object - System metrics (CPU, memory, etc.)

### alerts
- **Purpose**: Stores health alerts for patients
- **Document ID**: Auto-generated
- **Fields**:
  - `patientId`: String - ID of the patient
  - `type`: String - Alert type
  - `severity`: String - Alert severity (warning, critical)
  - `message`: String - Alert message
  - `value`: Number - Measured value
  - `threshold`: Number - Threshold value
  - `status`: String - Alert status (active, resolved)
  - `timestamp`: Timestamp - When the alert was generated

### daily_metrics
- **Purpose**: Stores system-wide daily metrics
- **Document ID**: Date string (YYYY-MM-DD)
- **Fields**:
  - `date`: String - Date string (YYYY-MM-DD)
  - `totalReadings`: Number - Total readings for the day
  - `averageMetrics`: Object - Average metrics for the day
  - `criticalAlerts`: Number - Number of critical alerts for the day
  - `activePatients`: Number - Number of active patients for the day

### admin_settings
- **Purpose**: Stores system settings
- **Document ID**: `general`
- **Fields**:
  - `alertThresholds`: Object - Alert thresholds for different metrics
  - `notificationSettings`: Object - Notification settings
  - `dataRetentionDays`: Number - Number of days to retain data
  - `lastUpdated`: Timestamp

### audit_logs
- **Purpose**: Stores system audit logs
- **Document ID**: Auto-generated
- **Fields**:
  - Various audit log fields

### patient_requests
- **Purpose**: Manages patient-practitioner linking requests
- **Document ID**: Auto-generated
- **Fields**:
  - `patientId`: String - ID of the patient (may be null for email-only invitations)
  - `patientEmail`: String - Email of the patient
  - `practitionerId`: String - ID of the practitioner
  - `practitionerName`: String - Name of the practitioner
  - `requestType`: String - Type of request ('practitioner_initiated' or 'patient_initiated')
  - `status`: String - Request status ('pending', 'approved', 'rejected', 'cancelled', 'expired')
  - `message`: String - Optional message for the request
  - `createdAt`: Timestamp - When the request was created
  - `responseAt`: Timestamp - When the request was responded to
  - `expiresAt`: Timestamp - When the request expires (optional)

### studies
- **Purpose**: Contains study information
- **Document ID**: Study ID

#### Subcollections:
- **users**: Users participating in studies
  - Document ID: User ID
  - Subcollections:
    - **healthKit**: Health data from Apple HealthKit
    - **healthFhir**: FHIR-formatted health data

## Relationships Between Collections

### Patient-Practitioner Relationship
- A patient can be assigned to one practitioner
- A practitioner can have multiple patients
- The relationship is maintained in two places:
  1. The `practitionerId` field in the patient document
  2. A document in the practitioner's `patients` subcollection

#### Patient-Practitioner Linking Workflow
- The linking process uses the `patient_requests` collection to manage invitations
- Bidirectional invitation flow:
  1. **Practitioner-initiated**: Practitioner sends invitation to patient by email
     - Creates a document in `patient_requests` with `requestType: 'practitioner_initiated'`
     - Patient receives notification and can approve/reject the request
  2. **Patient-initiated**: Patient sends invitation to practitioner
     - Creates a document in `patient_requests` with `requestType: 'patient_initiated'`
     - Practitioner receives notification and can approve/reject the request
- When a request is approved:
  1. The `status` field in the request document is updated to `'approved'`
  2. The patient document is updated with the practitioner's ID
  3. A document is created in the practitioner's `patients` subcollection

### Patient Health Data Relationship
- A patient has multiple readings in the `readings` subcollection
- Daily aggregated metrics are stored in the `daily_metrics` subcollection
- The latest metrics are stored in the `profile/metrics` document
- Historical data is stored in the `health_records/history` document
- A health summary is stored in the `health_summary/latest` document

## Efficient Data Retrieval

### Getting a Patient's Health Data
```javascript
// Use the getPatientHealthData helper function
import { getPatientHealthData } from '@/utils/firestore-helpers';

const patientHealthData = await getPatientHealthData(patientId);
```

### Getting a Practitioner's Patients
```javascript
// Use the getPractitionerPatients helper function
import { getPractitionerPatients } from '@/utils/firestore-helpers';

const patients = await getPractitionerPatients(practitionerId);
```

### Assigning a Patient to a Practitioner
```javascript
// Use the assignPatientToPractitioner helper function
import { assignPatientToPractitioner } from '@/utils/firestore-helpers';

await assignPatientToPractitioner(patientId, practitionerId, patientData);
```

### Removing a Patient from a Practitioner
```javascript
// Use the removePatientFromPractitioner helper function
import { removePatientFromPractitioner } from '@/utils/firestore-helpers';

await removePatientFromPractitioner(patientId, practitionerId);
```

### Requesting Patient Linking
```javascript
// Use the requestPatientLinking helper function
import { requestPatientLinking } from '@/utils/firestore-helpers';

const result = await requestPatientLinking(practitionerId, patientEmail);
// result contains: { success, requestId, patientExists, message }
```

### Getting Patient Linking Requests
```javascript
// Use the getPatientLinkingRequests helper function
import { getPatientLinkingRequests } from '@/utils/firestore-helpers';

const requests = await getPatientLinkingRequests(practitionerId);
```

### Cancelling a Patient Linking Request
```javascript
// Use the cancelPatientLinkingRequest helper function
import { cancelPatientLinkingRequest } from '@/utils/firestore-helpers';

await cancelPatientLinkingRequest(requestId);
```

## Indexes

The following indexes are defined to optimize common queries:

1. Alerts by status and timestamp:
   - Collection: `alerts`
   - Fields: `status` (ASC), `timestamp` (DESC)

2. Patients by practitioner and last update:
   - Collection: `patients`
   - Fields: `practitionerId` (ASC), `lastUpdate` (DESC)

3. Readings by patient and timestamp:
   - Collection Group: `readings`
   - Fields: `patientId` (ASC), `timestamp` (DESC)

4. Daily metrics by patient and date:
   - Collection Group: `daily_metrics`
   - Fields: `patientId` (ASC), `date` (DESC)

5. HealthKit data by various criteria:
   - Collection: `healthKit`
   - Multiple indexes for different query patterns

6. Patient requests by practitioner and status:
   - Collection: `patient_requests`
   - Fields: `practitionerId` (ASC), `status` (ASC), `createdAt` (DESC)

7. Patient requests by patient and status:
   - Collection: `patient_requests`
   - Fields: `patientId` (ASC), `status` (ASC), `requestType` (ASC)

## Security Rules

Security rules are defined to ensure proper access control:

1. Patients can access their own data
2. Practitioners can access data for patients assigned to them
3. Admins can access all data
4. Write operations are restricted based on role

For the `patient_requests` collection:
1. Practitioners can create linking requests
2. Practitioners can read, update, and delete their own requests
3. Patients can read and update requests directed to them (by ID or email)
4. Admins have full access to all requests

See `firestore.rules` for the complete security rules configuration.
