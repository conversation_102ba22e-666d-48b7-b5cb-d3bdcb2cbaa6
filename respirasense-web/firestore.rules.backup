rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    // Safe version of getUserRole that doesn't fail if the document doesn't exist
    function getUserRole() {
      let userId = request.auth.uid;
      let userDocPath = /databases/$(database)/documents/users_roles/$(userId);
      let userDoc = existsAfter(userDocPath) ? getAfter(userDocPath) :
                   exists(userDocPath) ? get(userDocPath) : null;
      return userDoc != null ? userDoc.data.role : null;
    }

    function isAdmin() {
      let role = getUserRole();
      return role == 'admin' || role == 'superAdmin';
    }

    function isPractitioner() {
      return getUserRole() == 'practitioner';
    }

    function isPatient() {
      return getUserRole() == 'patient';
    }

    // Helper function to check if practitioner is assigned to patient
    function isPractitionerForPatient(patientId) {
      // Check in patients collection
      let patientDoc = get(/databases/$(database)/documents/patients/$(patientId));
      return patientDoc != null && patientDoc.data.practitionerId == request.auth.uid;
    }

    // Additional helper to check if practitioner has any relationship with patient
    function hasPractitionerPatientRelationship(patientId) {
      return isPractitionerForPatient(patientId) || isPractitioner();
    }

    // Add new helper function for pending users
    function isPendingUser(userId) {
      let userDoc = get(/databases/$(database)/documents/users_roles/$(userId));
      return userDoc != null && userDoc.data.status == 'pending';
    }

    // Overview statistics collection
    match /overview_stats/{document} {
      allow read: if isAuthenticated() && (isAdmin() || isPractitioner());
      allow write: if isAuthenticated() && isAdmin();
    }

    // System health collection
    match /system_health/{document} {
      allow read: if isAuthenticated() && isAdmin();
      allow write: if isAuthenticated() && isAdmin();
    }

    // Alerts collection
    match /alerts/{alertId} {
      allow read: if isAuthenticated() && (isAdmin() || isPractitioner());
      allow write: if isAuthenticated() && isAdmin();
    }

    // Daily metrics collection
    match /daily_metrics/{date} {
      allow read: if isAuthenticated() && (isAdmin() || isPractitioner());
      allow write: if isAuthenticated() && isAdmin();
    }

    // User roles collection
    match /users_roles/{userId} {
      // Allow read for authenticated users who are the document owner or admin
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        isAdmin()
      );

      // Allow practitioners to query users_roles to find patients by email
      // This is critical for patient linking functionality
      allow list: if isAuthenticated() && (isAdmin() || isPractitioner());

      // Allow creation for any authenticated user (needed for Google sign-in)
      // This is critical for new users signing up with Google
      allow create: if isAuthenticated() && request.auth.uid == userId;

      // Allow updates with different conditions
      allow update: if isAuthenticated() && (
        // User can update their own document
        request.auth.uid == userId ||
        // Admin can update any document
        isAdmin()
      );
    }

    // Admin settings collection
    match /admin_settings/{document} {
      allow read: if isAuthenticated() && isAdmin();
      allow write: if isAuthenticated() && isAdmin();
    }

    // Audit logs collection
    match /audit_logs/{logId} {
      allow read: if isAuthenticated() && isAdmin();
      allow write: if isAuthenticated();
    }

    // Patient requests collection - for practitioner-patient linking
    match /patient_requests/{requestId} {
      // Allow any authenticated user to create requests
      // This is the most permissive rule to ensure it works
      allow create: if isAuthenticated();

      // Allow practitioners to read, update, and delete their own requests
      // This is critical for the patient linking functionality
      allow read, update, delete: if isAuthenticated() && isPractitioner() && (
        resource.data.practitionerId == request.auth.uid ||
        // Allow practitioners to query for requests directed to their email
        resource.data.practitionerEmail == get(/databases/$(database)/documents/users_roles/$(request.auth.uid)).data.email
      );

      // Allow patients to read, update, and delete requests they initiated or directed to them
      allow read, update, delete: if isAuthenticated() && isPatient() && (
        // Requests directed to this patient
        resource.data.patientId == request.auth.uid ||
        resource.data.patientEmail == get(/databases/$(database)/documents/users_roles/$(request.auth.uid)).data.email ||
        // Requests initiated by this patient
        (resource.data.requestType == 'patient_initiated' && resource.data.patientId == request.auth.uid)
      );

      // Allow admins full access
      allow read, write: if isAuthenticated() && isAdmin();
    }

    // Patients collection
    match /patients/{patientId} {
      // Base patient document
      allow read: if isAuthenticated() && (
        request.auth.uid == patientId ||
        hasPractitionerPatientRelationship(patientId) ||
        isAdmin()
      );

      // Allow practitioners to query patients collection
      allow list: if isAuthenticated() && isPractitioner();

      // Allow patients to write their own data
      allow write: if isAuthenticated() && (
        request.auth.uid == patientId ||
        isAdmin()
      );

      // Allow practitioners to update patient document when linking
      // This is critical for assigning patients to practitioners
      allow update: if isAuthenticated() && isPractitioner() &&
        request.resource.data.practitionerId == request.auth.uid;

      // Profile subcollection
      match /profile/{document} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          hasPractitionerPatientRelationship(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          hasPractitionerPatientRelationship(patientId) ||
          isAdmin()
        );
      }

      // Readings subcollection
      match /readings/{readingId} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
      }

      // Health readings subcollection
      match /health_readings/{readingId} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          hasPractitionerPatientRelationship(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          hasPractitionerPatientRelationship(patientId) ||
          isAdmin()
        );
      }

      // Daily metrics subcollection
      match /daily_metrics/{date} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
      }

      // Health records subcollection
      match /health_records/{document} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
      }

      // Health summary subcollection
      match /health_summary/{document} {
        allow read: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == patientId ||
          isPractitionerForPatient(patientId) ||
          isAdmin()
        );
      }

      // Notifications subcollection
      match /notifications/{notificationId} {
        // Patients can read and update their own notifications
        allow read, update: if isAuthenticated() && request.auth.uid == patientId;

        // Practitioners can create notifications for their patients
        // or for patients they're trying to link with
        allow create: if isAuthenticated() && isPractitioner();

        // Admins have full access
        allow read, write: if isAuthenticated() && isAdmin();
      }
    }

    // Practitioners collection
    match /practitioners/{practitionerId} {
      // Base practitioner document
      allow read: if isAuthenticated() && (
        request.auth.uid == practitionerId ||
        isAdmin() ||
        // Allow patients to read practitioner documents for linking
        isPatient()
      );

      // Allow patients to query practitioners collection for linking
      allow list: if isAuthenticated() && isPatient();

      // Allow practitioners to write their own document
      allow write: if isAuthenticated() && (
        request.auth.uid == practitionerId ||
        isAdmin()
      );

      // Create the practitioner document if it doesn't exist
      // This is critical for first-time practitioner setup
      allow create: if isAuthenticated() && isPractitioner() &&
        request.auth.uid == practitionerId;

      // Patients subcollection - references to assigned patients
      match /patients/{patientId} {
        allow read: if isAuthenticated() && (
          request.auth.uid == practitionerId ||
          isAdmin()
        );
        allow write: if isAuthenticated() && (
          request.auth.uid == practitionerId ||
          isAdmin()
        );
      }
    }

    // Studies collection
    match /studies/{studyId} {
      allow read: if isAuthenticated() && isAdmin();
      allow write: if isAuthenticated() && isAdmin();

      // Users in studies
      match /users/{userId} {
        allow read: if isAuthenticated() && (
          request.auth.uid == userId ||
          isAdmin()
        );
        allow write: if isAuthenticated() && isAdmin();

        // Health data subcollections
        match /healthKit/{healthId} {
          allow read: if isAuthenticated() && (
            request.auth.uid == userId ||
            isAdmin()
          );
          allow write: if isAuthenticated() && (
            request.auth.uid == userId ||
            isAdmin()
          );
        }

        match /healthFhir/{healthId} {
          allow read: if isAuthenticated() && (
            request.auth.uid == userId ||
            isAdmin()
          );
          allow write: if isAuthenticated() && isAdmin();
        }
      }
    }

    // Chats collection for patient-practitioner communication
    match /chats/{chatId} {
      // Allow read/write if the user is part of the chat
      // chatId is expected to be in the format "userId1_userId2" where the IDs are sorted
      allow read, write: if isAuthenticated() && (
        request.auth.uid in chatId.split('_')
      );

      // Messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated() && (
          request.auth.uid in chatId.split('_')
        );

        // Allow create if the user is the sender
        allow create: if isAuthenticated() && (
          request.auth.uid in chatId.split('_') &&
          request.resource.data.senderId == request.auth.uid
        );

        // Allow update for marking messages as read
        allow update: if isAuthenticated() && (
          request.auth.uid in chatId.split('_') &&
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read'])
        );
      }
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
