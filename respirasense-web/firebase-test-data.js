// Firebase test data generator
// This script can be used to generate test data for the Firebase database

const generateTestData = () => {
  const patients = [
    { id: 'patient1', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'patient2', name: '<PERSON>', email: '<EMAIL>' }
  ];

  const readings = [];
  
  // Generate readings for the past 30 days
  const today = new Date();
  for (let i = 0; i < 30; i++) {
    const date = new Date();
    date.setDate(today.getDate() - i);
    
    // Generate 1-3 readings per day
    const readingsPerDay = Math.floor(Math.random() * 3) + 1;
    
    for (let j = 0; j < readingsPerDay; j++) {
      // Set random hour between 8am and 8pm
      date.setHours(8 + Math.floor(Math.random() * 12), 
                   Math.floor(Math.random() * 60),
                   Math.floor(Math.random() * 60));
      
      const reading = {
        id: `reading-${i}-${j}`,
        patientId: patients[Math.floor(Math.random() * patients.length)].id,
        lastUpdated: date.toISOString(),
        respiratoryRate: 12 + Math.random() * 10, // 12-22
        oxygenSaturation: 92 + Math.random() * 8, // 92-100
        heartRate: 60 + Math.random() * 40, // 60-100
        temperature: 36 + Math.random() * 2, // 36-38
        riskLevel: getRiskLevel(),
        notes: getRandomNote()
      };
      
      readings.push(reading);
    }
  }
  
  return { patients, readings };
};

const getRiskLevel = () => {
  const levels = ['low', 'medium', 'high'];
  const weights = [0.7, 0.2, 0.1]; // 70% low, 20% medium, 10% high
  
  const random = Math.random();
  let sum = 0;
  
  for (let i = 0; i < weights.length; i++) {
    sum += weights[i];
    if (random < sum) {
      return levels[i];
    }
  }
  
  return levels[0];
};

const getRandomNote = () => {
  const notes = [
    'Patient feeling well',
    'Slight shortness of breath',
    'Patient reports mild cough',
    'No symptoms reported',
    'Patient exercised before reading',
    'Reading taken after meal',
    'Patient reports feeling tired',
    null // Some readings won't have notes
  ];
  
  return notes[Math.floor(Math.random() * notes.length)];
};

// Example of how to use this data in Firebase
/*
const firebaseConfig = {
  // Your Firebase config
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

const data = generateTestData();

// Upload patients
data.patients.forEach(patient => {
  db.collection('patients').doc(patient.id).set(patient);
});

// Upload readings
data.readings.forEach(reading => {
  db.collection('readings').doc(reading.id).set(reading);
});
*/

console.log(JSON.stringify(generateTestData(), null, 2));
