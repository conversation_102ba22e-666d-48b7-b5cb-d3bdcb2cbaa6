{"name": "RespiraSense", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@kyvg/vue3-notification": "^3.4.1", "@popperjs/core": "^2.11.8", "@vueform/multiselect": "^2.6.11", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "core-js": "^3.40.0", "firebase": "^11.2.0", "firebase-admin": "^13.2.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "uuid": "^11.0.5", "v-calendar": "^3.0.0-alpha.5", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "vue-signature-pad": "^3.0.2", "vue-simple-calendar": "^7.1.0", "vue-spinner": "^1.0.4", "vue-sweetalert2": "^5.0.11", "vuex": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-unit-jest": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.6", "node-sass": "^9.0.0", "sass-loader": "^16.0.4", "typescript": "~5.7.3", "vue-jest": "^5.0.0-0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "jest": {"preset": "@vue/cli-plugin-unit-jest", "transform": {"^.+\\.vue$": "vue-jest"}}}