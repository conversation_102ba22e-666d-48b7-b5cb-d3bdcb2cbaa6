import { db } from "@/plugins/firebase/firebase";
import { 
  collection, doc, getDoc, setDoc, updateDoc, deleteDoc, getDocs, 
  query, where, orderBy, limit 
} from "firebase/firestore";

export class Ref {
    constructor(object, method, payload = null) {
        this.object = object;
        this.method = method;
        this.payload = payload;
    }

    WHERE(params) {
        if (this.object.type === "collection") {
            this.object = query(this.object, where(params[0], params[1], params[2]));
        } else {
            console.error("WHERE can only be used on a collection, not a document.");
        }
        return this;
    }

    LIMIT(limitValue) {
        if (this.object.type === "collection") {
            this.object = query(this.object, limit(limitValue));
        } else {
            console.error("LIMIT can only be used on a collection, not a document.");
        }
        return this;
    }

    ORDER_BY(name, desc = false) {
        if (this.object.type === "collection") {
            this.object = query(this.object, orderBy(name, desc ? "desc" : "asc"));
        } else {
            console.error("ORDER_BY can only be used on a collection, not a document.");
        }
        return this;
    }

    async Execute() {
        switch (this.method) {
            case "GET":
                if (this.object.type === "collection") {
                    return await getDocs(this.object);
                } else {
                    return await getDoc(this.object);
                }
            case "ADD":
                return await addDoc(this.object, this.payload);
            case "SET":
                return await setDoc(this.object, this.payload);
            case "UPDATE":
                return await updateDoc(this.object, this.payload);
            case "DELETE":
                return await deleteDoc(this.object);
            default:
                return await getDocs(this.object);
        }
    }

    CLONE() {
        return new Ref(this.object, this.method, this.payload);
    }
}

// ✅ Corrected function to differentiate between `collection` and `doc`
const objectFirebase = (path) => {
    let parts = path.split("/");
    
    if (parts.length % 2 !== 0) {
        return { ref: collection(db, ...parts), type: "collection" };
    } else {
        return { ref: doc(db, ...parts), type: "document" };
    }
};

export const GET = (path) => {
    let { ref, type } = objectFirebase(path);
    return new Ref(ref, "GET", null, type);
};

export const POST = (path, payload) => {
    let { ref } = objectFirebase(path);
    if (payload.emptyDoc) {
        return new Ref(ref, "ADD", payload.data);
    } else {
        return new Ref(ref, "SET", payload.data);
    }
};

export const PATH = (path, payload) => {
    let { ref } = objectFirebase(path);
    return new Ref(ref, "SET", payload.data);
};

export const PUT = (path, payload) => {
    let { ref } = objectFirebase(path);
    return new Ref(ref, "UPDATE", payload.data);
};

export const DELETE = (path) => {
    let { ref } = objectFirebase(path);
    return new Ref(ref, "DELETE");
};

export const getPatientReadings = async (patientId) => {
  const readingsRef = collection(db, `patients/${patientId}/readings`);
  const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(10));
  return await getDocs(q);
};

export const getPatientProfile = async (patientId) => {
  const profileRef = doc(db, `patients/${patientId}/profile/metrics`);
  return await getDoc(profileRef);
};

export const getDailyMetrics = async (patientId, date) => {
  const metricsRef = doc(db, `patients/${patientId}/daily_metrics/${date}`);
  return await getDoc(metricsRef);
};

export const getPractitionerProfile = async (practitionerId) => {
  const profileRef = doc(db, `practitioners/${practitionerId}`);
  return await getDoc(profileRef);
};

export const getPractitionerPatients = async (practitionerId) => {
  const patientsRef = collection(db, 'patients');
  const q = query(
    patientsRef, 
    where('practitionerId', '==', practitionerId),
    orderBy('lastUpdate', 'desc')
  );
  return await getDocs(q);
};

export const assignPatientToPractitioner = async (patientId, practitionerId) => {
  const patientRef = doc(db, `patients/${patientId}`);
  await updateDoc(patientRef, {
    practitionerId: practitionerId,
    lastUpdate: new Date()
  });
};

export const createPractitionerProfile = async (practitionerId, profileData) => {
  const profileRef = doc(db, `practitioners/${practitionerId}`);
  await setDoc(profileRef, {
    ...profileData,
    createdAt: new Date(),
    lastUpdate: new Date()
  });
};
