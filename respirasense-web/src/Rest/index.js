import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs
} from 'firebase/firestore';

// Get Firestore instance
const db = getFirestore();

class Request {
  constructor(path) {
    this.path = path;
    this.method = 'GET';
    this.whereConditions = [];
    this.orderByField = null;
    this.orderByDirection = 'asc';
    this.limitValue = null;
    this.startAfterValue = null;
    this.data = null;
  }

  WHERE(condition) {
    if (Array.isArray(condition)) {
      this.whereConditions.push({
        field: condition[0],
        operator: condition[1],
        value: condition[2]
      });
    } else {
      this.whereConditions.push(condition);
    }
    return this;
  }

  ORDER_BY(field, direction = 'asc') {
    this.orderByField = field;
    this.orderByDirection = direction;
    return this;
  }

  LIMIT(value) {
    this.limitValue = value;
    return this;
  }

  START_AFTER(value) {
    this.startAfterValue = value;
    return this;
  }

  CLONE() {
    const cloned = new Request(this.path);
    cloned.method = this.method;
    cloned.whereConditions = [...this.whereConditions];
    cloned.orderByField = this.orderByField;
    cloned.orderByDirection = this.orderByDirection;
    cloned.limitValue = this.limitValue;
    cloned.startAfterValue = this.startAfterValue;
    cloned.data = this.data;
    return cloned;
  }

  async Execute() {
    try {
      const pathSegments = this.path.split('/');

      if (this.method === 'GET') {
        // Handle document get
        if (pathSegments.length % 2 === 0) {
          const docRef = doc(db, ...pathSegments);
          const docSnap = await getDoc(docRef);
          return docSnap;
        }
        
        // Handle collection query
        const collectionRef = collection(db, ...pathSegments);
        let constraints = [];

        // Add where conditions
        this.whereConditions.forEach(condition => {
          constraints.push(where(condition.field, condition.operator, condition.value));
        });

        // Add orderBy
        if (this.orderByField) {
          constraints.push(orderBy(this.orderByField, this.orderByDirection));
        }

        // Add limit
        if (this.limitValue) {
          constraints.push(limit(this.limitValue));
        }

        // Add startAfter
        if (this.startAfterValue) {
          constraints.push(startAfter(this.startAfterValue));
        }

        const q = query(collectionRef, ...constraints);
        const querySnapshot = await getDocs(q);

        // Add helper methods to the snapshot
        querySnapshot.exists = function() {
          return !this.empty;
        };

        querySnapshot.data = function() {
          if (this.empty) return null;
          const data = {};
          this.forEach(doc => {
            data[doc.id] = doc.data();
          });
          return data;
        };

        return querySnapshot;

      } else if (this.method === 'POST') {
        const docRef = doc(db, ...pathSegments);
        return await setDoc(docRef, this.data);

      } else if (this.method === 'PUT') {
        const docRef = doc(db, ...pathSegments);
        return await updateDoc(docRef, this.data);
      }

    } catch (error) {
      console.error('Execute error:', error);
      throw error;
    }
  }
}

export default {
  GET: (path) => new Request(path),
  POST: (path, { data }) => {
    const request = new Request(path);
    request.method = 'POST';
    request.data = data;
    return request;
  },
  PUT: (path, { data }) => {
    const request = new Request(path);
    request.method = 'PUT';
    request.data = data;
    return request;
  }
};
