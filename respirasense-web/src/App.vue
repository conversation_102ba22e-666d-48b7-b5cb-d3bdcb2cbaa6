<template>
  <div id="app">
    <notifications position="top left" width="100%" />
    <component :is="layout">
      <router-view />
    </component>
  </div>
</template>

<script>
import MainLayout from '@/common/layout/Main.vue'
import AuthLayout from '@/common/layout/Auth.vue'
import { defineComponent, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'App',
  components: {
    MainLayout,
    AuthLayout
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()

    onMounted(() => {
      // Set up periodic session checks
      setInterval(() => {
        const isSessionValid = store.dispatch('authFirebase/checkSession')
        if (!isSessionValid) {
          router.push('/auth/login')
        }
      }, 5 * 60 * 1000) // Check every 5 minutes
    })

    const layout = computed(() => {
      const layoutName = route.meta.layout || 'default'
      return layoutName === 'main' ? 'MainLayout' : 
             layoutName === 'auth' ? 'AuthLayout' : 'div'
    })
    
    return { layout }
  }
})
</script>

<style lang="scss">
@import "@/assets/styles/style.scss";

body {
  min-height: 100vh;
  margin: 0;
  font-family: 'Lato Regular', Helvetica, sans-serif;
}

#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}
</style>
