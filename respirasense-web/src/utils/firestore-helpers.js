import { db } from '@/plugins/firebase/firebase';
import {
  doc,
  setDoc,
  updateDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';

/**
 * Assigns a patient to a practitioner, ensuring all related collections are updated
 * @param {string} patientId - The ID of the patient
 * @param {string} practitionerId - The ID of the practitioner
 * @param {object} patientData - Optional additional patient data
 * @returns {Promise<void>}
 */
export const assignPatientToPractitioner = async (patientId, practitionerId, patientData = {}) => {
  try {
    // Create a batch to ensure atomicity of the operation
    const batch = writeBatch(db);

    // Get current timestamp
    const timestamp = serverTimestamp();

    // 1. Update the patient document with practitioner reference
    const patientRef = doc(db, `patients/${patientId}`);
    const patientDoc = await getDoc(patientRef);

    if (patientDoc.exists()) {
      batch.update(patientRef, {
        practitionerId: practitionerId,
        lastUpdate: timestamp,
        ...patientData
      });
    } else {
      batch.set(patientRef, {
        practitionerId: practitionerId,
        createdAt: timestamp,
        lastUpdate: timestamp,
        ...patientData
      });
    }

    // 2. Create/update reference in practitioner's patients subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);

    // Get patient name and email from users_roles if available
    const userRoleRef = doc(db, `users_roles/${patientId}`);
    const userRoleDoc = await getDoc(userRoleRef);

    const userData = userRoleDoc.exists() ? userRoleDoc.data() : {};

    batch.set(practitionerPatientRef, {
      name: userData.name || patientData.name || '',
      email: userData.email || patientData.email || '',
      lastUpdate: timestamp
    }, { merge: true });

    // 3. Commit the batch
    await batch.commit();

    console.log(`✅ Successfully assigned patient ${patientId} to practitioner ${practitionerId}`);
    return true;
  } catch (error) {
    console.error('❌ Error assigning patient to practitioner:', error);
    throw error;
  }
};

/**
 * Removes a patient from a practitioner
 * @param {string} patientId - The ID of the patient
 * @param {string} practitionerId - The ID of the practitioner
 * @returns {Promise<void>}
 */
export const removePatientFromPractitioner = async (patientId, practitionerId) => {
  try {
    // Create a batch to ensure atomicity of the operation
    const batch = writeBatch(db);

    // 1. Update the patient document to remove practitioner reference
    const patientRef = doc(db, `patients/${patientId}`);
    batch.update(patientRef, {
      practitionerId: null,
      lastUpdate: serverTimestamp()
    });

    // 2. Remove reference from practitioner's patients subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);
    batch.delete(practitionerPatientRef);

    // 3. Commit the batch
    await batch.commit();

    console.log(`✅ Successfully removed patient ${patientId} from practitioner ${practitionerId}`);
    return true;
  } catch (error) {
    console.error('❌ Error removing patient from practitioner:', error);
    throw error;
  }
};

/**
 * Gets all patients assigned to a practitioner
 * @param {string} practitionerId - The ID of the practitioner
 * @returns {Promise<Array>} - Array of patient documents
 */
export const getPractitionerPatients = async (practitionerId) => {
  try {
    // First, check if the practitioner document exists
    const practitionerRef = doc(db, `practitioners/${practitionerId}`);
    let practitionerDoc;

    try {
      practitionerDoc = await getDoc(practitionerRef);
    } catch (error) {
      console.warn('⚠️ Could not get practitioner document:', error);
      // Continue even if we can't get the practitioner document
    }

    // If practitioner document doesn't exist, try to create it
    if (!practitionerDoc || !practitionerDoc.exists()) {
      try {
        // Get user role data to verify this is a practitioner
        const userRoleRef = doc(db, `users_roles/${practitionerId}`);
        const userRoleDoc = await getDoc(userRoleRef);

        if (userRoleDoc.exists() && userRoleDoc.data().role === 'practitioner') {
          // Create the practitioner document
          await setDoc(practitionerRef, {
            createdAt: serverTimestamp(),
            lastUpdate: serverTimestamp(),
            userId: practitionerId,
            name: userRoleDoc.data().name || 'Unknown Practitioner'
          });
          console.log(`✅ Created practitioner document for ${practitionerId}`);
        } else {
          console.warn(`⚠️ User ${practitionerId} is not a practitioner`);
        }
      } catch (createError) {
        console.warn('⚠️ Could not create practitioner document:', createError);
        // Continue even if we can't create the practitioner document
      }
    }

    // Try multiple approaches to get patients, with fallbacks:
    let patients = [];

    // Approach 1: Try to get from the practitioners/{id}/patients subcollection
    try {
      const practitionerPatientsRef = collection(db, `practitioners/${practitionerId}/patients`);
      const practitionerPatientsSnapshot = await getDocs(practitionerPatientsRef);

      if (!practitionerPatientsSnapshot.empty) {
        practitionerPatientsSnapshot.forEach((doc) => {
          patients.push({
            id: doc.id,
            ...doc.data()
          });
        });

        if (patients.length > 0) {
          return patients; // Return if we found patients
        }
      }
    } catch (subError) {
      console.warn('⚠️ Could not get patients from practitioner subcollection:', subError);
      // Continue to the next approach
    }

    // Approach 2: Query patients collection for all patients with this practitionerId
    try {
      const patientsRef = collection(db, 'patients');
      const q = query(
        patientsRef,
        where('practitionerId', '==', practitionerId)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        patients = []; // Reset the array
        querySnapshot.forEach((doc) => {
          patients.push({
            id: doc.id,
            ...doc.data()
          });
        });
      }
    } catch (queryError) {
      console.warn('⚠️ Could not query patients collection:', queryError);
      // Continue to the next approach if this fails
    }

    return patients;
  } catch (error) {
    console.error('❌ Error getting practitioner patients:', error);
    return []; // Return empty array instead of throwing
  }
};

/**
 * Gets a patient's health data with proper linking to related collections
 * @param {string} patientId - The ID of the patient
 * @returns {Promise<Object>} - Object containing patient health data
 */
export const getPatientHealthData = async (patientId) => {
  try {
    // Get patient profile
    const profileRef = doc(db, `patients/${patientId}/profile/metrics`);
    const profileDoc = await getDoc(profileRef);

    // Get recent readings
    const readingsRef = collection(db, `patients/${patientId}/readings`);
    const readingsQuery = query(readingsRef, where('timestamp', '>=', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)));
    const readingsSnapshot = await getDocs(readingsQuery);

    const readings = [];
    readingsSnapshot.forEach((doc) => {
      readings.push({
        id: doc.id,
        ...doc.data()
      });
    });

    // Get health summary
    const summaryRef = doc(db, `patients/${patientId}/health_summary/latest`);
    const summaryDoc = await getDoc(summaryRef);

    // Return combined data
    return {
      profile: profileDoc.exists() ? profileDoc.data() : null,
      readings: readings,
      summary: summaryDoc.exists() ? summaryDoc.data() : null
    };
  } catch (error) {
    console.error('❌ Error getting patient health data:', error);
    throw error;
  }
};

/**
 * Request to link with a patient using their email
 * @param {string} practitionerId - The ID of the practitioner
 * @param {string} patientEmail - The email of the patient
 * @returns {Promise<Object>} - Object containing request result
 */
export const requestPatientLinking = async (practitionerId, patientEmail) => {
  try {
    // Debug log to see what's being received
    console.log('Helper received practitionerId:', practitionerId);
    console.log('Helper received patientEmail:', patientEmail);
    console.log('Helper received patientEmail type:', typeof patientEmail);

    // Validate inputs
    if (!practitionerId) {
      console.error('Invalid practitionerId:', practitionerId);
      return {
        success: false,
        error: 'Practitioner ID is required'
      };
    }

    if (!patientEmail || typeof patientEmail !== 'string' || patientEmail.trim() === '') {
      console.error('Invalid email in helper function:', patientEmail);
      return {
        success: false,
        error: 'Patient email is required'
      };
    }

    // Ensure email is trimmed and lowercase
    const cleanedEmail = patientEmail.trim().toLowerCase();
    console.log('Using cleaned email for query:', cleanedEmail);

    // 1. Check if patient exists with this email
    const usersRef = collection(db, 'users_roles');
    const q = query(usersRef, where('email', '==', cleanedEmail));
    const querySnapshot = await getDocs(q);

    let patientId = null;
    let patientExists = false;
    let patientData = null;

    // Find the patient document
    if (!querySnapshot.empty) {
      for (const doc of querySnapshot.docs) {
        const data = doc.data();
        if (data.role === 'patient') {
          patientExists = true;
          patientId = doc.id;
          patientData = data;
          break;
        }
      }

      // Check if patient is already linked to a practitioner
      if (patientExists && patientData && patientData.practitionerId) {
        if (patientData.practitionerId === practitionerId) {
          throw new Error('This patient is already linked to you');
        } else {
          throw new Error('This patient is already linked to another practitioner');
        }
      }
    }

    // 2. Get practitioner info
    const practitionerRef = doc(db, 'users_roles', practitionerId);
    let practitionerData = { name: 'Unknown Practitioner' };

    try {
      const practitionerDoc = await getDoc(practitionerRef);
      if (practitionerDoc.exists()) {
        practitionerData = practitionerDoc.data();
      } else {
        console.warn('Practitioner profile not found, using default name');
      }
    } catch (practitionerError) {
      console.warn('Error getting practitioner data:', practitionerError);
      // Continue with default practitioner name
    }

    // 3. Check if a request already exists for this patient email
    try {
      const existingRequestsRef = collection(db, 'patient_requests');
      const existingQ = query(
        existingRequestsRef,
        where('practitionerId', '==', practitionerId),
        where('patientEmail', '==', cleanedEmail),
        where('status', '==', 'pending')
      );

      const existingRequests = await getDocs(existingQ);

      if (!existingRequests.empty) {
        return {
          success: true,
          requestId: existingRequests.docs[0].id,
          patientExists,
          message: 'A pending request already exists for this patient'
        };
      }
    } catch (existingRequestError) {
      console.warn('Error checking existing requests:', existingRequestError);
      // Continue to create a new request
    }

    // 4. Create a request in the patient_requests collection
    const requestRef = doc(collection(db, 'patient_requests'));
    await setDoc(requestRef, {
      patientEmail: cleanedEmail,
      patientId: patientId, // Will be null if patient doesn't exist yet
      practitionerId: practitionerId,
      practitionerName: practitionerData.name || 'Unknown Practitioner',
      status: 'pending',
      createdAt: serverTimestamp(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      requestType: 'practitioner_initiated'
    });

    // 5. If patient exists, create a notification (this would be better handled by a Cloud Function)
    if (patientExists && patientId) {
      try {
        // First check if the patient document exists
        const patientDocRef = doc(db, `patients/${patientId}`);
        let patientDocExists = false;

        try {
          const patientDoc = await getDoc(patientDocRef);
          patientDocExists = patientDoc.exists();
        } catch (patientDocError) {
          console.warn('Error checking patient document:', patientDocError);
        }

        // Create patient document if it doesn't exist
        if (!patientDocExists) {
          try {
            await setDoc(patientDocRef, {
              createdAt: serverTimestamp(),
              lastUpdate: serverTimestamp(),
              email: cleanedEmail,
              name: patientData?.name || 'Unknown Patient'
            });
          } catch (createPatientError) {
            console.warn('Error creating patient document:', createPatientError);
          }
        }

        // Create notification
        const notificationRef = doc(collection(db, `patients/${patientId}/notifications`));
        await setDoc(notificationRef, {
          type: 'practitioner_request',
          title: 'New Practitioner Request',
          message: `${practitionerData.name || 'A practitioner'} has requested to link with your account`,
          requestId: requestRef.id,
          practitionerId: practitionerId,
          practitionerName: practitionerData.name || 'Unknown Practitioner',
          read: false,
          createdAt: serverTimestamp()
        });
      } catch (notificationError) {
        console.error('❌ Error creating notification:', notificationError);
        // Continue even if notification creation fails
      }
    }

    // 6. Return success response
    return {
      success: true,
      requestId: requestRef.id,
      patientExists
    };
  } catch (error) {
    console.error('❌ Error requesting patient linking:', error);
    // Return error object instead of throwing
    return {
      success: false,
      error: error.message || 'Failed to send patient linking request'
    };
  }
};

/**
 * Get all patient linking requests for a practitioner
 * @param {string} practitionerId - The ID of the practitioner
 * @returns {Promise<Array>} - Array of request documents
 */
export const getPatientLinkingRequests = async (practitionerId) => {
  try {
    // Query patient_requests collection for requests initiated by this practitioner
    const requestsRef = collection(db, 'patient_requests');

    // Use a simpler query first to avoid index issues
    const q = query(
      requestsRef,
      where('practitionerId', '==', practitionerId)
    );

    const querySnapshot = await getDocs(q);

    // Map the documents to an array of request data
    const requests = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Only include practitioner-initiated requests
      if (data.requestType === 'practitioner_initiated') {
        requests.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || null,
          updatedAt: data.updatedAt?.toDate() || null
        });
      }
    });

    return requests;
  } catch (error) {
    console.error('❌ Error getting patient linking requests:', error);
    // Return empty array instead of throwing to prevent UI errors
    return [];
  }
};

/**
 * Cancel a patient linking request
 * @param {string} requestId - The ID of the request
 * @returns {Promise<boolean>} - True if successful
 */
export const cancelPatientLinkingRequest = async (requestId) => {
  try {
    const requestRef = doc(db, `patient_requests/${requestId}`);
    await updateDoc(requestRef, {
      status: 'cancelled',
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('❌ Error cancelling patient linking request:', error);
    throw error;
  }
};
