import { db, auth } from '@/plugins/firebase/firebase';
import {
  doc,
  deleteDoc,
  collection,
  query,
  where,
  getDocs,
  writeBatch,
  getDoc
} from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
// We now use a Cloud Function to delete Firebase Auth users

/**
 * Deletes a user's documents from the system and the Firebase Auth user
 * This includes:
 * 1. Deleting the user's documents from Firestore (users_roles, patients/practitioners collections)
 * 2. Cleaning up related documents and references
 * 3. Deleting the Firebase Auth user via Cloud Function
 *
 * @param {string} userId - The ID of the user to delete
 * @param {boolean} isAdmin - Whether the caller is an admin (optional)
 * @returns {Promise<boolean>} - True if successful, throws error otherwise
 */
export const deleteUser = async (userId, isAdmin = false) => {
  try {
    // 1. Delete Firestore documents first
    await deleteFirestoreDocuments(userId);

    // 2. Delete the Firebase Auth user via Cloud Function
    // This will trigger the onUserDeleted Cloud Function to clean up any remaining Firestore data
    const functions = getFunctions();
    const deleteAuthUserFn = httpsCallable(functions, 'deleteAuthUser');

    try {
      const result = await deleteAuthUserFn({ userId });
      console.log('Auth user deletion result:', result.data);
    } catch (authError) {
      console.error('Error deleting Auth user:', authError);
      // If this is a self-delete operation, we can try to delete the user directly
      if (!isAdmin && auth.currentUser && auth.currentUser.uid === userId) {
        try {
          await auth.currentUser.delete();
          console.log('User successfully deleted their own account');
        } catch (selfDeleteError) {
          console.error('Error during self-delete:', selfDeleteError);
          throw new Error('Failed to delete your account. You may need to re-authenticate before deleting your account.');
        }
      } else {
        // For admin operations, we need to inform that the Firestore data was deleted but not the Auth user
        console.warn('Firestore data deleted but Auth user remains');
      }
    }

    console.log(`✅ User ${userId} successfully deleted from the system`);
    return true;
  } catch (error) {
    console.error('❌ Error deleting user:', error);
    throw error;
  }
};

/**
 * Helper function to delete Firestore documents for a user
 * @param {string} userId - The ID of the user to delete
 * @returns {Promise<void>}
 */
const deleteFirestoreDocuments = async (userId) => {
  try {
    const batch = writeBatch(db);

    // 1. Check if user exists in Firestore
    const userRoleRef = doc(db, 'users_roles', userId);
    const userRoleDoc = await getDoc(userRoleRef);

    if (!userRoleDoc.exists()) {
      console.warn(`User document not found for ${userId}`);
      // Continue anyway to clean up any other references
    } else {
      const userData = userRoleDoc.data();
      const userRole = userData.role;

      // 2. Delete from users_roles collection
      batch.delete(userRoleRef);

      // 3. Delete role-specific documents
      if (userRole === 'patient') {
        // Delete from patients collection
        const patientRef = doc(db, 'patients', userId);
        batch.delete(patientRef);

        // If patient has a practitioner, remove the reference
        if (userData.practitionerId) {
          const practitionerPatientRef = doc(db, `practitioners/${userData.practitionerId}/patients/${userId}`);
          batch.delete(practitionerPatientRef);
        }

        // Delete patient readings if they exist
        try {
          const readingsRef = collection(db, `patients/${userId}/readings`);
          const readingsSnapshot = await getDocs(readingsRef);
          readingsSnapshot.forEach(doc => {
            batch.delete(doc.ref);
          });
        } catch (error) {
          console.warn('No readings found or error accessing readings:', error);
        }

        // Delete health summary if it exists
        try {
          const healthSummaryRef = doc(db, `patients/${userId}/health_summary/latest`);
          batch.delete(healthSummaryRef);
        } catch (error) {
          console.warn('No health summary found or error accessing it:', error);
        }

        // Delete daily metrics if they exist
        try {
          const dailyMetricsRef = collection(db, `patients/${userId}/daily_metrics`);
          const dailyMetricsSnapshot = await getDocs(dailyMetricsRef);
          dailyMetricsSnapshot.forEach(doc => {
            batch.delete(doc.ref);
          });
        } catch (error) {
          console.warn('No daily metrics found or error accessing them:', error);
        }
      } else if (userRole === 'practitioner') {
        // Delete from practitioners collection
        const practitionerRef = doc(db, 'practitioners', userId);
        batch.delete(practitionerRef);

        // Update patients that were assigned to this practitioner
        const patientsRef = collection(db, 'patients');
        const q = query(patientsRef, where('practitionerId', '==', userId));
        const patientsSnapshot = await getDocs(q);

        patientsSnapshot.forEach(patientDoc => {
          batch.update(patientDoc.ref, {
            practitionerId: null
          });
        });

        // Delete practitioner's patients subcollection
        try {
          const practitionerPatientsRef = collection(db, `practitioners/${userId}/patients`);
          const practitionerPatientsSnapshot = await getDocs(practitionerPatientsRef);
          practitionerPatientsSnapshot.forEach(doc => {
            batch.delete(doc.ref);
          });
        } catch (error) {
          console.warn('No patients subcollection found or error accessing it:', error);
        }
      }
    }

    // Delete user metrics if they exist
    try {
      const metricsRef = doc(db, `user_metrics/${userId}`);
      batch.delete(metricsRef);
    } catch (error) {
      console.warn('No user metrics found or error accessing them:', error);
    }

    // Delete patient requests if they exist
    try {
      const patientRequestsRef = collection(db, 'patient_requests');
      const patientRequestsQ = query(patientRequestsRef, where('patientId', '==', userId));
      const patientRequestsSnapshot = await getDocs(patientRequestsQ);
      patientRequestsSnapshot.forEach(doc => {
        batch.delete(doc.ref);
      });
    } catch (error) {
      console.warn('Error cleaning up patient requests:', error);
    }

    // Delete practitioner requests if they exist
    try {
      const practitionerRequestsRef = collection(db, 'patient_requests');
      const practitionerRequestsQ = query(practitionerRequestsRef, where('practitionerId', '==', userId));
      const practitionerRequestsSnapshot = await getDocs(practitionerRequestsQ);
      practitionerRequestsSnapshot.forEach(doc => {
        batch.delete(doc.ref);
      });
    } catch (error) {
      console.warn('Error cleaning up practitioner requests:', error);
    }

    // 4. Commit all the Firestore deletions
    await batch.commit();
    console.log(`User ${userId} Firestore documents deleted.`);
  } catch (error) {
    console.error('Error deleting Firestore documents:', error);
    throw error;
  }
};
