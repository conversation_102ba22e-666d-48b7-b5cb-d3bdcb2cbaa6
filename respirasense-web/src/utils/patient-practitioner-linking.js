import { db, auth } from '@/plugins/firebase/firebase';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';

/**
 * Comprehensive function to establish a patient-practitioner link
 * This ensures all necessary documents are updated consistently
 *
 * @param {string} patientId - The patient's user ID
 * @param {string} practitionerId - The practitioner's user ID
 * @param {string} requestId - Optional ID of the linking request to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const establishPatientPractitionerLink = async (patientId, practitionerId, requestId = null) => {
  try {
    console.log('Establishing patient-practitioner link:', { patientId, practitionerId, requestId });

    // Validate inputs
    if (!patientId || !practitionerId) {
      console.error('Missing required parameters:', { patientId, practitionerId });
      return {
        success: false,
        error: 'Missing required parameters'
      };
    }

    // Create a batch to ensure atomicity
    const batch = writeBatch(db);
    const timestamp = serverTimestamp();

    // 1. Get patient and practitioner data
    const patientRef = doc(db, `patients/${patientId}`);
    const patientDoc = await getDoc(patientRef);

    const practitionerRef = doc(db, `practitioners/${practitionerId}`);
    const practitionerDoc = await getDoc(practitionerRef);

    const patientRoleRef = doc(db, `users_roles/${patientId}`);
    const patientRoleDoc = await getDoc(patientRoleRef);

    const practitionerRoleRef = doc(db, `users_roles/${practitionerId}`);
    const practitionerRoleDoc = await getDoc(practitionerRoleRef);

    console.log('Retrieved user documents:', {
      patientExists: patientDoc.exists(),
      practitionerExists: practitionerDoc.exists(),
      patientRoleExists: patientRoleDoc.exists(),
      practitionerRoleExists: practitionerRoleDoc.exists()
    });

    // 2. Validate that both users exist and have correct roles
    if (!patientRoleDoc.exists()) {
      console.error('Patient user not found:', patientId);
      return {
        success: false,
        error: 'Patient user not found'
      };
    }

    if (!practitionerRoleDoc.exists()) {
      console.error('Practitioner user not found:', practitionerId);
      return {
        success: false,
        error: 'Practitioner user not found'
      };
    }

    const patientRoleData = patientRoleDoc.data();
    const practitionerRoleData = practitionerRoleDoc.data();

    console.log('User roles:', {
      patientRole: patientRoleData.role,
      practitionerRole: practitionerRoleData.role
    });

    if (patientRoleData.role !== 'patient') {
      console.error('User is not a patient:', patientId, patientRoleData.role);
      return {
        success: false,
        error: 'User is not a patient'
      };
    }

    if (practitionerRoleData.role !== 'practitioner') {
      console.error('User is not a practitioner:', practitionerId, practitionerRoleData.role);
      return {
        success: false,
        error: 'User is not a practitioner'
      };
    }

    // 3. Update patient document with practitioner reference
    if (!patientDoc.exists()) {
      // Create patient document if it doesn't exist
      console.log('Creating new patient document for:', patientId);
      batch.set(patientRef, {
        email: patientRoleData.email,
        name: patientRoleData.name || 'Patient',
        practitionerId: practitionerId,
        updatedAt: timestamp,
        createdAt: timestamp
      });
    } else {
      // Update existing patient document
      console.log('Updating existing patient document for:', patientId);
      batch.update(patientRef, {
        practitionerId: practitionerId,
        updatedAt: timestamp
      });
    }

    // 4. Create/update entry in practitioner's patients subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);

    // Include denormalized patient data to reduce cross-collection queries
    console.log('Adding patient to practitioner\'s patients subcollection:', {
      practitionerId,
      patientId,
      patientName: patientRoleData.name || 'Patient'
    });

    batch.set(practitionerPatientRef, {
      name: patientRoleData.name || 'Patient',
      email: patientRoleData.email,
      photoURL: patientRoleData.photoURL || null,
      lastUpdate: timestamp
    }, { merge: true });

    // 5. Create practitioner document if it doesn't exist
    if (!practitionerDoc.exists()) {
      console.log('Creating new practitioner document for:', practitionerId);
      batch.set(practitionerRef, {
        email: practitionerRoleData.email,
        name: practitionerRoleData.name || 'Practitioner',
        updatedAt: timestamp,
        createdAt: timestamp
      });
    }

    // 6. Update the request status if a requestId was provided
    if (requestId) {
      console.log('Updating request status to approved:', requestId);
      const requestRef = doc(db, `patient_requests/${requestId}`);
      batch.update(requestRef, {
        status: 'approved',
        updatedAt: timestamp
      });
    }

    // 7. Commit all changes as a single atomic operation
    console.log('Committing batch operation...');
    try {
      await batch.commit();
      console.log('Batch operation successful');
      return {
        success: true,
        message: 'Patient-practitioner link established successfully'
      };
    } catch (batchError) {
      console.error('Error committing batch operation:', batchError);
      return {
        success: false,
        error: `Batch operation failed: ${batchError.message}`
      };
    }
  } catch (error) {
    console.error('Error establishing patient-practitioner link:', error);
    return {
      success: false,
      error: error.message || 'Failed to establish link'
    };
  }
};

/**
 * Checks if a practitioner is linked to a patient
 *
 * @param {string} patientId - The patient's user ID
 * @param {string} practitionerId - The practitioner's user ID
 * @returns {Promise<boolean>} - True if linked, false otherwise
 */
export const isPractitionerLinkedToPatient = async (patientId, practitionerId) => {
  try {
    if (!patientId || !practitionerId) {
      console.error('Missing required parameters:', { patientId, practitionerId });
      return false;
    }

    // Check method 1: Look in patient document
    const patientRef = doc(db, `patients/${patientId}`);
    const patientDoc = await getDoc(patientRef);

    if (patientDoc.exists() && patientDoc.data().practitionerId === practitionerId) {
      return true;
    }

    // Check method 2: Look in practitioner's patients subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);
    const practitionerPatientDoc = await getDoc(practitionerPatientRef);

    return practitionerPatientDoc.exists();
  } catch (error) {
    console.error('Error checking practitioner-patient link:', error);
    return false;
  }
};

/**
 * Removes a patient-practitioner link
 *
 * @param {string} patientId - The patient's user ID
 * @param {string} practitionerId - The practitioner's user ID
 * @returns {Promise<Object>} - Result of the operation
 */
export const removePatientPractitionerLink = async (patientId, practitionerId) => {
  try {
    // Validate inputs
    if (!patientId || !practitionerId) {
      return {
        success: false,
        error: 'Missing required parameters'
      };
    }

    // Create a batch to ensure atomicity
    const batch = writeBatch(db);
    const timestamp = serverTimestamp();

    // 1. Update patient document to remove practitioner reference
    const patientRef = doc(db, `patients/${patientId}`);
    const patientDoc = await getDoc(patientRef);

    if (patientDoc.exists()) {
      const patientData = patientDoc.data();

      // Only remove if this practitioner is linked
      if (patientData.practitionerId === practitionerId) {
        batch.update(patientRef, {
          practitionerId: null,
          updatedAt: timestamp
        });
      }
    }

    // 2. Remove entry from practitioner's patients subcollection
    const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${patientId}`);
    batch.delete(practitionerPatientRef);

    // 3. Commit all changes as a single atomic operation
    await batch.commit();

    return {
      success: true,
      message: 'Patient-practitioner link removed successfully'
    };
  } catch (error) {
    console.error('Error removing patient-practitioner link:', error);
    return {
      success: false,
      error: error.message || 'Failed to remove link'
    };
  }
};
