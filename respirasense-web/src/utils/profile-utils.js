import { auth, db } from '@/plugins/firebase/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import defaultUserIcon from '@/assets/DefaultUserIcon.webp';

/**
 * Get the user's profile photo URL with enhanced resolution for Google photos
 * @param {Object} user - Firebase user object or user data with photoURL
 * @param {string} userId - Optional user ID if user object is not provided
 * @returns {Promise<string>} - URL to the user's profile photo or default icon
 */
export const getProfilePhotoUrl = async (user, userId = null) => {
  try {
    // If user object is provided
    if (user) {
      // If user has a photoURL
      if (user.photoURL) {
        // For Google photos, ensure we get a reliable URL with higher resolution
        if (user.photoURL.includes('googleusercontent.com')) {
          // Replace the default size parameter with a larger one
          // First, try to extract the base URL without size parameters
          const baseUrl = user.photoURL.split('=')[0];
          if (baseUrl) {
            // Add a specific size and ensure we get a fresh version with a cache buster
            return `${baseUrl}=s400-c-k-no?${Date.now()}`;
          }
          // If we can't extract the base URL, try the standard replacement
          return user.photoURL.replace(/=s\d+-c/, `=s400-c-k-no?${Date.now()}`);
        }
        // For other providers, use the URL as is
        return user.photoURL;
      }

      // If user has uid but no photoURL, try to get it from Firestore
      if (user.uid) {
        const userDocRef = doc(db, 'users_roles', user.uid);
        const userDoc = await getDoc(userDocRef);
        if (userDoc.exists() && userDoc.data().photoURL) {
          return userDoc.data().photoURL;
        }
      }
    }
    // If only userId is provided, try to get user data from Firestore
    else if (userId) {
      const userDocRef = doc(db, 'users_roles', userId);
      const userDoc = await getDoc(userDocRef);
      if (userDoc.exists() && userDoc.data().photoURL) {
        return userDoc.data().photoURL;
      }

      // If user exists but has no photoURL, check if they have a role-specific document
      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.role === 'patient') {
          const patientDocRef = doc(db, `patients/${userId}`);
          const patientDoc = await getDoc(patientDocRef);
          if (patientDoc.exists() && patientDoc.data().photoURL) {
            return patientDoc.data().photoURL;
          }
        } else if (userData.role === 'practitioner') {
          const practitionerDocRef = doc(db, `practitioners/${userId}`);
          const practitionerDoc = await getDoc(practitionerDocRef);
          if (practitionerDoc.exists() && practitionerDoc.data().photoURL) {
            return practitionerDoc.data().photoURL;
          }
        }
      }
    }

    // If no photo URL found, return default icon
    return defaultUserIcon;
  } catch (error) {
    console.error('Error getting profile photo URL:', error);
    return defaultUserIcon;
  }
};

/**
 * Update the user's profile photo URL in Firestore
 * @param {string} userId - User ID
 * @param {string} photoURL - New photo URL
 * @returns {Promise<boolean>} - Success status
 */
export const updateProfilePhotoUrl = async (userId, photoURL) => {
  try {
    if (!userId || !photoURL) return false;

    // Update users_roles document
    const userDocRef = doc(db, 'users_roles', userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      await updateDoc(userDocRef, { photoURL });

      // Update role-specific document if it exists
      const userData = userDoc.data();
      if (userData.role === 'patient') {
        const patientDocRef = doc(db, `patients/${userId}`);
        const patientDoc = await getDoc(patientDocRef);
        if (patientDoc.exists()) {
          await updateDoc(patientDocRef, { photoURL });
        }
      } else if (userData.role === 'practitioner') {
        const practitionerDocRef = doc(db, `practitioners/${userId}`);
        const practitionerDoc = await getDoc(practitionerDocRef);
        if (practitionerDoc.exists()) {
          await updateDoc(practitionerDocRef, { photoURL });
        }
      }

      return true;
    }

    return false;
  } catch (error) {
    console.error('Error updating profile photo URL:', error);
    return false;
  }
};

/**
 * Handle image loading errors by replacing with default icon
 * @param {Event} event - Image error event
 */
export const handleImageError = (event) => {
  event.target.src = defaultUserIcon;
};

/**
 * Sync the current user's profile photo from auth to Firestore
 * This should be called after login or when the user's profile is updated
 * @returns {Promise<boolean>} - Success status
 */
export const syncUserProfilePhoto = async () => {
  try {
    const user = auth.currentUser;
    if (!user) return false;

    // If user has a photoURL from auth provider, update it in Firestore
    if (user.photoURL) {
      let photoURL = user.photoURL;

      // For Google photos, ensure we get a reliable URL with higher resolution
      if (photoURL.includes('googleusercontent.com')) {
        // First, try to extract the base URL without size parameters
        const baseUrl = photoURL.split('=')[0];
        if (baseUrl) {
          // Add a specific size and ensure we get a fresh version with a cache buster
          photoURL = `${baseUrl}=s400-c-k-no`;
        } else {
          // If we can't extract the base URL, try the standard replacement
          photoURL = photoURL.replace(/=s\d+-c/, '=s400-c-k-no');
        }
      }

      await updateProfilePhotoUrl(user.uid, photoURL);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error syncing user profile photo:', error);
    return false;
  }
};
