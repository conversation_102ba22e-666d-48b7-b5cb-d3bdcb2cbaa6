import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';

/**
 * Creates an audit log entry in Firestore
 *
 * @param {Object} logData - The audit log data
 * @param {string} logData.action - The action performed (e.g., 'create_user', 'update_settings')
 * @param {string} logData.userId - The ID of the user the action was performed on (if applicable)
 * @param {string} logData.performedBy - The ID of the user who performed the action
 * @param {Object} logData.details - Additional details about the action
 * @returns {Promise<DocumentReference>} - A promise that resolves with the document reference
 */
export async function createAuditLog({ action, userId, performedBy, details = {} }) {
  try {
    if (!action || !performedBy) {
      console.warn('Action and performedBy are required for audit logs');
      return null;
    }

    const auditLogRef = collection(db, 'audit_logs');

    const logEntry = {
      action,
      userId: userId || null,
      performedBy,
      details,
      timestamp: serverTimestamp(),
      ipAddress: null // This would be set by a server-side function
    };

    return await addDoc(auditLogRef, logEntry);
  } catch (error) {
    console.error('Error creating audit log:', error);
    // Don't throw the error - audit logging should not break the main functionality
    return null;
  }
}

/**
 * Formats an audit log action into a human-readable string
 *
 * @param {string} action - The audit log action
 * @returns {string} - A human-readable description of the action
 */
export const formatAuditAction = (action) => {
  const actionMap = {
    'login': 'Logged in',
    'logout': 'Logged out',
    'create_user': 'Created user',
    'update_user': 'Updated user',
    'delete_user': 'Deleted user',
    'create_practitioner': 'Created practitioner',
    'update_practitioner': 'Updated practitioner',
    'update_settings': 'Updated system settings',
    'reset_password': 'Reset password',
    'assign_patient': 'Assigned patient',
    'remove_patient': 'Removed patient',
    'view_patient': 'Viewed patient data',
    'export_data': 'Exported data'
  };

  return actionMap[action] || action.replace(/_/g, ' ');
};

/**
 * Gets a FontAwesome icon class for an audit log action
 *
 * @param {string} action - The audit log action
 * @returns {string} - A FontAwesome icon class
 */
export const getAuditActionIcon = (action) => {
  const iconMap = {
    'login': 'fas fa-sign-in-alt',
    'logout': 'fas fa-sign-out-alt',
    'create_user': 'fas fa-user-plus',
    'update_user': 'fas fa-user-edit',
    'delete_user': 'fas fa-user-minus',
    'create_practitioner': 'fas fa-user-md',
    'update_practitioner': 'fas fa-user-edit',
    'update_settings': 'fas fa-cog',
    'reset_password': 'fas fa-key',
    'assign_patient': 'fas fa-user-plus',
    'remove_patient': 'fas fa-user-minus',
    'view_patient': 'fas fa-eye',
    'export_data': 'fas fa-file-export'
  };

  return iconMap[action] || 'fas fa-history';
};

export default {
  createAuditLog,
  formatAuditAction,
  getAuditActionIcon
};