/**
 * Notification utility functions
 * Uses @kyvg/vue3-notification package
 */
import { notify as vueNotify } from '@kyvg/vue3-notification';

/**
 * Show a notification
 * @param {Object} options - Notification options
 * @param {string} options.type - Notification type ('success', 'error', 'warning', 'info')
 * @param {string} options.title - Notification title (optional)
 * @param {string} options.text - Notification text content
 * @param {number} options.duration - Duration in milliseconds (optional, default: 5000)
 */
export const notify = (options) => {
  vueNotify({
    type: options.type || 'info',
    title: options.title || getDefaultTitle(options.type),
    text: options.text,
    duration: options.duration || 5000
  });
};

/**
 * Show a success notification
 * @param {string} message - Notification message
 * @param {string} title - Notification title (optional)
 */
export const notifySuccess = (message, title) => {
  notify({
    type: 'success',
    title: title || 'Success',
    text: message
  });
};

/**
 * Show an error notification
 * @param {string} message - Notification message
 * @param {string} title - Notification title (optional)
 */
export const notifyError = (message, title) => {
  notify({
    type: 'error',
    title: title || 'Error',
    text: message
  });
};

/**
 * Show a warning notification
 * @param {string} message - Notification message
 * @param {string} title - Notification title (optional)
 */
export const notifyWarning = (message, title) => {
  notify({
    type: 'warning',
    title: title || 'Warning',
    text: message
  });
};

/**
 * Show an info notification
 * @param {string} message - Notification message
 * @param {string} title - Notification title (optional)
 */
export const notifyInfo = (message, title) => {
  notify({
    type: 'info',
    title: title || 'Information',
    text: message
  });
};

/**
 * Get default title based on notification type
 * @param {string} type - Notification type
 * @returns {string} Default title
 * @private
 */
function getDefaultTitle(type) {
  switch (type) {
    case 'success':
      return 'Success';
    case 'error':
      return 'Error';
    case 'warning':
      return 'Warning';
    case 'info':
      return 'Information';
    default:
      return '';
  }
}
