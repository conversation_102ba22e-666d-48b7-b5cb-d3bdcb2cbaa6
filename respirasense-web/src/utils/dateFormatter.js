/**
 * Converts various timestamp formats to a Date object
 *
 * @param {Date|Object|string|number} timestamp - The timestamp to convert
 * @returns {Date|null} - The converted Date object or null if invalid
 */
const convertToDate = (timestamp) => {
  if (!timestamp) return null;

  // Handle Firestore Timestamp
  if (timestamp && typeof timestamp.toDate === 'function') {
    return timestamp.toDate();
  }

  // Handle Date object
  if (timestamp instanceof Date) {
    return timestamp;
  }

  // Handle numeric timestamp
  if (typeof timestamp === 'number') {
    return new Date(timestamp);
  }

  // Handle string timestamp
  try {
    const date = new Date(timestamp);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error converting timestamp to date:', error);
    return null;
  }
};

/**
 * Formats a date into a human-readable string
 *
 * @param {Date|Object|string|number} timestamp - The timestamp to format
 * @param {Object} options - Formatting options
 * @param {boolean} options.includeTime - Whether to include the time in the formatted string
 * @param {boolean} options.shortFormat - Whether to use a shorter date format
 * @returns {string} - The formatted date string
 */
export const formatDate = (timestamp, options = {}) => {
  const date = convertToDate(timestamp);
  if (!date) return 'N/A';

  const { includeTime = false, shortFormat = false } = options;

  try {
    // Format options
    const dateFormatOptions = {
      year: 'numeric',
      month: shortFormat ? 'numeric' : 'short',
      day: 'numeric',
      hour: includeTime ? '2-digit' : undefined,
      minute: includeTime ? '2-digit' : undefined,
      hour12: true
    };

    return new Intl.DateTimeFormat('en-US', dateFormatOptions).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Error';
  }
};

/**
 * Formats a date as a time string (e.g., "3:45 PM")
 *
 * @param {Date|Object|string|number} timestamp - The timestamp to format
 * @returns {string} - The formatted time string
 */
export const formatTime = (timestamp) => {
  const date = convertToDate(timestamp);
  if (!date) return 'N/A';

  try {
    // Format options
    const timeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };

    return new Intl.DateTimeFormat('en-US', timeFormatOptions).format(date);
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Error';
  }
};

/**
 * Formats a date as a relative time string (e.g., "2 hours ago")
 *
 * @param {Date|Object|string|number} timestamp - The timestamp to format
 * @returns {string} - The relative time string
 */
export const formatRelativeTime = (timestamp) => {
  const date = convertToDate(timestamp);
  if (!date) return 'N/A';

  try {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffMonth = Math.floor(diffDay / 30);
    const diffYear = Math.floor(diffMonth / 12);

    if (diffSec < 60) {
      return diffSec <= 5 ? 'just now' : `${diffSec} seconds ago`;
    } else if (diffMin < 60) {
      return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} ${diffDay === 1 ? 'day' : 'days'} ago`;
    } else if (diffMonth < 12) {
      return `${diffMonth} ${diffMonth === 1 ? 'month' : 'months'} ago`;
    } else {
      return `${diffYear} ${diffYear === 1 ? 'year' : 'years'} ago`;
    }
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return 'Error';
  }
};

export default {
  formatDate,
  formatTime,
  formatRelativeTime
};