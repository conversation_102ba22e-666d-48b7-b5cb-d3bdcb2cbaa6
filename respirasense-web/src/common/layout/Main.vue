<template>
  <div class="main-layout">
    <div class="main-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'MainLayout',
  methods: {
    ...mapActions("auth", ["logout"]),
    async handleLogout() {
      try {
        await this.logout();
        localStorage.removeItem('userRole');
        await this.$router.push({ name: 'Logout' });
      } catch (error) {
        console.error('Logout error:', error);
        await this.$router.push({ name: 'Login' });
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.main-layout {
  min-height: 100vh;
}

.main-content {
  padding: 2rem;
}
</style>
