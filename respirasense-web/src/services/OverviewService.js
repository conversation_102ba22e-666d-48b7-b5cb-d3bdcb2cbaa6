import { db } from '@/plugins/firebase/firebase';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit,
  doc,
  getDoc 
} from 'firebase/firestore';

export default class OverviewService {
  static async getOverviewStats() {
    try {
      console.log('📊 Getting overview stats from Firestore...');
      const statsRef = doc(db, 'overview_stats', 'latest');
      const statsDoc = await getDoc(statsRef);
      
      if (!statsDoc.exists()) {
        console.warn('⚠️ No overview_stats document found');
        return {
          totalPatients: 0,
          activePatients: 0,
          criticalAlerts: 0,
          dailyReadings: 0
        };
      }

      const data = statsDoc.data();
      console.log('📊 Raw overview stats data:', data);
      return data;
    } catch (error) {
      console.error('❌ Error fetching overview stats:', error);
      throw error;
    }
  }

  static async getRecentAlerts() {
    try {
      console.log('⚠️ Fetching recent alerts...');
      const alertsRef = collection(db, 'alerts');
      const alertsQuery = query(
        alertsRef,
        where('status', '==', 'active'),
        orderBy('timestamp', 'desc'),
        limit(10)
      );
      
      const alertsSnapshot = await getDocs(alertsQuery);
      console.log('⚠️ Found alerts count:', alertsSnapshot.size);
      
      return alertsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate()
        };
      });
    } catch (error) {
      console.error('❌ Error fetching recent alerts:', error);
      throw error;
    }
  }

  static async getDailyMetrics() {
    try {
      console.log('📈 Fetching daily metrics...');
      const metricsRef = collection(db, 'daily_metrics');
      const metricsQuery = query(
        metricsRef,
        orderBy('date', 'desc'),
        limit(7)
      );
      
      const metricsSnapshot = await getDocs(metricsQuery);
      console.log('📈 Found metrics count:', metricsSnapshot.size);
      
      return metricsSnapshot.docs.map(doc => doc.data());
    } catch (error) {
      console.error('❌ Error fetching daily metrics:', error);
      throw error;
    }
  }

  static async getSystemHealth() {
    try {
      const healthRef = doc(db, 'system_health', 'current');
      const healthDoc = await getDoc(healthRef);
      return healthDoc.data() || {
        status: 'unknown',
        lastUpdate: null,
        metrics: {}
      };
    } catch (error) {
      console.error('Error fetching system health:', error);
      throw error;
    }
  }
}