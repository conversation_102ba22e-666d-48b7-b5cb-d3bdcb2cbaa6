/**
 * AI Server Service
 * Handles communication with the RespiraSense AI server running on localhost:5000
 */

const AI_SERVER_BASE_URL = 'http://localhost:5000';

class AIServerService {
  /**
   * Check if the AI server is available
   */
  async checkHealth() {
    try {
      const response = await fetch(`${AI_SERVER_BASE_URL}/health`);
      return await response.json();
    } catch (error) {
      console.error('AI Server health check failed:', error);
      throw new Error('AI Server is not available');
    }
  }

  /**
   * Get temperature reading from MLX90614 sensor
   */
  async getTemperature() {
    try {
      const response = await fetch(`${AI_SERVER_BASE_URL}/temperature`);
      if (!response.ok) {
        throw new Error(`Temperature reading failed: ${response.statusText}`);
      }
      const data = await response.json();
      return {
        temperature: data.temperature,
        timestamp: new Date(),
        sensor_type: data.sensor_type || 'mlx90614'
      };
    } catch (error) {
      console.error('Temperature reading error:', error);
      throw error;
    }
  }

  /**
   * Get pulse oximetry reading (SpO2 and heart rate)
   */
  async getOximetry() {
    try {
      const response = await fetch(`${AI_SERVER_BASE_URL}/oximeter`);
      if (!response.ok) {
        throw new Error(`Oximetry reading failed: ${response.statusText}`);
      }
      const data = await response.json();
      return {
        spo2: data.spo2,
        heartRate: data.heart_rate,
        timestamp: new Date(),
        sensor_type: data.sensor_type || 'uart_hack'
      };
    } catch (error) {
      console.error('Oximetry reading error:', error);
      throw error;
    }
  }

  /**
   * Perform spirometry test with demographics
   */
  async getSpirometry(demographics = {}) {
    try {
      const response = await fetch(`${AI_SERVER_BASE_URL}/spirometry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(demographics)
      });
      
      if (!response.ok) {
        throw new Error(`Spirometry test failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      return {
        ...data,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Spirometry test error:', error);
      throw error;
    }
  }

  /**
   * Get COPD prediction based on all collected data
   */
  async predictCOPD(patientData) {
    try {
      const response = await fetch(`${AI_SERVER_BASE_URL}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patientData)
      });
      
      if (!response.ok) {
        throw new Error(`COPD prediction failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      return {
        ...data,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('COPD prediction error:', error);
      throw error;
    }
  }

  /**
   * Prepare patient data for COPD prediction
   * Maps from user profile and readings to the format expected by the AI server
   */
  preparePatientData(profile, readings) {
    // Map gender to numeric value (assuming male=1, female=0)
    const genderMap = {
      'male': 1,
      'female': 0,
      'M': 1,
      'F': 0
    };

    return {
      age: parseInt(profile.age) || 0,
      gen: genderMap[profile.gender] || 0,
      ht: parseInt(profile.height) || 0, // height in cm
      wt: parseInt(profile.weight) || 0, // weight in kg
      smoke_hist: 0, // TODO: Add to profile
      smoke_rt: 0, // TODO: Add to profile
      fev1: readings.spirometry?.FEV1 || 0,
      fvc: readings.spirometry?.FVC || 0,
      pefr: readings.spirometry?.PEF || 0,
      hr: readings.oximetry?.heartRate || 0,
      spo2: readings.oximetry?.spo2 || 0,
      temp: readings.temperature?.temperature || 0,
      wheeze: 0, // TODO: Add symptom questionnaire
      cough: 0, // TODO: Add symptom questionnaire
      sob: 0, // TODO: Add symptom questionnaire (shortness of breath)
      sputum: 0, // TODO: Add symptom questionnaire
      pectoriloquy: 0, // TODO: Add clinical examination
      exp_biomass: 0, // TODO: Add to profile (exposure to biomass)
      exp_occ: 0, // TODO: Add to profile (occupational exposure)
      fam_hist: 0 // TODO: Add to profile (family history)
    };
  }
}

export default new AIServerService();
