// Variables
$primary: #b71540; // Dark red
$primary-light: #b71540; // Use the same dark red for consistency
$primary-subtle: rgba(183, 21, 64, 0.1); // Subtle version of primary
$danger: #b71540;
$dark-grey: #333;
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$white: #fff;
$link: #007bff;

.readings-calendar {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .view-toggle {
      display: flex;
      gap: 5px;

      .view-button {
        padding: 5px 10px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &.active {
          background: $primary-light;
          color: white;
          border-color: $primary-light;
        }

        &:hover {
          background: #f5f5f5;
          &.active {
            background: darken($primary-light, 5%);
          }
        }
      }
    }

    .navigation {
      display: flex;
      align-items: center;
      gap: 10px;

      .nav-button {
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
          background: #f5f5f5;
        }
      }

      .current-period {
        font-weight: 500;
        min-width: 150px;
        text-align: center;
      }

      .today-button {
        background: white;
        border: 1px solid $primary-light;
        color: $primary-light;
        border-radius: 4px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
        cursor: pointer;
        margin-left: 10px;

        &:hover {
          background-color: rgba($primary-light, 0.1);
        }

        &:disabled {
          opacity: 0.5;
          cursor: default;

          &:hover {
            background-color: white;
          }
        }

        i {
          font-size: 12px;
        }
      }
    }
  }

  // Year View
  .year-view {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;

    .month-mini {
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 10px;
      cursor: pointer;

      &:hover {
        background: #f9f9f9;
      }

      h4 {
        margin: 0 0 10px 0;
        text-align: center;
        font-size: 14px;
      }

      .mini-days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;

        .mini-day {
          font-size: 10px;
          text-align: center;
          padding: 2px;
          border-radius: 2px;

          &.has-reading {
            // Remove background color to show the dot indicator instead
            // background-color: rgba($primary-light, 0.2);
            // color: $primary-light;
            // font-weight: bold;
            position: relative; // For positioning the indicator
          }

          // Reading indicator dot for mini-day
          .reading-indicator {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: $primary;
            display: block;
          }

          &.current-day {
            border: 1px solid $primary-light;
          }
        }
      }
    }
  }

  // Month View
  .month-view {
    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 10px;

      .weekday {
        text-align: center;
        font-weight: 500;
        color: #666;
      }
    }

    .days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 5px;

      .day {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
        position: relative;

        &.other-month {
          color: #ccc;
        }

        &.has-reading {
          // Remove background color to show the dot indicator instead
          // background-color: $primary-light;
          // color: white;
          // font-weight: bold;
          position: relative; // For positioning the indicator
        }

        // Reading indicator dot
        .reading-indicator {
          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: $primary;
          display: block;
        }

        &.current-day {
          border: 2px solid $primary-light;
          font-weight: bold;
          color: $primary-light;
        }

        &:hover {
          background-color: #f5f5f5;
          &.has-reading {
            // Subtle hover effect for days with readings
            background-color: rgba($primary-light, 0.05);

            // Keep the indicator visible on hover
            .reading-indicator {
              background-color: $primary;
            }
          }
        }
      }
    }
  }

  // Week View
  .week-view {
    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 10px;

      .weekday {
        text-align: center;
        font-weight: 500;
        color: #666;
      }
    }

    .week-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 5px;

      .week-day {
        min-height: 150px;
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 10px;
        cursor: pointer;

        &.has-reading {
          // Remove background color to show the dot indicator instead
          // background-color: rgba($primary-light, 0.05);
          position: relative; // For positioning the indicator
        }

        // Reading indicator dot for week view
        .reading-indicator-week {
          position: absolute;
          top: 5px;
          right: 5px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: $primary;
          display: block;
        }

        &.current-day {
          border: 2px solid $primary-light;
        }

        &:hover {
          background-color: #f9f9f9;
        }

        .day-readings {
          display: flex;
          flex-direction: column;
          gap: 5px;

          .day-reading {
            padding: 5px;
            border-radius: 4px;
            font-size: 12px;

            &.normal {
              background-color: #e8f5e9;
              color: #2e7d32;
            }

            &.warning {
              background-color: #fff8e1;
              color: #f57f17;
            }

            &.danger {
              background-color: #ffebee;
              color: #c62828;
            }
          }
        }

        .no-readings {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  // Day View
  .day-view {
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      text-align: center;
    }

    .day-readings-list {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .day-reading-item {
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        border-left: 4px solid #ddd;

        &.normal {
          border-left-color: #2e7d32;
        }

        &.warning {
          border-left-color: #f57f17;
        }

        &.danger {
          border-left-color: #c62828;
        }

        .reading-time {
          font-weight: 500;
          margin-bottom: 10px;
        }

        .reading-metrics {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 10px;

          span {
            font-size: 14px;
          }
        }

        .reading-risk {
          margin-bottom: 10px;

          span {
            font-weight: 500;

            &.normal {
              color: #2e7d32;
            }

            &.warning {
              color: #f57f17;
            }

            &.danger {
              color: #c62828;
            }
          }
        }

        .reading-notes {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }

    .no-readings-day {
      text-align: center;
      padding: 30px;
      color: #999;
    }
  }
}
