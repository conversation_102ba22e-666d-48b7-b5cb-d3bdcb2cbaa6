.reading-capture {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .progress-container {
    margin-bottom: 3rem;

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 1rem;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #dc3545, #e74c3c);
        transition: width 0.3s ease;
      }
    }

    .step-indicators {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .step-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f8f9fa;
        border: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        transition: all 0.3s ease;

        &.active {
          background: #dc3545;
          border-color: #dc3545;
          color: white;
          transform: scale(1.1);
        }

        &.completed {
          background: #28a745;
          border-color: #28a745;
          color: white;
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  .step-content {
    min-height: 400px;
    margin-bottom: 2rem;

    .step-header {
      text-align: center;
      margin-bottom: 2rem;

      h2 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 1.8rem;
      }

      p {
        color: #6c757d;
        font-size: 1.1rem;
      }
    }

    .welcome-step {
      .welcome-content {
        text-align: center;

        .icon-large {
          font-size: 4rem;
          color: #dc3545;
          margin-bottom: 1rem;
        }

        h3 {
          color: #2c3e50;
          margin-bottom: 1rem;
        }

        .checklist {
          margin: 2rem 0;

          .checklist-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 0.5rem;

            &.completed {
              background: #d4edda;
              color: #155724;

              i {
                color: #28a745;
              }
            }

            .retry-btn {
              background: #dc3545;
              color: white;
              border: none;
              padding: 0.25rem 0.75rem;
              border-radius: 4px;
              font-size: 0.875rem;
              cursor: pointer;

              &:hover {
                background: #c82333;
              }

              &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
              }
            }
          }
        }
      }
    }

    .reading-content {
      text-align: center;

      .sensor-icon {
        font-size: 3rem;
        color: #dc3545;
        margin-bottom: 1.5rem;
      }

      .instructions {
        h3 {
          color: #2c3e50;
          margin-bottom: 1rem;
        }

        p {
          color: #6c757d;
          margin-bottom: 2rem;
          line-height: 1.6;
        }

        .reading-btn {
          background: #dc3545;
          color: white;
          border: none;
          padding: 1rem 2rem;
          border-radius: 8px;
          font-size: 1.1rem;
          cursor: pointer;
          transition: background 0.3s ease;

          &:hover {
            background: #c82333;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .reading-result {
        h3 {
          color: #28a745;
          margin-bottom: 1rem;
        }

        .result-value {
          font-size: 2.5rem;
          font-weight: bold;
          color: #dc3545;
          margin-bottom: 0.5rem;
        }

        .result-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;
          margin-bottom: 1rem;

          .result-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;

            .result-label {
              display: block;
              font-size: 0.875rem;
              color: #6c757d;
              margin-bottom: 0.5rem;
            }

            .result-value {
              font-size: 1.5rem;
              font-weight: bold;
              color: #dc3545;
            }
          }
        }

        .result-info {
          color: #6c757d;
          font-size: 0.875rem;
          margin-bottom: 1rem;
        }

        .retry-reading-btn {
          background: #6c757d;
          color: white;
          border: none;
          padding: 0.5rem 1rem;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background: #5a6268;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .results-content {
      .loading-prediction {
        text-align: center;
        padding: 2rem;

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #dc3545;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }
      }

      .prediction-results {
        .prediction-header {
          text-align: center;
          margin-bottom: 2rem;

          .risk-indicator {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;

            &.low-risk {
              background: #d4edda;
              color: #28a745;
            }

            &.medium-risk {
              background: #fff3cd;
              color: #ffc107;
            }

            &.high-risk {
              background: #f8d7da;
              color: #dc3545;
            }
          }

          h3 {
            color: #2c3e50;
          }
        }

        .prediction-summary {
          background: #f8f9fa;
          padding: 1.5rem;
          border-radius: 8px;
          margin-bottom: 2rem;

          > div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              font-weight: 600;
              color: #495057;
            }

            .value {
              font-weight: bold;

              &.positive {
                color: #dc3545;
              }

              &.negative {
                color: #28a745;
              }
            }
          }
        }

        .readings-summary {
          h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
          }

          .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;

            .summary-item {
              display: flex;
              justify-content: space-between;
              padding: 0.75rem;
              background: #f8f9fa;
              border-radius: 6px;

              .label {
                color: #6c757d;
              }

              .value {
                font-weight: 600;
                color: #2c3e50;
              }
            }
          }
        }
      }
    }

    .error-message {
      background: #f8d7da;
      color: #721c24;
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
      text-align: center;

      i {
        margin-right: 0.5rem;
      }

      .retry-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        margin-left: 1rem;
        cursor: pointer;

        &:hover {
          background: #c82333;
        }
      }
    }
  }

  .navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;

    .nav-right {
      display: flex;
      gap: 1rem;
    }

    .nav-btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.cancel-btn {
        background: #6c757d;
        color: white;
        border: none;

        &:hover:not(:disabled) {
          background: #5a6268;
        }
      }

      &.secondary-btn {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;

        &:hover:not(:disabled) {
          background: #f8f9fa;
        }
      }

      &.primary-btn {
        background: #dc3545;
        color: white;
        border: none;

        &:hover:not(:disabled) {
          background: #c82333;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .reading-capture {
    padding: 1rem;
    margin: 1rem;

    .step-content {
      min-height: 300px;

      .reading-content {
        .result-grid {
          grid-template-columns: 1fr;
        }
      }

      .results-content {
        .summary-grid {
          grid-template-columns: 1fr;
        }
      }
    }

    .navigation {
      flex-direction: column;
      gap: 1rem;

      .nav-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
