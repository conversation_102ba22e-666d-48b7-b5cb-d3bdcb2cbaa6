@import '@/assets/styles/base/settings';

.detailed-report {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .back-navigation {
    margin-bottom: 1.5rem;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background-color: white;
      color: $primary-light;
      border: 1px solid $primary-light;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba($primary-light, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h1 {
      margin: 0;
      color: $primary-light;
    }

    .download-btn {
      padding: 0.5rem 1rem;
      background-color: $primary-light;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;

      &:hover {
        background-color: darken($primary-light, 5%);
      }

      &:disabled {
        background-color: lighten($primary-light, 20%);
        cursor: not-allowed;
      }
    }
  }

  .filters-section {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .date-range-picker {
      display: flex;
      gap: 1rem;
      align-items: center;

      input[type="date"] {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
    }

    .filter-controls {
      display: flex;
      gap: 1.5rem;

      .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          font-weight: 500;
          color: $dark-grey;
        }

        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: white;
        }
      }
    }
  }

  .statistics-summary {
    margin-bottom: 3rem;

    h2 {
      color: $primary-light;
      margin-bottom: 1.5rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-top: 3px solid $primary-light;

      h3 {
        font-size: 0.9rem;
        color: $dark-grey;
        margin-bottom: 0.5rem;
      }

      p {
        font-size: 1.5rem;
        font-weight: bold;
        color: $primary-light;
      }
    }
  }

  .charts-section {
    margin-bottom: 3rem;

    h2 {
      color: $primary-light;
      margin-bottom: 1.5rem;
    }

    .chart-container {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 1.5rem;

      h3 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: $dark-grey;
      }
    }
  }

  .trends-section, .anomalies-section {
    margin-bottom: 3rem;

    h2 {
      color: $primary-light;
      margin-bottom: 1.5rem;
    }

    .trend-item, .anomaly-item {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      h3 {
        margin-top: 0;
        margin-bottom: 0.5rem;
        color: $dark-grey;
      }

      p {
        margin: 0;
        color: $dark-grey;
      }
    }
  }

  .readings-table-section {
    margin-bottom: 3rem;

    h2 {
      color: $primary-light;
      margin-bottom: 1.5rem;
    }

    .readings-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      th, td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #eee;
      }

      th {
        background-color: $primary-light;
        color: white;
        font-weight: 500;
      }

      tr {
        &.normal {
          background-color: rgba(#2e7d32, 0.05);
        }

        &.warning {
          background-color: rgba(#f57f17, 0.05);
        }

        &.danger {
          background-color: rgba(#c62828, 0.05);
        }

        &:hover {
          background-color: #f9f9f9;
        }
      }

      .risk-cell {
        font-weight: 500;
      }
    }
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary-light, 0.3);
      border-radius: 50%;
      border-top-color: $primary-light;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    span {
      color: $dark-grey;
      font-size: 1.2rem;
    }
  }

  .error {
    color: #dc3545;
    padding: 1.5rem;
    background: #f8d7da;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 500;
  }
}
