@import '@/assets/styles/base/settings';

.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .main-content {
    flex: 1;
    padding: 16px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.patient-dashboard {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.2);

    .header-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    h1 {
      margin: 0;
      color: $primary;
      font-size: 1.8rem;
      font-weight: 600;
    }

    .last-reading {
      color: $dark-grey;
      font-size: 0.9em;
      background-color: rgba($primary-light, 0.05);
      padding: 6px 12px;
      border-radius: 4px;
      border-left: 3px solid $primary-light;
    }

    .header-actions {
      margin-top: 0;
      justify-content: flex-start;
    }
  }

  // Common section title styling
  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      color: $primary;
      font-size: 1.2rem;
    }

    h1, h2 {
      margin: 0;
      color: $primary;
    }
  }

  .status-card {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    background: $white;
    box-shadow: $shadow-sm;

    &.normal {
      border-left: 4px solid $primary-light;
      .status-icon {
        color: $primary-light;
      }
    }
    &.warning {
      border-left: 4px solid $primary;
      background: $primary-subtle;
      .status-icon {
        color: $primary;
      }
    }
    &.danger {
      border-left: 4px solid $primary;
      background: rgba(183, 21, 64, 0.15);
      .status-icon {
        color: $primary;
      }
    }

    .status-icon {
      font-size: 1.5rem;
      margin-right: 16px;
    }

    .status-content {
      flex: 1;

      h2 {
        margin: 0 0 8px 0;
        font-size: 1.2rem;
        color: $dark-grey;
      }

      .status-level {
        font-weight: bold;
        margin: 0 0 4px 0;
        color: $primary-light;
      }

      .status-message {
        margin: 0;
        font-size: 0.9rem;
        color: $dark-grey;
      }
    }
  }

  // Metrics section heading
  .metrics-heading {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    h2 {
      margin: 0;
      color: $primary;
      font-size: 1.3rem;
      font-weight: 600;
    }
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .metric-card {
      background: $white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: $shadow-sm;
      border-top: 3px solid $primary;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .metric-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        i {
          color: $primary;
          font-size: 1.1rem;
        }

        h3 {
          margin: 0;
          font-size: 1rem;
          color: $dark-grey;
          font-weight: 500;
        }
      }

      .metric-value {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 4px;
        color: $primary;
      }

      .metric-unit {
        font-size: 0.9rem;
        color: $dark-grey;
        margin-bottom: 12px;
        background-color: rgba($primary-light, 0.05);
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }
  }

  .readings-timeline {
    background: $white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: $shadow-sm;
    margin-bottom: 24px;
    border-top: 3px solid $primary;

    h2 {
      margin: 0 0 16px 0;
      color: $primary;
      font-size: 1.3rem;
      font-weight: 600;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba($primary-light, 0.1);
    }

    .timeline-container {
      max-height: 300px;
      overflow-y: auto;
      padding: 0.75rem;
      border: 1px solid rgba($primary-light, 0.1);
      border-radius: 8px;
      background-color: rgba($primary-light, 0.02);

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: $primary-light;
        border-radius: 4px;
      }
    }

    .timeline-item {
      padding: 12px;
      border-bottom: 1px solid rgba($primary-light, 0.1);
      font-size: 0.9rem;
      display: flex;
      flex-direction: column;
      gap: 8px;
      position: relative;
      background-color: white;
      margin-bottom: 8px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .timeline-date {
        font-size: 0.9rem;
        color: $primary;
        font-weight: 500;
        padding-bottom: 6px;
        border-bottom: 1px dashed rgba($primary-light, 0.2);
      }

      .timeline-content {
        h4 {
          margin: 0 0 8px 0;
          font-size: 1.1rem;
          color: $primary;
          font-weight: 600;
        }

        .reading-metrics {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 8px;
          background-color: rgba($primary-light, 0.05);
          padding: 8px 12px;
          border-radius: 6px;

          span {
            font-size: 0.9rem;
            font-weight: 500;
            color: $dark-grey;
            background-color: white;
            padding: 4px 8px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          }
        }

        .reading-notes {
          margin: 8px 0 0 0;
          font-size: 0.9rem;
          color: $dark-grey;
          font-style: italic;
          padding: 8px;
          background-color: rgba($primary-light, 0.02);
          border-left: 3px solid rgba($primary-light, 0.2);
          border-radius: 0 4px 4px 0;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    &.header-actions {
      flex-wrap: wrap;

      .secondary-button {
        font-size: 0.85rem;
        padding: 6px 12px;
      }
    }

    .secondary-button {
      padding: 8px 16px;
      border: 1px solid $danger;
      border-radius: 4px;
      background: white;
      color: $danger;
      cursor: pointer;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;

      i {
        font-size: 0.9rem;
        color: $danger;
      }

      &:hover {
        background: rgba(183, 21, 64, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }

  .action-button {
    padding: 8px 16px;
    background-color: $primary-light;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;

    &:hover {
      background-color: $primary;
    }

    &:disabled {
      background-color: lighten($primary-light, 15%);
      cursor: not-allowed;
    }
  }
}

.readings-section {
  margin-bottom: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: $shadow-sm;
  padding: 20px;
  border-top: 3px solid $primary;

  .readings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    h2 {
      margin: 0;
      color: $primary;
      font-size: 1.3rem;
      font-weight: 600;
    }

    .view-toggle {
      display: flex;
      gap: 2px;
      background-color: rgba($primary-light, 0.05);
      border-radius: 6px;
      padding: 3px;

      .toggle-btn {
        padding: 8px 14px;
        border: none;
        background: transparent;
        color: $dark-grey;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        i {
          font-size: 1rem;
        }

        &.active {
          background: white;
          color: $primary;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          font-weight: 500;
        }

        &:hover {
          color: $primary;

          &.active {
            background: white;
          }
        }
      }
    }
  }
}

.load-more-btn {
  margin: 0 auto;
  padding: 10px 16px;
  background-color: white;
  color: $primary;
  border: 1px solid $primary;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 16px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &::before {
    content: '+';
    font-size: 1.2rem;
    font-weight: bold;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    background-color: rgba($primary-light, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  }
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: $dark-grey;
  font-size: 0.9rem;
  background-color: white;
  border-radius: 8px;
  margin-top: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .spinner {
    width: 36px;
    height: 36px;
    border: 3px solid rgba($primary, 0.2);
    border-radius: 50%;
    border-top-color: $primary;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 12px;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 8px;
}

.no-more-readings, .no-readings {
  text-align: center;
  padding: 20px;
  color: $dark-grey;
  font-size: 0.9rem;
  background-color: rgba($primary-light, 0.05);
  border-radius: 8px;
  margin-top: 16px;
  border: 1px dashed rgba($primary-light, 0.2);
}
