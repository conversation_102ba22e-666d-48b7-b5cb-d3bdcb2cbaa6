@import "@/assets/styles/base/settings";

.profile-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;

  .dashboard-header {
    margin-bottom: 2rem;

    .header-left {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background-color: white;
        color: $primary-light;
        border: 1px solid $primary-light;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba($primary-light, 0.05);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      h1 {
        margin: 0;
      }
    }
  }

  .profile-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;

    .tab-header {
      display: flex;
      border-bottom: 1px solid #eee;

      .tab-button {
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        color: $dark-grey;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;

        i {
          color: $primary-light;
        }

        &.active {
          color: $primary;
          border-bottom-color: $primary;

          i {
            color: $primary;
          }
        }

        &:hover:not(.active) {
          background-color: rgba($primary-light, 0.05);
          color: $primary;
        }
      }
    }

    .tab-content {
      padding: 0;
    }
  }
}

.profile-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 2rem;
}

.profile-form {
  h3 {
    margin-bottom: 2rem;
  }
}

.danger-zone {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;

  h3 {
    margin-bottom: 1.5rem;
    color: #dc3545;
  }

  .btn-delete {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background: #c82333;
    }
  }
}

.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;

    &:disabled {
      background-color: #f8f9fa;
      cursor: not-allowed;
    }
  }
}

.form-actions {
  margin-top: 2rem;

  .btn-primary {
    background: $primary;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background: darken($primary, 5%);
    }
  }
}
