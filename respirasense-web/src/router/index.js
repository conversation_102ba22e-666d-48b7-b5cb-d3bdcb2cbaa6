import { createRouter, createWebHistory } from 'vue-router';
import request from "@/Rest";
import { auth } from "@/plugins/firebase/firebase";
import store from '@/store'; // Import the store directly
import Login from '@/views/auth/login';
import AdminDashboard from '@/views/admin/Dashboard';
import PractitionerDashboard from '@/views/practitioner/Dashboard';
import PatientDashboard from '@/views/patient/Dashboard';
import PatientRegistration from '@/components/auth/PatientRegistration.vue';

const routes = [
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: {
      requiresAuth: true,
      roles: ['admin', 'superAdmin']
    },
    children: [
      {
        path: '',
        name: 'AdminOverview',
        component: () => import('@/views/admin/Overview.vue')
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/Users.vue')
      },
      {
        path: 'users/new',
        name: 'AdminNewUser',
        component: () => import('@/views/admin/NewUser.vue')
      },
      {
        path: 'practitioners',
        name: 'AdminPractitioners',
        component: () => import('@/views/admin/Practitioners.vue')
      },
      {
        path: 'analytics',
        name: 'AdminAnalytics',
        component: () => import('@/views/admin/Analytics.vue')
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: () => import('@/views/admin/Settings.vue')
      },
      {
        path: 'health',
        name: 'SystemHealth',
        component: () => import('@/views/admin/SystemHealth.vue')
      },
      {
        path: 'reports',
        name: 'AdminReports',
        component: () => import('@/views/admin/Reports.vue')
      }
    ]
  },
  {
    path: '/',
    redirect: to => {
      return { name: 'Login' }
    }
  },
  {
    path: '/auth/login',
    name: 'Login',
    component: Login,
    meta: { layout: 'auth' }
  },
  {
    path: '/practitioner/dashboard',
    name: 'PractitionerDashboard',
    component: PractitionerDashboard,
    meta: {
      requiresAuth: true,
      roles: ['practitioner']
    }
  },
  {
    path: '/practitioner/patients',
    name: 'PractitionerPatients',
    component: () => import('@/views/practitioner/PatientList.vue'),
    meta: {
      requiresAuth: true,
      roles: ['practitioner']
    }
  },
  {
    path: '/practitioner/patient/:id',
    name: 'PractitionerPatientDetails',
    component: () => import('@/views/practitioner/PatientDetails.vue'),
    meta: {
      requiresAuth: true,
      roles: ['practitioner']
    },
    beforeEnter: (to, from, next) => {
      // Always allow navigation to patient details
      // We'll handle validation in the component itself
      next();
    }
  },
  {
    path: '/practitioner/patient/:id/detailed-report',
    name: 'PractitionerPatientDetailedReport',
    component: () => import('@/views/practitioner/PatientDetailedReport.vue'),
    meta: {
      requiresAuth: true,
      roles: ['practitioner']
    }
  },
  {
    path: '/patient/dashboard',
    name: 'PatientDashboard',
    component: PatientDashboard,
    meta: {
      requiresAuth: true,
      roles: ['patient']
    }
  },
  {
    path: '/patient/registration',
    name: 'PatientRegistration',
    component: PatientRegistration,
    meta: { requiresAuth: true }
  },
  {
    path: '/auth/signup',
    name: 'Signup',
    component: () => import('@/views/auth/signUp.vue'),
    meta: {
      layout: 'auth',
      requiresAuth: false
    }
  },
  {
    path: '/auth/role-selection',
    name: 'RoleSelection',
    component: () => import('@/views/auth/RoleSelection.vue'),
    meta: {
      layout: 'auth',
      requiresAuth: true
    }
  },
  {
    path: '/auth/registration-pending',
    name: 'RegistrationPending',
    component: () => import('@/views/auth/RegistrationPending.vue'),
    meta: {
      layout: 'auth',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/patient/Profile.vue'),
    meta: {
      layout: 'default',
      requiresAuth: true,
      roles: ['patient']
    }
  },
  {
    path: '/patient/profile',
    name: 'PatientProfile',
    component: () => import('@/views/patient/Profile.vue'),
    meta: {
      layout: 'default',
      requiresAuth: true,
      roles: ['patient']
    }
  },
  {
    path: '/auth/logout',
    name: 'Logout',
    beforeEnter: async (to, from, next) => {
      try {
        await auth.signOut();
        next({ name: 'Login' });
      } catch (error) {
        console.error('Logout error:', error);
        next({ name: 'Login' });
      }
    }
  },
  {
    path: '/patient/detailed-report',
    name: 'PatientDetailedReport',
    component: () => import('@/views/patient/DetailedReport.vue'),
    meta: { requiresAuth: true, roles: ['patient'] }
  },
  {
    path: '/unauthorized',
    name: 'Unauthorized',
    component: () => import('@/views/auth/Unauthorized.vue'),
    meta: { layout: 'auth' }
  },
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/static/Help.vue'),
    meta: {
      layout: 'default',
      requiresAuth: false
    }
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/static/Privacy.vue'),
    meta: {
      layout: 'default',
      requiresAuth: false
    }
  },
  {
    path: '/terms',
    name: 'Terms',
    component: () => import('@/views/static/Terms.vue'),
    meta: {
      layout: 'default',
      requiresAuth: false
    }
  },
  {
    path: '/settings',
    component: () => import('@/views/settings/SettingsLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'patient',
        name: 'PatientSettings',
        component: () => import('@/views/settings/PatientSettings.vue'),
        meta: {
          requiresAuth: true,
          roles: ['patient']
        }
      },
      {
        path: 'practitioner',
        name: 'PractitionerSettings',
        component: () => import('@/views/settings/PractitionerSettings.vue'),
        meta: {
          requiresAuth: true,
          roles: ['practitioner']
        }
      },
      {
        path: 'admin',
        name: 'AdminSettings',
        component: () => import('@/views/settings/AdminSettings.vue'),
        meta: {
          requiresAuth: true,
          roles: ['admin', 'superAdmin']
        }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

router.beforeEach(async (to, from, next) => {
  // Wait for Firebase Auth to initialize
  await new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      resolve(user);
    });
  });

  const user = auth.currentUser;

  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!user) {
      next('/auth/login');
      return;
    }

    try {
      // Force token refresh to ensure valid session
      await user.getIdToken(true);

      const userRoleDoc = await request.GET(`users_roles/${user.uid}`).Execute();

      if (!userRoleDoc.exists()) {
        next('/auth/login');
        return;
      }

      const userData = userRoleDoc.data();

      // Check if user has a rejected status
      if (userData.status === 'rejected') {
        // Sign out the user
        await auth.signOut();
        // Clear localStorage
        localStorage.clear();
        // Redirect to login with error message
        next({
          path: '/auth/login',
          query: { error: 'Your registration has been rejected. Please contact support for assistance.' }
        });
        return;
      }

      // Check if user has a pending status
      if (userData.status === 'pending' && to.path !== '/auth/registration-pending') {
        next('/auth/registration-pending');
        return;
      }

      // Check if user needs to complete role selection
      if ((userData.status === 'new' || !userData.role) && to.path !== '/auth/role-selection') {
        next('/auth/role-selection');
        return;
      }

      // Check if user has the required role for the route
      if (to.meta.roles && !to.meta.roles.includes(userData.role)) {
        next('/unauthorized');
        return;
      }

      next();
    } catch (error) {
      console.error('Navigation guard error:', error);
      // Clear invalid session data
      localStorage.removeItem('logged');
      localStorage.removeItem('userRole');
      next('/auth/login');
    }
  } else {
    next();
  }
});

export default router;
