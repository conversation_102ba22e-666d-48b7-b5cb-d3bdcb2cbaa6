import { createStore } from 'vuex';
import authFirebase from './modules/authFirebase';
import user from './modules/user';
import patient from './modules/patient';
import admin from './modules/admin'
import settings from './modules/settings'
import units from './modules/units'
import share from './modules/share'
import practitioner from './modules/practitioner'

export default createStore({
  modules: {
    authFirebase,
    user,
    patient,
    admin,
    settings,
    units,
    share,
    practitioner
  }
});
