import * as mutations from './mutations';
import * as actions from './actions';
import * as getters from './getters';

export const initialState = () => ({
  patients: [],
  pendingRequests: [],
  recentActivity: [],
  selectedPatient: null,
  patientReadings: [],
  patientLinkingRequests: [],
  loading: false,
  error: null
});

const practitionerModule = {
  namespaced: true,
  state: initialState(),
  mutations,
  actions,
  getters
};

export default practitionerModule;
