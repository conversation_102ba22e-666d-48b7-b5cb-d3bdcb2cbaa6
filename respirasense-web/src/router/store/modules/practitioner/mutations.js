import { initialState } from './index';

export function RESET(state) {
  const newState = initialState();
  Object.keys(newState).forEach(key => {
    state[key] = newState[key];
  });
}

export function SET_LOADING(state, status) {
  state.loading = status;
}

export function SET_ERROR(state, error) {
  state.error = error;
}

export function SET_PATIENTS(state, patients) {
  state.patients = patients;
}

export function SET_PENDING_REQUESTS(state, requests) {
  state.pendingRequests = requests;
}

export function SET_RECENT_ACTIVITY(state, activity) {
  state.recentActivity = activity;
}

export function SET_SELECTED_PATIENT(state, patient) {
  state.selectedPatient = patient;
}

export function SET_PATIENT_READINGS(state, readings) {
  state.patientReadings = readings;
}

export function REMOVE_REQUEST(state, requestId) {
  state.pendingRequests = state.pendingRequests.filter(r => r.id !== requestId);
}

export function SET_PATIENT_LINKING_REQUESTS(state, requests) {
  state.patientLinkingRequests = requests;
}
