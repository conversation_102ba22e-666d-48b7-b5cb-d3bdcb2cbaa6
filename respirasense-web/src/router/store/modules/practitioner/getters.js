export function getPatients(state) {
  return state.patients;
}

export function getPendingRequests(state) {
  return state.pendingRequests;
}

export function getRecentActivity(state) {
  return state.recentActivity;
}

export function getSelectedPatient(state) {
  return state.selectedPatient;
}

export function getPatientReadings(state) {
  return state.patientReadings;
}

export function isLoading(state) {
  return state.loading;
}

export function getError(state) {
  return state.error;
}

export function getActivePatientCount(state) {
  return state.patients.filter(p => p.status === 'active').length;
}

export function getPendingRequestsCount(state) {
  return state.pendingRequests.length;
}

export function getPatientLinkingRequests(state) {
  return state.patientLinkingRequests;
}

export function getPendingLinkingRequestsCount(state) {
  return state.patientLinkingRequests.filter(r => r.status === 'pending').length;
}
