import { auth } from '@/plugins/firebase/firebase';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';
import {
  getPractitionerPatients,
  requestPatientLinking as requestPatientLinkingUtil,
  getPatientLinkingRequests,
  cancelPatientLinkingRequest as cancelPatientLinkingRequestUtil
} from '@/utils/firestore-helpers';

/**
 * Fetch all patients assigned to the current practitioner
 */
export const fetchPatients = async ({ commit }) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    const patients = await getPractitionerPatients(user.uid);

    // Get additional patient data from users_roles collection
    const enhancedPatients = await Promise.all(patients.map(async (patient) => {
      try {
        const userRoleRef = doc(db, `users_roles/${patient.id}`);
        const userRoleDoc = await getDoc(userRoleRef);

        if (userRoleDoc.exists()) {
          const userData = userRoleDoc.data();
          return {
            ...patient,
            name: userData.name || patient.name,
            email: userData.email || patient.email,
            status: userData.status || 'active'
          };
        }
        return patient;
      } catch (error) {
        console.error(`Error fetching additional data for patient ${patient.id}:`, error);
        return patient;
      }
    }));

    commit('SET_PATIENTS', enhancedPatients);
    return enhancedPatients;
  } catch (error) {
    console.error('Error fetching patients:', error);
    commit('SET_ERROR', 'Failed to load patients');
    return [];
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Fetch pending patient requests for the current practitioner
 */
export const fetchPendingRequests = async ({ commit }) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Query patient_requests collection for pending requests assigned to this practitioner
    const requestsRef = collection(db, 'patient_requests');
    const q = query(
      requestsRef,
      where('practitionerId', '==', user.uid),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);

    // Map the documents to an array of request data
    const requests = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    }));

    commit('SET_PENDING_REQUESTS', requests);
    return requests;
  } catch (error) {
    console.error('Error fetching pending requests:', error);
    commit('SET_ERROR', 'Failed to load pending requests');
    return [];
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Fetch recent patient activity for the current practitioner
 */
export const fetchRecentActivity = async ({ commit }) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Get all patients assigned to this practitioner
    const patients = await getPractitionerPatients(user.uid);

    if (patients.length === 0) {
      commit('SET_RECENT_ACTIVITY', []);
      return [];
    }

    // Get patient IDs
    const patientIds = patients.map(patient => patient.id);

    // Process each patient to get their latest reading
    const recentActivity = [];

    for (const patientId of patientIds) {
      try {
        // Get patient info
        const patientRef = doc(db, `patients/${patientId}`);
        const patientDoc = await getDoc(patientRef);
        const patientData = patientDoc.exists() ? patientDoc.data() : {};

        // Get user role info for name
        const userRoleRef = doc(db, `users_roles/${patientId}`);
        const userRoleDoc = await getDoc(userRoleRef);
        const userData = userRoleDoc.exists() ? userRoleDoc.data() : {};

        // Get latest reading
        const readingsRef = collection(db, `patients/${patientId}/readings`);
        const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(1));
        const readingSnapshot = await getDocs(q);

        if (!readingSnapshot.empty) {
          const latestReading = readingSnapshot.docs[0];
          const readingData = latestReading.data();

          recentActivity.push({
            id: patientId,
            name: userData.name || patientData.name || 'Unknown Patient',
            email: userData.email || patientData.email,
            lastUpdate: readingData.timestamp?.toDate() || new Date(),
            riskLevel: readingData.riskLevel || 'normal',
            readingId: latestReading.id
          });
        } else {
          // Include patient even without readings
          recentActivity.push({
            id: patientId,
            name: userData.name || patientData.name || 'Unknown Patient',
            email: userData.email || patientData.email,
            lastUpdate: null,
            riskLevel: 'unknown',
            readingId: null
          });
        }
      } catch (error) {
        console.error(`Error processing patient ${patientId}:`, error);
      }
    }

    // Sort by last update time (most recent first)
    recentActivity.sort((a, b) => {
      if (!a.lastUpdate) return 1;
      if (!b.lastUpdate) return -1;
      return b.lastUpdate - a.lastUpdate;
    });

    commit('SET_RECENT_ACTIVITY', recentActivity);
    return recentActivity;
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    commit('SET_ERROR', 'Failed to load recent patient activity');
    return [];
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Approve a patient request
 */
export const approveRequest = async ({ commit, dispatch }, requestId) => {
  try {
    const requestRef = doc(db, `patient_requests/${requestId}`);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      throw new Error('Request not found');
    }

    const requestData = requestDoc.data();

    // Update the request status
    await updateDoc(requestRef, {
      status: 'approved',
      approvedBy: auth.currentUser.uid,
      approvedAt: serverTimestamp()
    });

    // Remove from pending requests
    commit('REMOVE_REQUEST', requestId);

    // Refresh data
    await dispatch('fetchPendingRequests');
    await dispatch('fetchPatients');

    return true;
  } catch (error) {
    console.error('Error approving request:', error);
    throw error;
  }
};

/**
 * Reject a patient request
 */
export const rejectRequest = async ({ commit, dispatch }, requestId) => {
  try {
    const requestRef = doc(db, `patient_requests/${requestId}`);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      throw new Error('Request not found');
    }

    // Update the request status
    await updateDoc(requestRef, {
      status: 'rejected',
      rejectedBy: auth.currentUser.uid,
      rejectedAt: serverTimestamp()
    });

    // Remove from pending requests
    commit('REMOVE_REQUEST', requestId);

    // Refresh data
    await dispatch('fetchPendingRequests');

    return true;
  } catch (error) {
    console.error('Error rejecting request:', error);
    throw error;
  }
};

/**
 * Get patient details
 */
export const getPatientDetails = async ({ commit }, patientId) => {
  commit('SET_LOADING', true);
  try {
    console.log('Fetching details for patient:', patientId);

    // First, check if this patient is linked to the current practitioner
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Get patient data from users_roles
    const userRoleRef = doc(db, `users_roles/${patientId}`);
    let userRoleDoc;
    try {
      userRoleDoc = await getDoc(userRoleRef);
      console.log('User role document exists:', userRoleDoc.exists());
    } catch (err) {
      console.error('Error fetching user_roles document:', err);
      throw new Error('Failed to access patient data');
    }

    if (!userRoleDoc.exists()) {
      throw new Error('Patient not found');
    }

    const userData = userRoleDoc.data();
    console.log('User data retrieved successfully');

    // Get additional patient data from patients collection
    const patientRef = doc(db, `patients/${patientId}`);
    let patientDoc;
    let patientData = {};

    try {
      patientDoc = await getDoc(patientRef);
      if (patientDoc.exists()) {
        patientData = patientDoc.data();
        console.log('Patient document exists in patients collection');
      } else {
        console.log('Patient document does not exist in patients collection');
      }
    } catch (err) {
      console.error('Error fetching patient document:', err);
      // Continue with limited data
    }

    // Get patient profile if available
    let profileData = {};
    try {
      const profileRef = doc(db, `patients/${patientId}/profile/details`);
      const profileDoc = await getDoc(profileRef);
      if (profileDoc.exists()) {
        profileData = profileDoc.data();
        console.log('Patient profile document exists');
      } else {
        console.log('Patient profile document does not exist');
      }
    } catch (err) {
      console.error('Error fetching patient profile:', err);
      // Continue with limited data
    }

    // Create patient details object with available data
    const patientDetails = {
      id: patientId,
      name: userData.name || patientData.name || 'Unknown Patient',
      email: userData.email || patientData.email,
      status: userData.status || 'active',
      age: profileData.age,
      height: profileData.height,
      weight: profileData.weight,
      gender: profileData.gender,
      medicalHistory: profileData.medicalHistory || [],
      medications: profileData.medications || [],
      createdAt: userData.createdAt || patientData.createdAt,
      lastUpdate: patientData.lastUpdate
    };

    console.log('Patient details compiled successfully');
    commit('SET_SELECTED_PATIENT', patientDetails);
    return patientDetails;
  } catch (error) {
    console.error('Error fetching patient details:', error);
    commit('SET_ERROR', 'Failed to load patient details: ' + error.message);
    return null;
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Get patient readings
 */
export const getPatientReadings = async ({ commit }, patientId) => {
  commit('SET_LOADING', true);
  try {
    console.log('Fetching readings for patient:', patientId);

    // Query readings collection for this patient (not health_readings)
    const readingsRef = collection(db, `patients/${patientId}/readings`);

    try {
      const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(20));
      const readingSnapshot = await getDocs(q);
      console.log(`Found ${readingSnapshot.docs.length} readings for patient`);

      // Map the documents to an array of reading data
      const readings = readingSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));

      commit('SET_PATIENT_READINGS', readings);
      return readings;
    } catch (err) {
      console.error('Error querying readings:', err);

      // Try to access the collection without query to check permissions
      try {
        const simpleSnapshot = await getDocs(readingsRef);
        console.log('Could access collection but query failed:', simpleSnapshot);
      } catch (collErr) {
        console.error('Cannot access readings collection:', collErr);
      }

      throw err;
    }
  } catch (error) {
    console.error('Error fetching patient readings:', error);
    commit('SET_ERROR', 'Failed to load patient readings: ' + error.message);
    return [];
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Get patient readings in a date range
 */
export const getPatientReadingsInDateRange = async ({ commit }, { patientId, fromDate, toDate, limit = 100 }) => {
  try {
    console.log(`Fetching readings for patient ${patientId} in date range: ${fromDate} to ${toDate}`);

    // Query readings collection for this patient
    const readingsRef = collection(db, `patients/${patientId}/readings`);

    // Build the query
    let q = query(readingsRef, orderBy('timestamp', 'desc'));

    // Add date filters if provided
    if (fromDate) {
      const fromDateObj = new Date(fromDate);
      q = query(q, where('timestamp', '>=', fromDateObj));
    }

    if (toDate) {
      const toDateObj = new Date(toDate);
      q = query(q, where('timestamp', '<=', toDateObj));
    }

    // Add limit
    q = query(q, limit);

    // Execute the query
    const readingSnapshot = await getDocs(q);
    console.log(`Found ${readingSnapshot.docs.length} readings for patient in date range`);

    // Map the documents to an array of reading data
    const readings = readingSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate() || new Date()
    }));

    return readings;
  } catch (error) {
    console.error('Error fetching patient readings in date range:', error);
    return [];
  }
};



/**
 * Request to link with a patient using their email
 */
export const requestPatientLinking = async ({ commit, dispatch }, { patientEmail }) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Use the utility function to request patient linking
    const result = await requestPatientLinkingUtil(user.uid, patientEmail);

    // Refresh the requests list
    await dispatch('fetchPatientLinkingRequests');

    return result;
  } catch (error) {
    console.error('Error requesting patient linking:', error);
    commit('SET_ERROR', error.message || 'Failed to send request');
    throw error;
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Fetch all patient linking requests initiated by this practitioner
 */
export const fetchPatientLinkingRequests = async ({ commit }) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Use the utility function to get patient linking requests
    const requests = await getPatientLinkingRequests(user.uid);

    commit('SET_PATIENT_LINKING_REQUESTS', requests);
    return requests;
  } catch (error) {
    console.error('Error fetching patient linking requests:', error);
    commit('SET_ERROR', 'Failed to load patient linking requests');
    return [];
  } finally {
    commit('SET_LOADING', false);
  }
};

/**
 * Cancel a patient linking request
 */
export const cancelPatientLinkingRequest = async ({ commit, dispatch }, requestId) => {
  try {
    // Use the utility function to cancel patient linking request
    await cancelPatientLinkingRequestUtil(requestId);

    // Refresh the requests list
    await dispatch('fetchPatientLinkingRequests');

    return true;
  } catch (error) {
    console.error('Error cancelling patient linking request:', error);
    commit('SET_ERROR', 'Failed to cancel request');
    throw error;
  }
};
