import { initialState } from './index';

export function RESET(state) {
  const newState = initialState();
  Object.keys(newState).forEach(key => {
    state[key] = newState[key];
  });
}

export function SET_LAST_READING(state, reading) {
  state.lastReading = reading;
}

export function SET_LATEST_METRICS(state, metrics) {
  state.latestMetrics = metrics;
}

export function SET_RECENT_READINGS(state, readings) {
  state.recentReadings = readings;
}

export function SET_HEALTH_HISTORY(state, history) {
  state.healthHistory = history;
}
