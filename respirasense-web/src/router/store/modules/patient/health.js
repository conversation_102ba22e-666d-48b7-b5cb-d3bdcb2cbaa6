import request from '@/Rest';
import { auth } from '@/plugins/firebase/firebase';

/**
 * Calculate statistics from an array of readings
 * @param {Array} readings - Array of health readings
 * @returns {Object} Statistics object with averages, trends, and anomalies
 */
function calculateStatistics(readings) {
  if (!readings || !readings.length) {
    return {
      averages: {},
      trends: {},
      anomalies: []
    };
  }

  // Calculate averages
  const sum = readings.reduce((acc, reading) => {
    return {
      respiratoryRate: acc.respiratoryRate + (reading.respiratoryRate || 0),
      oxygenSaturation: acc.oxygenSaturation + (reading.oxygenSaturation || 0),
      heartRate: acc.heartRate + (reading.heartRate || 0),
      temperature: acc.temperature + (parseFloat(reading.temperature) || 0)
    };
  }, {
    respiratoryRate: 0,
    oxygenSaturation: 0,
    heartRate: 0,
    temperature: 0
  });

  const count = readings.length;
  const averages = {
    respiratoryRate: sum.respiratoryRate / count,
    oxygenSaturation: sum.oxygenSaturation / count,
    heartRate: sum.heartRate / count,
    temperature: sum.temperature / count
  };

  // Analyze trends (simplified for now)
  const trends = {
    respiratoryRate: {
      description: 'Respiratory rate has been stable over the selected period.'
    },
    oxygenSaturation: {
      description: 'Oxygen saturation has been within normal range.'
    },
    heartRate: {
      description: 'Heart rate shows normal variation throughout the day.'
    },
    temperature: {
      description: 'Body temperature has been consistent.'
    }
  };

  // Check for any readings with warning or danger risk levels
  const anomalies = readings
    .filter(reading => reading.riskLevel === 'warning' || reading.riskLevel === 'danger')
    .map(reading => ({
      date: reading.timestamp,
      description: `${reading.riskLevel.toUpperCase()} risk level detected. Respiratory Rate: ${reading.respiratoryRate} bpm, Oxygen Saturation: ${reading.oxygenSaturation}%, Heart Rate: ${reading.heartRate} bpm, Temperature: ${reading.temperature}°C`
    }));

  return {
    averages,
    trends,
    anomalies
  };
}

export default {
  namespaced: true,

  state: {
    lastReading: null,
    latestMetrics: {},
    recentReadings: [],
    healthHistory: {
      respiratoryRate: [],
      oxygenSaturation: [],
      heartRate: [],
      temperature: []
    }
  },

  mutations: {
    SET_LAST_READING(state, reading) {
      state.lastReading = reading;
    },
    SET_LATEST_METRICS(state, metrics) {
      state.latestMetrics = metrics;
    },
    SET_RECENT_READINGS(state, readings) {
      state.recentReadings = readings;
    },
    SET_HEALTH_HISTORY(state, history) {
      state.healthHistory = history;
    },
    ADD_RECENT_READINGS(state, readings) {
      state.recentReadings = [...state.recentReadings, ...readings];
    }
  },

  actions: {
    async fetchHealthData({ commit }) {
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        const userId = user.uid;

        // Fetch readings document
        const readingsDoc = await request.GET(
          `patients/${userId}/health_records/readings`
        ).Execute();

        if (!readingsDoc.exists()) {
          console.warn('No readings document found for user');
          return null;
        }

        const readings = readingsDoc.data();

        // Process last reading
        const lastReading = readings.latest ? {
          ...readings.latest,
          timestamp: readings.latest.timestamp,
          riskLevel: readings.latest.riskLevel || 'normal'
        } : null;

        // Process recent readings
        const recentReadings = (readings.recent || []).map(reading => ({
          ...reading,
          timestamp: reading.timestamp,
          riskLevel: reading.riskLevel || 'normal'
        }));

        // Extract latest metrics
        const latestMetrics = lastReading ? {
          respiratoryRate: lastReading.respiratoryRate,
          oxygenSaturation: lastReading.oxygenSaturation,
          heartRate: lastReading.heartRate,
          temperature: lastReading.temperature
        } : {};

        // Fetch history document
        const historyDoc = await request.GET(
          `patients/${userId}/health_records/history`
        ).Execute();

        const history = historyDoc.exists() ? historyDoc.data() : {
          respiratoryRate: [],
          oxygenSaturation: [],
          heartRate: [],
          temperature: []
        };

        // Commit all the data
        commit('SET_LAST_READING', lastReading);
        commit('SET_LATEST_METRICS', latestMetrics);
        commit('SET_RECENT_READINGS', recentReadings);
        commit('SET_HEALTH_HISTORY', history);

        return {
          lastReading,
          latestMetrics,
          recentReadings,
          history
        };
      } catch (error) {
        console.error('Error in fetchHealthData:', error);
        throw error;
      }
    },
    async fetchMoreReadings({ commit }, { limit = 5, lastTimestamp = null }) {
      try {
        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user found');

        let query = request.GET(`patients/${user.uid}/readings`);

        if (lastTimestamp) {
          query = query.START_AFTER(lastTimestamp);
        }

        query = query
          .ORDER_BY('timestamp', 'desc')
          .LIMIT(limit);

        // Execute query with proper error handling
        let snapshot;
        try {
          snapshot = await query.Execute();
        } catch (err) {
          console.error('Error executing Firestore query:', err);
          return [];
        }

        // Check if snapshot and its methods exist
        if (!snapshot || !snapshot._delegate) {
          console.error('Invalid snapshot returned from Firestore:', snapshot);
          return [];
        }

        // Check if snapshot exists and has data
        if (!snapshot.exists || (typeof snapshot.exists === 'function' && !snapshot.exists())) {
          console.log('No more readings found');
          return [];
        }

        // Process readings with additional error handling
        const readings = [];
        try {
          snapshot.forEach(doc => {
            if (doc && typeof doc.data === 'function') {
              readings.push({
                id: doc.id,
                ...doc.data()
              });
            }
          });
        } catch (err) {
          console.error('Error processing Firestore documents:', err);
        }

        return readings;
      } catch (error) {
        console.error('Error fetching more readings:', error);
        throw error;
      }
    },

    async fetchDetailedReport({ commit }, { from, to }) {
      try {
        console.log('fetchDetailedReport called with:', { from, to });

        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user found');

        // Convert dates to timestamps
        const fromDate = from ? new Date(from) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days
        const toDate = to ? new Date(to) : new Date(); // Default to today

        // Ensure fromDate is at the start of the day and toDate is at the end of the day
        fromDate.setHours(0, 0, 0, 0);
        toDate.setHours(23, 59, 59, 999);

        console.log('Fetching report from Firestore with:', fromDate, toDate);

        // Create a safe default return object
        const defaultReturn = {
          readings: [],
          statistics: {
            averages: {},
            trends: {},
            anomalies: []
          }
        };

        try {
          // Construct the query safely
          const collectionPath = `patients/${user.uid}/readings`;
          console.log('Collection path:', collectionPath);

          // Create the query step by step to identify any issues
          let query = request.GET(collectionPath);

          // Add WHERE clauses one by one
          query = query.WHERE(['timestamp', '>=', fromDate]);
          query = query.WHERE(['timestamp', '<=', toDate]);

          // Add ORDER BY
          query = query.ORDER_BY('timestamp', 'desc');

          console.log('Query constructed successfully');

          // Execute the query
          const snapshot = await query.Execute();

          // Check if snapshot exists
          if (!snapshot) {
            console.error('Null snapshot returned from Firestore');
            return defaultReturn;
          }

          // Check if snapshot is empty
          if (snapshot.empty) {
            console.log('No readings found in the specified date range');
            return defaultReturn;
          }

          // Process readings
          const readings = [];
          snapshot.forEach(doc => {
            if (doc && typeof doc.data === 'function') {
              const data = doc.data();
              readings.push({
                id: doc.id,
                ...data
              });
            }
          });

          console.log(`Processed ${readings.length} readings`);

          // Calculate statistics
          const statistics = calculateStatistics(readings);

          return {
            readings,
            statistics
          };
        } catch (err) {
          console.error('Error in Firestore query execution:', err);
          return defaultReturn;
        }
      } catch (error) {
        console.error('Error fetching detailed report:', error);
        // Return a safe default instead of throwing
        return {
          readings: [],
          statistics: {
            averages: {},
            trends: {},
            anomalies: []
          }
        };
      }
    },

    async fetchReadingsInDateRange({ commit }, { fromDate, toDate, limit = 100 }) {
      try {
        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user found');

        console.log(`Fetching readings in date range: ${fromDate} to ${toDate}`);

        // Create the query
        let query = request.GET(`patients/${user.uid}/readings`);

        // Add date range filters if provided
        if (fromDate) {
          query = query.WHERE(['timestamp', '>=', fromDate]);
        }

        if (toDate) {
          query = query.WHERE(['timestamp', '<=', toDate]);
        }

        // Order by timestamp and limit results
        query = query
          .ORDER_BY('timestamp', 'desc')
          .LIMIT(limit);

        // Execute query with proper error handling
        let snapshot;
        try {
          snapshot = await query.Execute();
        } catch (err) {
          console.error('Error executing Firestore query for date range:', err);
          return [];
        }

        // Check if snapshot is valid
        if (!snapshot) {
          console.error('Invalid snapshot returned from Firestore');
          return [];
        }

        // Process readings
        const readings = [];
        try {
          snapshot.forEach(doc => {
            if (doc && typeof doc.data === 'function') {
              readings.push({
                id: doc.id,
                ...doc.data()
              });
            }
          });
        } catch (err) {
          console.error('Error processing Firestore documents:', err);
        }

        console.log(`Found ${readings.length} readings in date range`);
        return readings;
      } catch (error) {
        console.error('Error fetching readings in date range:', error);
        return [];
      }
    },

    async requestNewReading({ commit }) {
      try {
        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user found');

        // This would typically connect to a device or service to get a new reading
        // For now, we'll simulate a new reading with random data
        const newReading = {
          respiratoryRate: Math.floor(Math.random() * 10) + 15, // 15-25
          oxygenSaturation: Math.floor(Math.random() * 5) + 95, // 95-100
          heartRate: Math.floor(Math.random() * 40) + 60, // 60-100
          temperature: (Math.random() * 1.5 + 36.5).toFixed(1), // 36.5-38.0
          timestamp: new Date(),
          riskLevel: 'normal'
        };

        // Determine risk level based on values
        if (newReading.respiratoryRate > 22 || newReading.oxygenSaturation < 95) {
          newReading.riskLevel = 'warning';
        }
        if (newReading.respiratoryRate > 25 || newReading.oxygenSaturation < 92) {
          newReading.riskLevel = 'danger';
        }

        // Save the new reading to Firestore
        await request.SET(
          `patients/${user.uid}/readings/${Date.now()}`,
          newReading
        ).Execute();

        // Update the latest reading in the health_records document
        await request.UPDATE(
          `patients/${user.uid}/health_records/readings`,
          { latest: newReading }
        ).Execute();

        // Add to recent readings
        await request.UPDATE(
          `patients/${user.uid}/health_records/readings`,
          { recent: [newReading] }
        ).ARRAY_UNION('recent').Execute();

        return newReading;
      } catch (error) {
        console.error('Error requesting new reading:', error);
        throw error;
      }
    }
  },

  getters: {
    getLastReading: state => state.lastReading,
    getLatestMetrics: state => state.latestMetrics,
    getRecentReadings: state => state.recentReadings,
    getHealthHistory: state => state.healthHistory
  }
};
