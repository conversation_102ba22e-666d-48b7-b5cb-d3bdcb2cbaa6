import * as mutations from './mutations';
import * as actions from './actions';
import * as getters from './getters';
import health from './health';

export const initialState = () => ({
  lastReading: null,
  latestMetrics: {},
  recentReadings: [],
  healthHistory: {
    respiratoryRate: [],
    oxygenSaturation: [],
    heartRate: [],
    temperature: []
  }
});

const patientModule = {
  namespaced: true,
  state: initialState(),
  mutations,
  actions,
  getters,
  modules: {
    health
  }
};

export default patientModule;
