import request from "@/Rest";
import { auth } from "@/plugins/firebase/firebase";

export const setLastReading = ({ commit }, reading) => {
    commit('SET_LAST_READING', reading);
};

export const setLatestMetrics = ({ commit }, metrics) => {
    commit('SET_LATEST_METRICS', metrics);
};

export const setRecentReadings = ({ commit }, readings) => {
    commit('SET_RECENT_READINGS', readings);
};

export const setHistory = ({ commit }, history) => {
    commit('SET_HISTORY', history);
};

export const saveProfile = async ({ commit }, profileData) => {
    try {
        const user = auth.currentUser;
        if (!user) {
            throw new Error('No authenticated user found');
        }

        // Save profile data to Firestore using 'details' as the document ID
        await request.POST(`patients/${user.uid}/profile/details`, {
            data: {
                ...profileData,
                email: user.email,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                status: 'active'
            }
        }).Execute();

        // Create the users_roles document
        await request.POST(`users_roles/${user.uid}`, {
            data: {
                name: profileData.name,
                email: user.email,
                role: 'patient',
                status: 'active',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        }).Execute();

        commit('SET_PROFILE', {
            ...profileData,
            email: user.email
        });
        return true;
    } catch (error) {
        console.error('Error saving profile:', error);
        throw error;
    }
};
