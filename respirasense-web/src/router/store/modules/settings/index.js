import { auth } from "@/plugins/firebase/firebase";
import request from "@/Rest";

export default {
  namespaced: true,
  
  state: () => ({
    settings: null,
    loading: false,
    error: null
  }),

  mutations: {
    SET_SETTINGS(state, settings) {
      state.settings = settings;
    },
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    }
  },

  actions: {
    async fetchSettings({ commit }, userType) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user');

        const settingsDoc = await request.GET(`users_settings/${user.uid}`).Execute();
        if (settingsDoc.exists()) {
          commit('SET_SETTINGS', settingsDoc.data());
        }
      } catch (error) {
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async updateSettings({ commit }, { userType, settings }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) throw new Error('No authenticated user');

        await request.PUT(`users_settings/${user.uid}`, {
          data: {
            ...settings,
            updatedAt: new Date().toISOString(),
            updatedBy: user.uid
          }
        }).Execute();

        commit('SET_SETTINGS', settings);
      } catch (error) {
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};