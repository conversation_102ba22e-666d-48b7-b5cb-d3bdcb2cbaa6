import {
  auth, google<PERSON>uth<PERSON><PERSON>ider, apple<PERSON>uth<PERSON>rovider, secondaryAuth,
  signInWithEmailAndPassword, createUserWithEmailAndPassword,
  signInWithPopup, signInWithRedirect, signOut, sendPasswordResetEmail,
  browserLocalPersistence, setPersistence
} from "@/plugins/firebase/firebase";
import request from "@/Rest";

export function reset({ commit }) {
  commit("RESET");
}

export const SignIn = async ({ commit, dispatch }, payload) => {
  commit("cleanErrors");
  try {
    const userCredential = await signInWithEmailAndPassword(auth, payload.email, payload.password);
    console.log('Firebase auth successful response:', userCredential);

    const userRoleSnapshot = await request.GET(`users_roles/${userCredential.user.uid}`).Execute();
    console.log('User role snapshot:', userRoleSnapshot.data());

    const now = new Date().toISOString();

    if (userRoleSnapshot.exists()) {
      const userData = userRoleSnapshot.data();

      // Set session persistence
      await setPersistence(auth, browserLocalPersistence);

      // Update localStorage with session data
      localStorage.setItem('logged', 'true');
      localStorage.setItem('userRole', userData.role);
      localStorage.setItem('userName', userData.name);
      localStorage.setItem('userEmail', userData.email);
      localStorage.setItem('sessionStartTime', now);

      // Set session timeout (e.g., 8 hours)
      const sessionTimeout = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
      localStorage.setItem('sessionTimeout', String(sessionTimeout));

      commit("isLogged", true);
      return {
        isLogged: true,
        role: userData.role,
        userData: userData
      };
    } else {
      // Create new user document with all required fields
      const newUserData = {
        email: userCredential.user.email,
        role: 'patient',
        status: 'active',
        createdAt: now,
        updatedAt: now,
        last_login: now,
        name: userCredential.user.displayName || userCredential.user.email.split('@')[0],
        provider: 'password'
      };

      await request.POST(`users_roles/${userCredential.user.uid}`, {
        data: newUserData
      }).Execute();

      // Set session persistence
      await setPersistence(auth, browserLocalPersistence);

      // Update localStorage with session data
      localStorage.setItem('logged', 'true');
      localStorage.setItem('userRole', 'patient');
      localStorage.setItem('userName', newUserData.name);
      localStorage.setItem('userEmail', newUserData.email);
      localStorage.setItem('sessionStartTime', now);

      // Set session timeout (e.g., 8 hours)
      const sessionTimeout = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
      localStorage.setItem('sessionTimeout', String(sessionTimeout));

      commit("isLogged", true);
      return {
        isLogged: true,
        role: 'patient',
        isNewUser: true,
        userData: newUserData
      };
    }
  } catch (error) {
    console.error('Firebase auth error details:', error);
    let errorMessage = "An error occurred during sign in";

    if (error.code === 'auth/user-not-found') {
      errorMessage = "User not found";
    } else if (error.code === 'auth/wrong-password') {
      errorMessage = "Invalid password";
    }

    commit("error", errorMessage);
    return {
      isLogged: false,
      error: errorMessage,
      errorCode: error.code,
      originalError: error.message
    };
  }
};

export const LogInWithGoogle = async ({ commit }) => {
  try {
    // First try to sign in with popup
    const result = await signInWithPopup(auth, googleAuthProvider);
    console.log('Google sign-in result:', result);

    try {
      // Get the user role data
      let userRoleSnapshot;
      let isNewUser = false;

      try {
        userRoleSnapshot = await request.GET(`users_roles/${result.user.uid}`).Execute();
      } catch (permissionError) {
        console.warn('Permission error when getting user role, assuming new user:', permissionError);
        // If we get a permission error, assume this is a new user
        userRoleSnapshot = { exists: () => false };
        isNewUser = true;
      }

      const now = new Date().toISOString();

      if (!userRoleSnapshot.exists()) {
        // Create new Google user with default fields
        const newUserData = {
          email: result.user.email,
          role: 'patient',
          status: 'active',
          createdAt: now,
          updatedAt: now,
          last_login: now,
          name: result.user.displayName || result.user.email.split('@')[0],
          provider: 'google'
        };

        try {
          // Create the user document
          await request.POST(`users_roles/${result.user.uid}`, {
            data: newUserData
          }).Execute();
        } catch (createError) {
          console.error('Error creating user document:', createError);

          // If we can't create the document, we'll use the Firebase Admin SDK
          // to create it via a Cloud Function. For now, we'll proceed with the login
          // and set up the user as if the document was created.

          // Log the error for debugging
          console.log('Will proceed with login despite document creation error:', createError.message);
        }

        // Set local storage data
        localStorage.setItem('logged', 'true');
        localStorage.setItem('userRole', 'patient');
        localStorage.setItem('userName', newUserData.name);
        localStorage.setItem('userEmail', newUserData.email);
        localStorage.setItem('sessionStartTime', now);

        commit("isLogged", true);
        return {
          isLogged: true,
          isNewUser: true,
          needsRoleSelection: true,
          userData: newUserData
        };
      }

      const userData = userRoleSnapshot.data();

      // Update last login and provider
      await request.PUT(`users_roles/${result.user.uid}`, {
        data: {
          last_login: now,
          updatedAt: now,
          provider: 'google'
        }
      }).Execute();

      if (userData.status === 'inactive' || userData.status === 'suspended') {
        await signOut(auth);
        commit("isLogged", false);
        commit("error", "Account is not active. Please contact support.");
        return {
          isLogged: false,
          error: "Account is not active. Please contact support."
        };
      }

      // Set local storage data
      localStorage.setItem('logged', 'true');
      localStorage.setItem('userRole', userData.role);
      localStorage.setItem('userName', userData.name);
      localStorage.setItem('userEmail', userData.email);
      localStorage.setItem('sessionStartTime', now);

      // Set session timeout (8 hours)
      const sessionTimeout = 8 * 60 * 60 * 1000;
      localStorage.setItem('sessionTimeout', String(sessionTimeout));

      commit("isLogged", true);

      return {
        isLogged: true,
        isNewUser: false,
        role: userData.role,
        userData: userData
      };
    } catch (firestoreError) {
      console.error('Firestore operation error:', firestoreError);
      // If there's an error with Firestore operations, sign out the user
      await signOut(auth);
      throw firestoreError;
    }
  } catch (error) {
    console.error('Google sign-in error:', error);
    commit("isLogged", false);
    let errorMessage = "An error occurred during Google sign-in";

    if (error.code === 'auth/popup-closed-by-user') {
      errorMessage = "Sign-in cancelled";
    } else if (error.code === 'auth/popup-blocked') {
      errorMessage = "Pop-up blocked by browser";
    } else if (error.code === 'auth/cancelled-popup-request') {
      errorMessage = "Sign-in cancelled";
    }

    commit("error", errorMessage);
    return { isLogged: false, error: errorMessage };
  }
};

export async function LogInWithAppleId({ commit }) {
  try {
    const result = await signInWithPopup(auth, appleAuthProvider);
    const now = new Date().toISOString();

    // Create or update user document
    const userData = {
      email: result.user.email,
      name: result.user.displayName || result.user.email.split('@')[0],
      provider: 'apple',
      last_login: now,
      updatedAt: now
    };

    await request.PUT(`users_roles/${result.user.uid}`, {
      data: userData
    }).Execute();

    commit("isLogged", true);
    return { isLogged: true, userData };
  } catch (error) {
    commit("isLogged", false);
    return { isLogged: false, error: error.message, errorCode: error.code };
  }
}

export async function SignUp({ commit }, payload) {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, payload.email, payload.password);

    // Create the user_roles document
    const now = new Date().toISOString();
    await request.POST(`users_roles/${userCredential.user.uid}`, {
      data: {
        email: payload.email,
        role: 'patient',
        status: 'active',
        createdAt: now,
        updatedAt: now,
        last_login: now,
        name: payload.name || 'Test User' // Add name if provided, otherwise use default
      }
    }).Execute();

    return {
      user: userCredential.user,
      isNewUser: true
    };
  } catch (error) {
    return { error: error.message, errorCode: error.code };
  }
}

export async function SingUpNoPassword({ commit }, payload) {
  const { v4: uuidv4 } = require("uuid");
  return new Promise(async (resolve, reject) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(secondaryAuth, payload.email, uuidv4());
      await sendPasswordResetEmail(secondaryAuth, payload.email);
      await request.POST(`users_roles/${userCredential.user.uid}`, {
        data: { rol: "doctor", studies: payload.studies },
      }).Execute();
      resolve();
    } catch (error) {
      reject(error.message);
    }
  });
}

export async function SignUpPractitioner({ commit }, payload) {
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      payload.email,
      payload.password
    );

    // Create practitioner profile
    await request.POST(`practitioners/${userCredential.user.uid}`, {
      data: {
        name: payload.name,
        email: payload.email,
        specialization: payload.specialization,
        licenseNumber: payload.licenseNumber,
        status: 'active'
      },
    }).Execute();

    // Set up user role
    await request.POST(`users_roles/${userCredential.user.uid}`, {
      data: {
        role: 'practitioner',
        email: payload.email,
        name: payload.name
      },
    }).Execute();

    return { user: userCredential.user };
  } catch (error) {
    return { error: error.message, errorCode: error.code };
  }
}

export const checkSession = ({ commit }) => {
  const sessionStartTime = localStorage.getItem('sessionStartTime');
  const sessionTimeout = Number(localStorage.getItem('sessionTimeout'));

  if (!sessionStartTime || !sessionTimeout) {
    return false;
  }

  const now = new Date().getTime();
  const sessionStart = new Date(sessionStartTime).getTime();
  const timeElapsed = now - sessionStart;

  if (timeElapsed > sessionTimeout) {
    // Session expired
    commit("isLogged", false);
    localStorage.clear();
    return false;
  }

  return true;
};

export function Logout({ commit }) {
  return auth.signOut().then(() => {
    commit("isLogged", false);
    localStorage.clear();
  }).catch((error) => {
    console.error("Logout Error:", error);
  });
}
