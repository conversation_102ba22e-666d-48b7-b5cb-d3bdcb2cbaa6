import { auth } from "@/plugins/firebase/firebase";
import request from "@/Rest";

export default {
  namespaced: true,
  state: {
    requests: [],
    loading: false
  },
  
  mutations: {
    SET_REQUESTS(state, requests) {
      state.requests = requests;
    },
    SET_LOADING(state, loading) {
      state.loading = loading;
    }
  },
  
  actions: {
    async fetchPatientRequests({ commit }) {
      commit('SET_LOADING', true);
      try {
        const requests = await request.GET('patient_requests')
          .WHERE(['status', '==', 'pending'])
          .Execute();
          
        commit('SET_REQUESTS', requests.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })));
      } catch (error) {
        console.error('Error fetching requests:', error);
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    async approveRequest({ dispatch }, requestId) {
      try {
        await request.PUT(`patient_requests/${requestId}`, {
          status: 'approved',
          approvedBy: auth.currentUser.uid,
          approvedAt: new Date()
        }).Execute();
        
        await dispatch('fetchPatientRequests');
      } catch (error) {
        console.error('Error approving request:', error);
        throw error;
      }
    }
  }
};