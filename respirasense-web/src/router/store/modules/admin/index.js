import request from "@/Rest";
import {
  collection,
  query,
  where,
  getDocs,
  getFirestore,
  Timestamp,
  orderBy,
  limit
} from 'firebase/firestore';

const db = getFirestore();

export default {
  namespaced: true,

  state: {
    overviewData: null,
    users: [],
    loading: false,
    error: null
  },

  mutations: {
    SET_OVERVIEW_DATA(state, data) {
      state.overviewData = data;
    },
    SET_LOADING(state, status) {
      state.loading = status;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    SET_USERS(state, users) {
      state.users = users;
    }
  },

  actions: {
    async fetchUsers({ commit }) {
      console.log('🔍 Starting fetchUsers action');
      commit('SET_LOADING', true);
      try {
        const usersRef = collection(db, 'users_roles');
        console.log('📊 Fetching users from Firestore');
        const usersSnapshot = await getDocs(usersRef);

        const users = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log('👥 Fetched users:', users);
        commit('SET_USERS', users);
        return users;
      } catch (error) {
        console.error('❌ Error in fetchUsers:', error);
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
        console.log('✅ fetchUsers action complete');
      }
    },

    async fetchOverviewData({ commit }) {
      console.log('🔍 Starting fetchOverviewData action');
      try {
        // Get total patients
        const patientsRef = collection(db, 'patients');
        const patientsSnapshot = await getDocs(patientsRef);
        const totalPatients = patientsSnapshot.size;
        console.log('👥 Total patients:', totalPatients);

        // Get active practitioners
        const practitionersRef = collection(db, 'practitioners');
        const practitionersSnapshot = await getDocs(practitionersRef);
        const activePractitioners = practitionersSnapshot.size;
        console.log('👨‍⚕️ Active practitioners:', activePractitioners);

        // Get today's readings (simplified)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayTimestamp = Timestamp.fromDate(today);

        const readingsRef = collection(db, 'readings');
        const readingsQuery = query(
          readingsRef,
          where('timestamp', '>=', todayTimestamp)
        );
        const readingsSnapshot = await getDocs(readingsQuery);
        const readingsToday = readingsSnapshot.size;

        // Get active alerts (simplified)
        const alertsRef = collection(db, 'alerts');
        const activeAlertsQuery = query(
          alertsRef,
          where('status', '==', 'active'),
          orderBy('timestamp', 'desc'),
          limit(10)
        );
        const alertsSnapshot = await getDocs(activeAlertsQuery);
        const activeAlerts = alertsSnapshot.size;

        // Get recent alerts
        const recentAlerts = alertsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            message: data.message || 'Alert triggered',
            priority: data.priority || 'medium',
            timestamp: data.timestamp?.toDate() || new Date(),
            patientId: data.patientId
          };
        });

        // Simplified trend calculation
        const patientsTrend = 0; // We'll implement this later

        const overviewData = {
          stats: {
            totalPatients,
            patientsTrend,
            activePractitioners,
            readingsToday,
            activeAlerts
          },
          recentAlerts
        };

        console.log('📊 Overview data:', overviewData);
        commit('SET_OVERVIEW_DATA', overviewData);
        return overviewData;
      } catch (error) {
        console.error('❌ Error in fetchOverviewData:', error);
        throw error;
      }
    },

    async fetchSystemUsage({ commit }) {
      try {
        const now = new Date();
        const last7Days = new Date(now.setDate(now.getDate() - 7));
        const timestamp = Timestamp.fromDate(last7Days);

        const readingsRef = collection(db, 'readings');
        const readingsQuery = query(
          readingsRef,
          where('timestamp', '>=', timestamp),
          orderBy('timestamp', 'desc')
        );

        const snapshot = await getDocs(readingsQuery);

        // Initialize daily counts for last 7 days
        const dailyCounts = {};
        for (let i = 0; i < 7; i++) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          dailyCounts[dateStr] = 0;
        }

        // Fill in actual counts
        snapshot.forEach(doc => {
          const date = doc.data().timestamp.toDate().toISOString().split('T')[0];
          if (dailyCounts[date] !== undefined) {
            dailyCounts[date]++;
          }
        });

        return dailyCounts;
      } catch (error) {
        console.error('Error fetching system usage:', error);
        throw error;
      }
    }
  }
};
