import request from "@/Rest";
import { usersRolesPaths } from "@/common/static_data/api_routes"
import { auth } from "@/plugins/firebase/firebase"

export function reset({commit}){
    commit('RESET')
}

export const FetchUserRolesAndStudies = async ({ commit }) => {
    return new Promise((resolve, reject) => {
        auth.onAuthStateChanged(async function (user) {
           if (!user || user === null) {
                commit("saveUserRol", { rol: "NoAccess", id: "" });
                return resolve();
            } else if (!user.uid) {
                console.error("User object exists but uid is missing");
                return resolve();
            }

            try {
                const userSnap = await request.GET(usersRolesPaths.get(user.uid)).Execute();
                if (userSnap.exists && userSnap.data()) {
                    const userData = userSnap.data();
                    commit("saveUserRol", { 
                        rol: userData.rol, 
                        id: user.uid 
                    });
                }
                resolve();
            } catch (error) {
                console.error("Error fetching user roles:", error);
                reject(error);
            }
        });
    });
};
