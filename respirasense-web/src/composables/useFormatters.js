import { format } from 'date-fns';

/**
 * Composable for formatting data
 * @returns {Object} Formatting utility functions
 */
export function useFormatters() {
  /**
   * Format a decimal number to 2 decimal places
   * @param {number} value - The value to format
   * @returns {string} Formatted decimal value
   */
  const formatDecimal = (value) => {
    if (!value || isNaN(value)) return '--';
    return Number(value).toFixed(2);
  };

  /**
   * Format a date using date-fns
   * @param {Date|string|number} date - The date to format
   * @param {string} formatString - The format string (default: 'MMM dd, yyyy')
   * @returns {string} Formatted date string
   */
  const formatDate = (date, formatString = 'MMM dd, yyyy') => {
    if (!date) return '';
    try {
      // Handle Firestore Timestamp objects
      let dateToFormat = date;

      // If it's a Firestore Timestamp with toDate method
      if (typeof date === 'object' && date.toDate && typeof date.toDate === 'function') {
        console.log('Converting Firestore timestamp to date');
        dateToFormat = date.toDate();
      }
      // If it's a serialized Firestore Timestamp with seconds
      else if (typeof date === 'object' && date.seconds) {
        console.log('Converting serialized Firestore timestamp to date');
        dateToFormat = new Date(date.seconds * 1000);
      }
      // If it's a string that looks like a Firestore timestamp
      else if (typeof date === 'object' && date._seconds) {
        console.log('Converting _seconds Firestore timestamp to date');
        dateToFormat = new Date(date._seconds * 1000);
      }

      // Log the date for debugging
      console.log('Date to format:', dateToFormat);

      const dateObj = dateToFormat instanceof Date ? dateToFormat : new Date(dateToFormat);

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date:', date);
        return 'Invalid date';
      }

      return format(dateObj, formatString);
    } catch (error) {
      console.error('Error formatting date:', error, date);
      return 'Invalid date';
    }
  };

  /**
   * Format a timestamp to time
   * @param {Date|string|number} timestamp - The timestamp to format
   * @returns {string} Formatted time string
   */
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    try {
      // Handle Firestore Timestamp objects
      let timeToFormat = timestamp;

      // If it's a Firestore Timestamp with toDate method
      if (typeof timestamp === 'object' && timestamp.toDate && typeof timestamp.toDate === 'function') {
        timeToFormat = timestamp.toDate();
      }
      // If it's a serialized Firestore Timestamp with seconds
      else if (typeof timestamp === 'object' && timestamp.seconds) {
        timeToFormat = new Date(timestamp.seconds * 1000);
      }
      // If it's a string that looks like a Firestore timestamp
      else if (typeof timestamp === 'object' && timestamp._seconds) {
        timeToFormat = new Date(timestamp._seconds * 1000);
      }

      const dateObj = timeToFormat instanceof Date ? timeToFormat : new Date(timeToFormat);

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid time:', timestamp);
        return 'Invalid time';
      }

      return format(dateObj, 'h:mm a');
    } catch (error) {
      console.error('Error formatting time:', error, timestamp);
      return 'Invalid time';
    }
  };

  /**
   * Format a timestamp to date and time
   * @param {Date|string|number} timestamp - The timestamp to format
   * @returns {string} Formatted date and time string
   */
  const formatDateTime = (timestamp) => {
    if (!timestamp) return '';
    try {
      // Handle Firestore Timestamp objects
      let timeToFormat = timestamp;

      // If it's a Firestore Timestamp with toDate method
      if (typeof timestamp === 'object' && timestamp.toDate && typeof timestamp.toDate === 'function') {
        timeToFormat = timestamp.toDate();
      }
      // If it's a serialized Firestore Timestamp with seconds
      else if (typeof timestamp === 'object' && timestamp.seconds) {
        timeToFormat = new Date(timestamp.seconds * 1000);
      }
      // If it's a string that looks like a Firestore timestamp
      else if (typeof timestamp === 'object' && timestamp._seconds) {
        timeToFormat = new Date(timestamp._seconds * 1000);
      }

      const dateObj = timeToFormat instanceof Date ? timeToFormat : new Date(timeToFormat);

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date/time:', timestamp);
        return 'Invalid date/time';
      }

      return format(dateObj, 'MMM dd, yyyy h:mm a');
    } catch (error) {
      console.error('Error formatting date/time:', error, timestamp);
      return 'Invalid date/time';
    }
  };

  return {
    formatDecimal,
    formatDate,
    formatTime,
    formatDateTime
  };
}
