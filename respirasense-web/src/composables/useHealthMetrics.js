import { computed } from 'vue';

/**
 * Composable for health metrics functionality
 * @param {Object} options - Options object
 * @param {Object} options.lastReading - Ref to the last reading
 * @returns {Object} Health metrics utility functions and computed properties
 */
export function useHealthMetrics({ lastReading }) {
  /**
   * Get the COPD risk level from the last reading
   */
  const copdRiskLevel = computed(() => lastReading.value?.riskLevel || 'normal');

  /**
   * Get the status icon based on the COPD risk level
   */
  const getStatusIcon = computed(() => ({
    'fas fa-check-circle': copdRiskLevel.value === 'normal',
    'fas fa-exclamation-triangle': copdRiskLevel.value === 'warning',
    'fas fa-exclamation-circle': copdRiskLevel.value === 'danger'
  }));

  /**
   * Get the status message based on the COPD risk level
   */
  const getStatusMessage = computed(() => {
    const messages = {
      normal: 'Your readings are within normal range.',
      warning: 'Some metrics require attention. Consider consulting your doctor.',
      danger: 'Immediate medical attention may be required.'
    };
    return messages[copdRiskLevel.value] || messages.normal;
  });

  /**
   * Map risk levels from different formats
   * @param {string} riskLevel - The risk level to map
   * @returns {string} Mapped risk level
   */
  const mapRiskLevel = (riskLevel) => {
    if (!riskLevel) return 'normal';
    
    const riskMap = {
      'low': 'normal',
      'medium': 'warning',
      'high': 'danger',
      'normal': 'normal',
      'warning': 'warning',
      'danger': 'danger'
    };
    
    return riskMap[riskLevel.toLowerCase()] || 'normal';
  };

  /**
   * Get the icon for a metric
   * @param {string} metricKey - The metric key
   * @returns {string} Icon class
   */
  const getMetricIcon = (metricKey) => {
    const icons = {
      respiratoryRate: 'fas fa-lungs',
      oxygenSaturation: 'fas fa-wind',
      heartRate: 'fas fa-heartbeat',
      temperature: 'fas fa-thermometer-half'
    };
    return icons[metricKey] || 'fas fa-chart-line';
  };

  /**
   * Get the standard metrics configuration
   */
  const metrics = [
    { key: 'respiratoryRate', label: 'Respiratory Rate', unit: 'bpm' },
    { key: 'oxygenSaturation', label: 'Oxygen Saturation', unit: '%' },
    { key: 'heartRate', label: 'Heart Rate', unit: 'bpm' },
    { key: 'temperature', label: 'Temperature', unit: '°C' }
  ];

  return {
    copdRiskLevel,
    getStatusIcon,
    getStatusMessage,
    mapRiskLevel,
    getMetricIcon,
    metrics
  };
}
