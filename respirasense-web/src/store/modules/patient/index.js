import { db, auth } from '@/plugins/firebase/firebase';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc,
  updateDoc,
  setDoc
} from 'firebase/firestore';
import health from './health';

export default {
  namespaced: true,

  state: {
    linkedPractitioners: [],
    pendingRequests: [],
    loading: false,
    error: null,
    chats: {},
    currentChat: null
  },

  getters: {
    getLinkedPractitioners: (state) => state.linkedPractitioners,
    getPendingRequests: (state) => state.pendingRequests,
    isLoading: (state) => state.loading,
    getError: (state) => state.error,
    getCurrentChat: (state) => state.currentChat,
    getChatMessages: (state) => (chatId) => state.chats[chatId] || []
  },

  mutations: {
    SET_LINKED_PRACTITIONERS(state, practitioners) {
      state.linkedPractitioners = practitioners;
    },
    SET_PENDING_REQUESTS(state, requests) {
      state.pendingRequests = requests;
    },
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    SET_CURRENT_CHAT(state, chatId) {
      state.currentChat = chatId;
    },
    SET_CHAT_MESSAGES(state, { chatId, messages }) {
      state.chats = {
        ...state.chats,
        [chatId]: messages
      };
    },
    ADD_CHAT_MESSAGE(state, { chatId, message }) {
      if (!state.chats[chatId]) {
        state.chats[chatId] = [];
      }
      state.chats[chatId].push(message);
    }
  },

  actions: {
    async fetch_linked_practitioners({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Get the patient document to check for practitionerId
        const patientRef = doc(db, `patients/${user.uid}`);
        const patientDoc = await getDoc(patientRef);

        if (!patientDoc.exists()) {
          commit('SET_LINKED_PRACTITIONERS', []);
          return [];
        }

        const patientData = patientDoc.data();

        // If patient has a practitionerId, fetch that practitioner
        if (patientData.practitionerId) {
          const practitionerRef = doc(db, `users_roles/${patientData.practitionerId}`);
          const practitionerDoc = await getDoc(practitionerRef);

          if (practitionerDoc.exists()) {
            const practitionerData = practitionerDoc.data();

            // Get additional practitioner details if available
            let additionalDetails = {};
            try {
              const practitionerDetailsRef = doc(db, `practitioners/${patientData.practitionerId}`);
              const practitionerDetailsDoc = await getDoc(practitionerDetailsRef);

              if (practitionerDetailsDoc.exists()) {
                additionalDetails = practitionerDetailsDoc.data();
              }
            } catch (err) {
              console.warn('Could not fetch additional practitioner details:', err);
            }

            const practitioners = [{
              id: practitionerDoc.id,
              name: practitionerData.name || 'Unknown Practitioner',
              email: practitionerData.email,
              status: 'active',
              assignedDate: patientData.practitionerAssignedDate || new Date(),
              specialty: additionalDetails.specialty || 'General Practitioner',
              ...additionalDetails
            }];

            commit('SET_LINKED_PRACTITIONERS', practitioners);
            return practitioners;
          }
        }

        commit('SET_LINKED_PRACTITIONERS', []);
        return [];
      } catch (error) {
        console.error('Error fetching linked practitioners:', error);
        commit('SET_ERROR', error.message || 'Failed to load practitioners');
        commit('SET_LINKED_PRACTITIONERS', []);
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetch_pending_requests({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        const requestsData = [];

        // 1. Query for practitioner-initiated requests for this patient
        const practitionerInitiatedRef = collection(db, 'patient_requests');
        const practitionerInitiatedQuery = query(
          practitionerInitiatedRef,
          where('patientId', '==', user.uid),
          where('status', '==', 'pending'),
          where('requestType', '==', 'practitioner_initiated')
        );

        const practitionerInitiatedSnapshot = await getDocs(practitionerInitiatedQuery);

        // Add practitioner-initiated requests to the results
        practitionerInitiatedSnapshot.forEach((doc) => {
          const data = doc.data();
          requestsData.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate() || new Date(),
            expiresAt: data.expiresAt?.toDate() || null,
            direction: 'incoming'
          });
        });

        // 2. Query for patient-initiated requests from this patient
        const patientInitiatedRef = collection(db, 'patient_requests');
        const patientInitiatedQuery = query(
          patientInitiatedRef,
          where('patientId', '==', user.uid),
          where('status', '==', 'pending'),
          where('requestType', '==', 'patient_initiated')
        );

        const patientInitiatedSnapshot = await getDocs(patientInitiatedQuery);

        // Add patient-initiated requests to the results
        patientInitiatedSnapshot.forEach((doc) => {
          const data = doc.data();
          requestsData.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate() || new Date(),
            expiresAt: data.expiresAt?.toDate() || null,
            direction: 'outgoing'
          });
        });

        commit('SET_PENDING_REQUESTS', requestsData);
        return requestsData;
      } catch (error) {
        console.error('Error fetching pending requests:', error);
        commit('SET_ERROR', error.message || 'Failed to load pending requests');
        commit('SET_PENDING_REQUESTS', []);
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async send_chat_message({ commit }, { practitionerId, message }) {
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Create a chat ID that's the same regardless of who initiates
        const chatId = [user.uid, practitionerId].sort().join('_');

        // Add the message to the chat
        const messageRef = await addDoc(collection(db, `chats/${chatId}/messages`), {
          text: message.trim(),
          senderId: user.uid,
          senderName: user.displayName || 'Patient',
          receiverId: practitionerId,
          timestamp: serverTimestamp(),
          read: false
        });

        // Update the chat document with the last message
        const chatRef = doc(db, `chats/${chatId}`);
        await updateDoc(chatRef, {
          lastMessage: message.trim(),
          lastMessageTime: serverTimestamp(),
          updatedAt: serverTimestamp()
        }, { merge: true });

        return {
          success: true,
          messageId: messageRef.id
        };
      } catch (error) {
        console.error('Error sending chat message:', error);
        commit('SET_ERROR', error.message || 'Failed to send message');
        return {
          success: false,
          error: error.message || 'Failed to send message'
        };
      }
    },

    async fetch_chat_messages({ commit }, practitionerId) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Create a chat ID that's the same regardless of who initiates
        const chatId = [user.uid, practitionerId].sort().join('_');

        // Set the current chat
        commit('SET_CURRENT_CHAT', chatId);

        // Create a query for messages in this chat
        const messagesRef = collection(db, `chats/${chatId}/messages`);
        const messagesQuery = query(
          messagesRef,
          orderBy('timestamp', 'asc'),
          limit(100)
        );

        // Get the messages
        const querySnapshot = await getDocs(messagesQuery);

        // Map the documents to an array of message data
        const messagesData = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          messagesData.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp?.toDate() || new Date()
          });
        });

        commit('SET_CHAT_MESSAGES', { chatId, messages: messagesData });
        return messagesData;
      } catch (error) {
        console.error('Error fetching chat messages:', error);
        commit('SET_ERROR', error.message || 'Failed to load messages');
        commit('SET_CHAT_MESSAGES', { chatId: null, messages: [] });
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async request_practitioner_linking({ commit }, practitionerEmail) {
      commit('SET_LOADING', true);
      try {
        // Validate email
        if (!practitionerEmail || typeof practitionerEmail !== 'string' || practitionerEmail.trim() === '') {
          return {
            success: false,
            error: 'Please enter a valid email address'
          };
        }

        const user = auth.currentUser;
        if (!user) {
          return {
            success: false,
            error: 'No authenticated user found'
          };
        }

        // Clean the email
        const cleanEmail = practitionerEmail.trim().toLowerCase();

        // Check if the practitioner exists in users_roles
        const usersRef = collection(db, 'users_roles');
        const q = query(
          usersRef,
          where('email', '==', cleanEmail),
          where('role', '==', 'practitioner')
        );

        const querySnapshot = await getDocs(q);
        let practitionerExists = false;
        let practitionerId = null;

        if (!querySnapshot.empty) {
          const practitionerDoc = querySnapshot.docs[0];
          practitionerExists = true;
          practitionerId = practitionerDoc.id;

          // Check if patient document exists, if not create it
          const patientRef = doc(db, `patients/${user.uid}`);
          const patientDoc = await getDoc(patientRef);

          if (!patientDoc.exists()) {
            // Create the patient document if it doesn't exist
            try {
              await setDoc(patientRef, {
                email: user.email,
                name: user.displayName || 'Patient',
                createdAt: serverTimestamp()
              });
              console.log('Created patient document for user:', user.uid);
            } catch (error) {
              console.error('Error creating patient document:', error);
              // Continue anyway, as we still want to create the request
            }
          } else if (patientDoc.data().practitionerId === practitionerId) {
            // Check if already linked
            return {
              success: false,
              error: 'You are already linked with this practitioner'
            };
          }
        }

        // Ensure patient document exists even if practitioner doesn't exist
        if (!practitionerExists) {
          const patientRef = doc(db, `patients/${user.uid}`);
          const patientDoc = await getDoc(patientRef);

          if (!patientDoc.exists()) {
            try {
              await setDoc(patientRef, {
                email: user.email,
                name: user.displayName || 'Patient',
                createdAt: serverTimestamp()
              });
              console.log('Created patient document for user:', user.uid);
            } catch (error) {
              console.error('Error creating patient document:', error);
              // Continue anyway, as we still want to create the request
            }
          }
        }

        // Check if a request already exists
        const requestsRef = collection(db, 'patient_requests');
        const existingRequestQuery = query(
          requestsRef,
          where('patientId', '==', user.uid),
          where('practitionerEmail', '==', cleanEmail),
          where('status', '==', 'pending')
        );

        const existingRequests = await getDocs(existingRequestQuery);

        if (!existingRequests.empty) {
          return {
            success: true,
            requestId: existingRequests.docs[0].id,
            message: 'A request to this practitioner is already pending'
          };
        }

        // Create the request
        const requestData = {
          patientId: user.uid,
          patientEmail: user.email,
          patientName: user.displayName || 'Patient',
          practitionerEmail: cleanEmail,
          practitionerId: practitionerExists ? practitionerId : null,
          requestType: 'patient_initiated',
          status: 'pending',
          message: 'Patient initiated linking request',
          createdAt: serverTimestamp()
        };

        const newRequest = await addDoc(collection(db, 'patient_requests'), requestData);

        return {
          success: true,
          requestId: newRequest.id,
          practitionerExists
        };
      } catch (error) {
        console.error('Error requesting practitioner linking:', error);
        commit('SET_ERROR', error.message || 'Failed to send practitioner linking request');
        return {
          success: false,
          error: error.message || 'Failed to send request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async cancel_linking_request({ commit, dispatch }, requestId) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          return {
            success: false,
            error: 'No authenticated user found'
          };
        }

        // Get the request document
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          return {
            success: false,
            error: 'Request not found'
          };
        }

        const requestData = requestDoc.data();

        // Verify that the user is authorized to cancel this request
        if (requestData.patientId !== user.uid) {
          return {
            success: false,
            error: 'You are not authorized to cancel this request'
          };
        }

        // Update the request status to cancelled
        await updateDoc(requestRef, {
          status: 'cancelled',
          responseAt: serverTimestamp()
        });

        // Refresh the pending requests
        await dispatch('fetch_pending_requests');

        return {
          success: true,
          message: 'Request cancelled successfully'
        };
      } catch (error) {
        console.error('Error cancelling linking request:', error);
        commit('SET_ERROR', error.message || 'Failed to cancel request');
        return {
          success: false,
          error: error.message || 'Failed to cancel request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async approve_linking_request({ commit, dispatch }, requestId) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          return {
            success: false,
            error: 'No authenticated user found'
          };
        }

        // Get the request document
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          return {
            success: false,
            error: 'Request not found'
          };
        }

        const requestData = requestDoc.data();
        const practitionerId = requestData.practitionerId;

        // Verify that the user is authorized to approve this request
        if (requestData.patientId !== user.uid) {
          return {
            success: false,
            error: 'You are not authorized to approve this request'
          };
        }

        // Update the request status to approved
        await updateDoc(requestRef, {
          status: 'approved',
          responseAt: serverTimestamp()
        });

        // Update the patient document with practitioner reference
        const patientRef = doc(db, `patients/${user.uid}`);
        await updateDoc(patientRef, {
          practitionerId: practitionerId,
          practitionerAssignedDate: serverTimestamp()
        });

        // Add patient to practitioner's patients subcollection
        const practitionerPatientRef = doc(db, `practitioners/${practitionerId}/patients/${user.uid}`);
        await setDoc(practitionerPatientRef, {
          name: user.displayName || 'Patient',
          email: user.email,
          lastUpdate: serverTimestamp()
        });

        // Refresh the pending requests and linked practitioners
        await dispatch('fetch_pending_requests');
        await dispatch('fetch_linked_practitioners');

        return {
          success: true,
          message: 'Request approved successfully'
        };
      } catch (error) {
        console.error('Error approving linking request:', error);
        commit('SET_ERROR', error.message || 'Failed to approve request');
        return {
          success: false,
          error: error.message || 'Failed to approve request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async reject_linking_request({ commit, dispatch }, requestId) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          return {
            success: false,
            error: 'No authenticated user found'
          };
        }

        // Get the request document
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          return {
            success: false,
            error: 'Request not found'
          };
        }

        const requestData = requestDoc.data();

        // Verify that the user is authorized to reject this request
        // For patient-initiated requests, the practitioner rejects
        // For practitioner-initiated requests, the patient rejects
        if (
          (requestData.requestType === 'patient_initiated' && requestData.practitionerId !== user.uid) ||
          (requestData.requestType === 'practitioner_initiated' && requestData.patientId !== user.uid)
        ) {
          return {
            success: false,
            error: 'You are not authorized to reject this request'
          };
        }

        // Update the request status to rejected
        await updateDoc(requestRef, {
          status: 'rejected',
          responseAt: serverTimestamp()
        });

        // Refresh the pending requests
        await dispatch('fetch_pending_requests');

        return {
          success: true,
          message: 'Request rejected successfully'
        };
      } catch (error) {
        console.error('Error rejecting linking request:', error);
        commit('SET_ERROR', error.message || 'Failed to reject request');
        return {
          success: false,
          error: error.message || 'Failed to reject request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    }
  },

  modules: {
    health
  }
};
