import { auth } from '@/plugins/firebase/firebase';
import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';
import { establishPatientPractitionerLink } from '@/utils/patient-practitioner-linking';

/**
 * Enhanced action to approve a practitioner linking request
 * Uses the establishPatientPractitionerLink utility for consistent data updates
 */
export const approve_linking_request = async ({ commit, dispatch }, requestId) => {
  commit('SET_LOADING', true);
  try {
    const user = auth.currentUser;
    if (!user) {
      return {
        success: false,
        error: 'No authenticated user found'
      };
    }

    // Get the request document
    const requestRef = doc(db, `patient_requests/${requestId}`);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      return {
        success: false,
        error: 'Request not found'
      };
    }

    const requestData = requestDoc.data();
    const practitionerId = requestData.practitionerId;

    // Verify that the user is authorized to approve this request
    if (requestData.patientId !== user.uid) {
      return {
        success: false,
        error: 'You are not authorized to approve this request'
      };
    }

    // Use the utility function to establish the link
    const result = await establishPatientPractitionerLink(user.uid, practitionerId, requestId);

    if (!result.success) {
      return result;
    }

    // Refresh the pending requests and linked practitioners
    await dispatch('fetch_pending_requests');
    await dispatch('fetch_linked_practitioners');

    return {
      success: true,
      message: 'Request approved successfully'
    };
  } catch (error) {
    console.error('Error approving linking request:', error);
    commit('SET_ERROR', error.message || 'Failed to approve request');
    return {
      success: false,
      error: error.message || 'Failed to approve request'
    };
  } finally {
    commit('SET_LOADING', false);
  }
};
