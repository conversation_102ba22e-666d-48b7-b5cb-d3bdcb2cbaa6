import {
  collection,
  query,
  where,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '@/plugins/firebase/firebase';
import {
  getPractitionerPatients,
  getPatientLinkingRequests,
  removePatientFromPractitioner,
  requestPatientLinking as requestPatientLinkingHelper // Alias to avoid naming collision with snake_case action
} from '@/utils/firestore-helpers';

export default {
  namespaced: true,

  state: {
    patients: [],
    pendingRequests: [],
    recentActivity: [],
    selectedPatient: null,
    patientReadings: [],
    patientLinkingRequests: [],
    loading: false,
    error: null
  },

  mutations: {
    SET_LOADING(state, status) {
      state.loading = status;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    SET_PATIENTS(state, patients) {
      state.patients = patients;
    },
    SET_PENDING_REQUESTS(state, requests) {
      state.pendingRequests = requests;
    },
    SET_RECENT_ACTIVITY(state, activity) {
      state.recentActivity = activity;
    },
    SET_SELECTED_PATIENT(state, patient) {
      state.selectedPatient = patient;
    },
    SET_PATIENT_READINGS(state, readings) {
      state.patientReadings = readings;
    },
    REMOVE_REQUEST(state, requestId) {
      state.pendingRequests = state.pendingRequests.filter(r => r.id !== requestId);
    },
    SET_PATIENT_LINKING_REQUESTS(state, requests) {
      state.patientLinkingRequests = requests;
    }
  },

  actions: {
    async fetch_patient_activities({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Get all patients assigned to this practitioner
        const patients = await getPractitionerPatients(user.uid);

        if (patients.length === 0) {
          return [];
        }

        // Get patient IDs
        const patientIds = patients.map(patient => patient.id);

        // Collect activities from all patients
        const activities = [];

        // Process each patient to get their activities
        for (const patientId of patientIds) {
          try {
            // Get patient info
            const patientRef = doc(db, `patients/${patientId}`);
            const patientDoc = await getDoc(patientRef);
            const patientData = patientDoc.exists() ? patientDoc.data() : {};

            // Get user role info for name
            const userRoleRef = doc(db, `users_roles/${patientId}`);
            const userRoleDoc = await getDoc(userRoleRef);
            const userData = userRoleDoc.exists() ? userRoleDoc.data() : {};

            // Get recent readings (last 10)
            const readingsRef = collection(db, `patients/${patientId}/readings`);
            const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(10));
            const readingSnapshot = await getDocs(q);

            if (!readingSnapshot.empty) {
              // Add each reading as an activity
              readingSnapshot.docs.forEach(doc => {
                const readingData = doc.data();
                activities.push({
                  id: doc.id,
                  patientId: patientId,
                  patientName: userData.name || patientData.name || 'Unknown Patient',
                  type: 'reading',
                  timestamp: readingData.timestamp?.toDate() || new Date(),
                  description: `Took a health reading`,
                  details: {
                    respiratoryRate: readingData.respiratoryRate,
                    oxygenSaturation: readingData.oxygenSaturation,
                    heartRate: readingData.heartRate,
                    temperature: readingData.temperature,
                    riskLevel: readingData.riskLevel
                  }
                });
              });
            }
          } catch (error) {
            console.error(`Error processing activities for patient ${patientId}:`, error);
          }
        }

        // Sort by timestamp (most recent first)
        activities.sort((a, b) => b.timestamp - a.timestamp);

        return activities;
      } catch (error) {
        console.error('Error fetching patient activities:', error);
        commit('SET_ERROR', 'Failed to load patient activities');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetch_patients({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        const patients = await getPractitionerPatients(user.uid);

        // Get additional patient data from users_roles collection
        const enhancedPatients = await Promise.all(patients.map(async (patient) => {
          try {
            const userRoleRef = doc(db, `users_roles/${patient.id}`);
            const userRoleDoc = await getDoc(userRoleRef);

            if (userRoleDoc.exists()) {
              const userData = userRoleDoc.data();
              return {
                ...patient,
                name: userData.name || patient.name,
                email: userData.email || patient.email,
                status: userData.status || 'active'
              };
            }
            return patient;
          } catch (error) {
            console.error(`Error fetching additional data for patient ${patient.id}:`, error);
            return patient;
          }
        }));

        commit('SET_PATIENTS', enhancedPatients);
        return enhancedPatients;
      } catch (error) {
        console.error('Error fetching patients:', error);
        commit('SET_ERROR', 'Failed to load patients');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetch_pending_requests({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // First try with the composite index
        try {
          // Query patient_requests collection for pending requests assigned to this practitioner
          const requestsRef = collection(db, 'patient_requests');
          const q = query(
            requestsRef,
            where('practitionerId', '==', user.uid),
            where('status', '==', 'pending'),
            orderBy('createdAt', 'desc')
          );

          const querySnapshot = await getDocs(q);

          // Map the documents to an array of request data
          const requests = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date()
          }));

          commit('SET_PENDING_REQUESTS', requests);
          return requests;
        } catch (indexError) {
          console.warn('Index error, falling back to simpler query:', indexError);

          // Fallback to a simpler query if the index doesn't exist yet
          const requestsRef = collection(db, 'patient_requests');
          const q = query(
            requestsRef,
            where('practitionerId', '==', user.uid)
          );

          const querySnapshot = await getDocs(q);

          // Filter and map the documents manually
          const requests = [];
          querySnapshot.forEach(doc => {
            const data = doc.data();
            if (data.status === 'pending') {
              requests.push({
                id: doc.id,
                ...data,
                createdAt: data.createdAt?.toDate() || new Date()
              });
            }
          });

          // Sort manually since we can't use orderBy
          requests.sort((a, b) => b.createdAt - a.createdAt);

          commit('SET_PENDING_REQUESTS', requests);
          return requests;
        }
      } catch (error) {
        console.error('Error fetching pending requests:', error);
        commit('SET_ERROR', 'Failed to load pending requests');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetch_recent_activity({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Get all patients assigned to this practitioner
        const patients = await getPractitionerPatients(user.uid);

        if (patients.length === 0) {
          commit('SET_RECENT_ACTIVITY', []);
          return [];
        }

        // Get patient IDs
        const patientIds = patients.map(patient => patient.id);

        // Process each patient to get their latest reading
        const recentActivity = [];

        for (const patientId of patientIds) {
          try {
            // Get patient info
            const patientRef = doc(db, `patients/${patientId}`);
            const patientDoc = await getDoc(patientRef);
            const patientData = patientDoc.exists() ? patientDoc.data() : {};

            // Get user role info for name
            const userRoleRef = doc(db, `users_roles/${patientId}`);
            const userRoleDoc = await getDoc(userRoleRef);
            const userData = userRoleDoc.exists() ? userRoleDoc.data() : {};

            // Get latest reading
            const readingsRef = collection(db, `patients/${patientId}/readings`);
            const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(1));
            const readingSnapshot = await getDocs(q);

            if (!readingSnapshot.empty) {
              const latestReading = readingSnapshot.docs[0];
              const readingData = latestReading.data();

              recentActivity.push({
                id: patientId,
                name: userData.name || patientData.name || 'Unknown Patient',
                email: userData.email || patientData.email,
                lastUpdate: readingData.timestamp?.toDate() || new Date(),
                riskLevel: readingData.riskLevel || 'normal',
                readingId: latestReading.id
              });
            } else {
              // Include patient even without readings
              recentActivity.push({
                id: patientId,
                name: userData.name || patientData.name || 'Unknown Patient',
                email: userData.email || patientData.email,
                lastUpdate: null,
                riskLevel: 'unknown',
                readingId: null
              });
            }
          } catch (error) {
            console.error(`Error processing patient ${patientId}:`, error);
          }
        }

        // Sort by last update time (most recent first)
        recentActivity.sort((a, b) => {
          if (!a.lastUpdate) return 1;
          if (!b.lastUpdate) return -1;
          return b.lastUpdate - a.lastUpdate;
        });

        commit('SET_RECENT_ACTIVITY', recentActivity);
        return recentActivity;
      } catch (error) {
        console.error('Error fetching recent activity:', error);
        commit('SET_ERROR', 'Failed to load recent patient activity');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async approve_request({ commit, dispatch }, requestId) {
      try {
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          throw new Error('Request not found');
        }

        // Verify the request exists, but we don't need the data

        // Update the request status
        await updateDoc(requestRef, {
          status: 'approved',
          approvedBy: auth.currentUser.uid,
          approvedAt: serverTimestamp()
        });

        // Remove from pending requests
        commit('REMOVE_REQUEST', requestId);

        // Refresh data
        await dispatch('fetch_pending_requests');
        await dispatch('fetch_patients');

        return true;
      } catch (error) {
        console.error('Error approving request:', error);
        throw error;
      }
    },

    async reject_request({ commit, dispatch }, requestId) {
      try {
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          throw new Error('Request not found');
        }

        // Verify the request exists, but we don't need the data

        // Update the request status
        await updateDoc(requestRef, {
          status: 'rejected',
          rejectedBy: auth.currentUser.uid,
          rejectedAt: serverTimestamp()
        });

        // Remove from pending requests
        commit('REMOVE_REQUEST', requestId);

        // Refresh data
        await dispatch('fetch_pending_requests');

        return true;
      } catch (error) {
        console.error('Error rejecting request:', error);
        throw error;
      }
    },

    async get_patient_details({ commit }, patientId) {
      commit('SET_LOADING', true);
      try {
        // Get patient data from users_roles
        const userRoleRef = doc(db, `users_roles/${patientId}`);
        const userRoleDoc = await getDoc(userRoleRef);

        if (!userRoleDoc.exists()) {
          throw new Error('Patient not found');
        }

        const userData = userRoleDoc.data();

        // Get additional patient data from patients collection
        const patientRef = doc(db, `patients/${patientId}`);
        const patientDoc = await getDoc(patientRef);
        const patientData = patientDoc.exists() ? patientDoc.data() : {};

        // Get patient profile if available
        const profileRef = doc(db, `patients/${patientId}/profile/details`);
        const profileDoc = await getDoc(profileRef);
        const profileData = profileDoc.exists() ? profileDoc.data() : {};

        const patientDetails = {
          id: patientId,
          name: userData.name || patientData.name || 'Unknown Patient',
          email: userData.email || patientData.email,
          status: userData.status || 'active',
          age: profileData.age,
          height: profileData.height,
          weight: profileData.weight,
          gender: profileData.gender,
          medicalHistory: profileData.medicalHistory || [],
          medications: profileData.medications || [],
          createdAt: userData.createdAt || patientData.createdAt,
          lastUpdate: patientData.lastUpdate
        };

        commit('SET_SELECTED_PATIENT', patientDetails);
        return patientDetails;
      } catch (error) {
        console.error('Error fetching patient details:', error);
        commit('SET_ERROR', 'Failed to load patient details');
        return null;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async get_patient_readings({ commit }, patientId) {
      commit('SET_LOADING', true);
      try {
        // Query readings collection for this patient (not health_readings)
        const readingsRef = collection(db, `patients/${patientId}/readings`);
        const q = query(readingsRef, orderBy('timestamp', 'desc'), limit(20));
        const readingSnapshot = await getDocs(q);

        // Map the documents to an array of reading data
        const readings = readingSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date()
        }));

        commit('SET_PATIENT_READINGS', readings);
        return readings;
      } catch (error) {
        console.error('Error fetching patient readings:', error);
        commit('SET_ERROR', 'Failed to load patient readings');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async fetch_patient_linking_requests({ commit }) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Get patient linking requests from firestore-helpers
        const requests = await getPatientLinkingRequests(user.uid);

        commit('SET_PATIENT_LINKING_REQUESTS', requests);
        return requests;
      } catch (error) {
        console.error('Error fetching patient linking requests:', error);
        commit('SET_ERROR', 'Failed to load patient linking requests');
        return [];
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async cancel_patient_linking_request({ commit, dispatch }, requestId) {
      commit('SET_LOADING', true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Get the request document
        const requestRef = doc(db, `patient_requests/${requestId}`);
        const requestDoc = await getDoc(requestRef);

        if (!requestDoc.exists()) {
          throw new Error('Request not found');
        }

        const requestData = requestDoc.data();

        // Verify that the user is authorized to cancel this request
        if (requestData.practitionerId !== user.uid) {
          throw new Error('You are not authorized to cancel this request');
        }

        // Update the request status to cancelled
        await updateDoc(requestRef, {
          status: 'cancelled',
          responseAt: serverTimestamp()
        });

        // Refresh the requests list
        await dispatch('fetch_patient_linking_requests');

        return {
          success: true,
          message: 'Request cancelled successfully'
        };
      } catch (error) {
        console.error('Error cancelling patient linking request:', error);
        commit('SET_ERROR', error.message || 'Failed to cancel request');
        return {
          success: false,
          error: error.message || 'Failed to cancel request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async request_patient_linking({ commit }, email) {
      commit('SET_LOADING', true);
      try {
        // Debug log to see what's being passed
        console.log('Patient email received in store action:', email);

        // Validate email
        if (!email || typeof email !== 'string' || email.trim() === '') {
          console.error('Invalid email in store action:', email);
          return {
            success: false,
            error: 'Please enter a valid email address'
          };
        }

        const user = auth.currentUser;
        if (!user) {
          return {
            success: false,
            error: 'No authenticated user found'
          };
        }

        // Call the helper function with the practitioner ID and patient email
        // Make sure to trim the email before passing it
        const cleanEmail = email.trim().toLowerCase();
        console.log('Sending cleaned email to helper:', cleanEmail);

        // Call the helper function with the correct parameters
        // Note: We're using the aliased helper function to avoid naming collision
        const result = await requestPatientLinkingHelper(user.uid, cleanEmail);
        console.log('Result from helper:', result);

        if (!result.success) {
          commit('SET_ERROR', result.error || 'Failed to send patient linking request');
        } else {
          // Refresh the requests list if successful
          await this.dispatch('practitioner/fetch_patient_linking_requests');
        }

        return result;
      } catch (error) {
        console.error('Error requesting patient linking:', error);
        commit('SET_ERROR', error.message || 'Failed to send patient linking request');
        return {
          success: false,
          error: error.message || 'Failed to send patient linking request'
        };
      } finally {
        commit('SET_LOADING', false);
      }
    }
  },

  getters: {
    getPatients: state => state.patients,
    getPendingRequests: state => state.pendingRequests,
    getRecentActivity: state => state.recentActivity,
    getSelectedPatient: state => state.selectedPatient,
    getPatientReadings: state => state.patientReadings,
    getPatientLinkingRequests: state => state.patientLinkingRequests,
    isLoading: state => state.loading,
    getError: state => state.error,
    getActivePatientCount: state => state.patients.filter(p => p.status === 'active').length,
    getPendingRequestsCount: state => state.pendingRequests.length,
    getPendingLinkingRequestsCount: state => {
      return state.patientLinkingRequests.filter(r => r.status === 'pending').length;
    }
  }
};
