import { auth } from '@/plugins/firebase/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';
import { establishPatientPractitionerLink } from '@/utils/patient-practitioner-linking';

/**
 * Enhanced action to approve a patient linking request
 * Uses the establishPatientPractitionerLink utility for consistent data updates
 */
export const approve_request = async ({ commit, dispatch }, requestId) => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Get the request document
    const requestRef = doc(db, `patient_requests/${requestId}`);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      throw new Error('Request not found');
    }

    const requestData = requestDoc.data();
    const patientId = requestData.patientId;

    // Verify that the user is authorized to approve this request
    if (requestData.practitionerId !== user.uid) {
      throw new Error('You are not authorized to approve this request');
    }

    // Use the utility function to establish the link
    const result = await establishPatientPractitionerLink(patientId, user.uid, requestId);

    if (!result.success) {
      throw new Error(result.error || 'Failed to establish link');
    }

    // Remove from pending requests
    commit('REMOVE_REQUEST', requestId);

    // Refresh data
    await dispatch('fetch_pending_requests');
    await dispatch('fetch_patients');

    return true;
  } catch (error) {
    console.error('Error approving request:', error);
    throw error;
  }
};
