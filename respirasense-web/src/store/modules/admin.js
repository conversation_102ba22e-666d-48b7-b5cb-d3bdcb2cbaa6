import {
  collection,
  query,
  getDocs,
  doc,
  updateDoc,
  orderBy,
  where,
  getDoc,
  Timestamp,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';

export default {
  namespaced: true,

  state: {
    users: [],
    practitioners: [],
    patients: [],
    analytics: null,
    systemHealth: null,
    loading: false,
    error: null
  },

  mutations: {
    SET_LOADING(state, status) {
      state.loading = status;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    SET_USERS(state, users) {
      state.users = users;
    },
    SET_PRACTITIONERS(state, practitioners) {
      state.practitioners = practitioners;
    },
    SET_PATIENTS(state, patients) {
      state.patients = patients;
    },
    SET_ANALYTICS(state, analytics) {
      state.analytics = analytics;
    },
    SET_SYSTEM_HEALTH(state, health) {
      state.systemHealth = health;
    },
    UPDATE_USER_STATUS(state, { userId, status }) {
      const userIndex = state.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        state.users[userIndex].status = status;
      }
    },
    UPDATE_PRACTITIONER_STATUS(state, { practitionerId, status }) {
      const index = state.practitioners.findIndex(p => p.id === practitionerId);
      if (index !== -1) {
        state.practitioners[index].status = status;
      }
    }
  },

  actions: {
    // User Management Actions
    async fetchUsers({ commit }) {
      console.log('📊 Fetching users from Firestore');
      commit('SET_LOADING', true);
      try {
        const usersRef = collection(db, 'users_roles');
        const q = query(
          usersRef,
          orderBy('createdAt', 'desc')
        );

        const snapshot = await getDocs(q);
        const users = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          // Convert string dates to Date objects if needed
          createdAt: doc.data().createdAt ? new Date(doc.data().createdAt) : new Date(),
          updatedAt: doc.data().updatedAt ? new Date(doc.data().updatedAt) : new Date(),
          last_login: doc.data().last_login ? new Date(doc.data().last_login) : null
        }));

        commit('SET_USERS', users);
        return users;
      } catch (error) {
        console.error('❌ Error in fetchUsers:', error);
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
        console.log('✅ fetchUsers action complete');
      }
    },

    async fetchPendingRegistrations({ commit }) {
      commit('SET_LOADING', true);
      try {
        const usersRef = collection(db, 'users_roles');
        const q = query(
          usersRef,
          where('status', '==', 'pending'),
          orderBy('createdAt', 'desc')
        );

        const snapshot = await getDocs(q);
        const pendingUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt ? new Date(doc.data().createdAt) : new Date(),
          updatedAt: doc.data().updatedAt ? new Date(doc.data().updatedAt) : new Date()
        }));

        return pendingUsers;
      } catch (error) {
        console.error('Error fetching pending registrations:', error);
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async updateUserStatus({ commit }, { userId, status }) {
      try {
        const now = new Date().toISOString();
        const userRef = doc(db, 'users_roles', userId);
        await updateDoc(userRef, {
          status,
          updatedAt: now
        });

        commit('UPDATE_USER_STATUS', { userId, status });
        return true;
      } catch (error) {
        console.error('Error updating user status:', error);
        throw error;
      }
    },

    async approveRegistration({ commit }, userId) {
      try {
        const now = new Date().toISOString();
        const userRef = doc(db, 'users_roles', userId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          throw new Error(`User with ID ${userId} not found`);
        }

        const userData = userDoc.data();

        // Update the user status to active
        await updateDoc(userRef, {
          status: 'active',
          updatedAt: now
        });

        // If this is a practitioner, create the practitioner document if it doesn't exist
        if (userData.role === 'practitioner') {
          const practitionerRef = doc(db, `practitioners/${userId}`);
          const practitionerDoc = await getDoc(practitionerRef);

          if (!practitionerDoc.exists()) {
            // Create practitioner profile
            await updateDoc(practitionerRef, {
              name: userData.name,
              email: userData.email,
              specialization: userData.specialty || '',
              licenseNumber: userData.licenseNumber || '',
              status: 'active',
              createdAt: now,
              updatedAt: now
            });
          }
        }

        commit('UPDATE_USER_STATUS', { userId, status: 'active' });
        return true;
      } catch (error) {
        console.error('Error approving registration:', error);
        throw error;
      }
    },

    async rejectRegistration({ commit }, userId) {
      try {
        const now = new Date().toISOString();
        const userRef = doc(db, 'users_roles', userId);

        // Update the user status to rejected
        await updateDoc(userRef, {
          status: 'rejected',
          updatedAt: now
        });

        commit('UPDATE_USER_STATUS', { userId, status: 'rejected' });
        return true;
      } catch (error) {
        console.error('Error rejecting registration:', error);
        throw error;
      }
    },

    async updateUserRole({ commit }, { userId, role }) {
      try {
        const now = new Date().toISOString();
        const userRef = doc(db, 'users_roles', userId);
        await updateDoc(userRef, {
          role,
          updatedAt: now
        });

        return true;
      } catch (error) {
        console.error('Error updating user role:', error);
        throw error;
      }
    },

    async updateUser({ commit }, { userId, ...userData }) {
      try {
        const now = new Date().toISOString();
        const userRef = doc(db, 'users_roles', userId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          throw new Error(`User with ID ${userId} not found`);
        }

        const user = userDoc.data();
        const batch = writeBatch(db);

        // Update the user_roles document
        batch.update(userRef, {
          ...userData,
          updatedAt: now
        });

        // If this is a patient and practitionerId is being updated
        if (user.role === 'patient' && userData.practitionerId !== undefined) {
          // If practitionerId is provided, create the relationship
          if (userData.practitionerId) {
            // Get patient name and email
            const patientName = userData.name || user.name;
            const patientEmail = userData.email || user.email;

            // Create/update reference in practitioner's patients subcollection
            const practitionerPatientRef = doc(db, `practitioners/${userData.practitionerId}/patients/${userId}`);
            batch.set(practitionerPatientRef, {
              name: patientName,
              email: patientEmail,
              lastUpdate: serverTimestamp()
            }, { merge: true });

            // Update patient document if it exists
            const patientRef = doc(db, `patients/${userId}`);
            const patientDoc = await getDoc(patientRef);

            if (patientDoc.exists()) {
              batch.update(patientRef, {
                practitionerId: userData.practitionerId,
                lastUpdate: serverTimestamp()
              });
            } else {
              batch.set(patientRef, {
                name: patientName,
                email: patientEmail,
                practitionerId: userData.practitionerId,
                createdAt: serverTimestamp(),
                lastUpdate: serverTimestamp()
              });
            }
          } else if (userData.practitionerId === null) {
            // If practitionerId is null, remove the relationship
            // First, get the current practitionerId
            const currentPractitionerId = user.practitionerId;

            if (currentPractitionerId) {
              // Remove reference from practitioner's patients subcollection
              const practitionerPatientRef = doc(db, `practitioners/${currentPractitionerId}/patients/${userId}`);
              batch.delete(practitionerPatientRef);

              // Update patient document if it exists
              const patientRef = doc(db, `patients/${userId}`);
              const patientDoc = await getDoc(patientRef);

              if (patientDoc.exists()) {
                batch.update(patientRef, {
                  practitionerId: null,
                  lastUpdate: serverTimestamp()
                });
              }
            }
          }
        }

        // Commit all the writes
        await batch.commit();

        return true;
      } catch (error) {
        console.error('Error updating user:', error);
        throw error;
      }
    },

    // Practitioner Management Actions
    async fetchPractitioners({ commit }) {
      commit('SET_LOADING', true);
      try {
        const usersRef = collection(db, 'users_roles');
        const q = query(
          usersRef,
          where('role', '==', 'practitioner'),
          orderBy('created_at', 'desc')
        );

        const snapshot = await getDocs(q);
        const practitioners = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          created_at: doc.data().created_at ? new Date(doc.data().created_at) : null,
          updatedAt: doc.data().updatedAt ? new Date(doc.data().updatedAt) : null,
          last_login: doc.data().last_login ? new Date(doc.data().last_login) : null
        }));

        // Get patient counts for each practitioner
        for (const practitioner of practitioners) {
          const patientCount = await this.dispatch('admin/getPractitionerPatientCount', practitioner.id);
          practitioner.patientCount = patientCount;
        }

        commit('SET_PRACTITIONERS', practitioners);
        return practitioners;
      } catch (error) {
        console.error('Error fetching practitioners:', error);
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async getPractitionerPatientCount(_, practitionerId) {
      try {
        const patientsRef = collection(db, 'users_roles');
        const q = query(
          patientsRef,
          where('role', '==', 'patient'),
          where('practitionerId', '==', practitionerId)
        );

        const snapshot = await getDocs(q);
        return snapshot.size;
      } catch (error) {
        console.error('Error getting practitioner patient count:', error);
        return 0;
      }
    },

    async updatePractitionerStatus({ commit }, { practitionerId, status }) {
      try {
        const now = new Date().toISOString();
        const practitionerRef = doc(db, 'users_roles', practitionerId);
        await updateDoc(practitionerRef, {
          status,
          updatedAt: now
        });

        commit('UPDATE_PRACTITIONER_STATUS', { practitionerId, status });
        return true;
      } catch (error) {
        console.error('Error updating practitioner status:', error);
        throw error;
      }
    },

    async updatePractitioner({ commit }, { practitionerId, data }) {
      try {
        const now = new Date().toISOString();
        const practitionerRef = doc(db, 'users_roles', practitionerId);

        await updateDoc(practitionerRef, {
          ...data,
          updatedAt: now
        });

        return true;
      } catch (error) {
        console.error('Error updating practitioner:', error);
        throw error;
      }
    },

    // Patient Management Actions
    async fetchPatients({ commit }) {
      commit('SET_LOADING', true);
      try {
        const usersRef = collection(db, 'users_roles');
        const q = query(
          usersRef,
          where('role', '==', 'patient'),
          orderBy('created_at', 'desc')
        );

        const snapshot = await getDocs(q);
        const patients = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          created_at: doc.data().created_at ? new Date(doc.data().created_at) : null,
          updatedAt: doc.data().updatedAt ? new Date(doc.data().updatedAt) : null,
          last_login: doc.data().last_login ? new Date(doc.data().last_login) : null
        }));

        commit('SET_PATIENTS', patients);
        return patients;
      } catch (error) {
        console.error('Error fetching patients:', error);
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    // Analytics Actions
    async fetchAnalytics({ commit }) {
      try {
        // Get total counts
        const [practitioners, patients, readings] = await Promise.all([
          this.dispatch('admin/fetchPractitioners'),
          this.dispatch('admin/fetchPatients'),
          this.dispatch('admin/fetchReadingsData')
        ]);

        // Calculate active users (logged in within last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const activePatients = patients.filter(p =>
          p.last_login && new Date(p.last_login) > thirtyDaysAgo
        ).length;

        const activePractitioners = practitioners.filter(p =>
          p.last_login && new Date(p.last_login) > thirtyDaysAgo
        ).length;

        const analytics = {
          totalPatients: patients.length,
          activePatients,
          totalPractitioners: practitioners.length,
          activePractitioners,
          readingsData: readings,
          lastUpdated: new Date()
        };

        commit('SET_ANALYTICS', analytics);
        return analytics;
      } catch (error) {
        console.error('Error fetching analytics:', error);
        throw error;
      }
    },

    async fetchReadingsData({ commit }) {
      try {
        // Get readings for the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const thirtyDaysAgoTimestamp = Timestamp.fromDate(thirtyDaysAgo);

        const readingsRef = collection(db, 'readings');
        const q = query(
          readingsRef,
          where('timestamp', '>=', thirtyDaysAgoTimestamp),
          orderBy('timestamp', 'desc')
        );

        const snapshot = await getDocs(q);
        const readings = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp.toDate()
        }));

        // Group readings by day
        const readingsByDay = {};
        const now = new Date();

        // Initialize last 30 days with 0 counts
        for (let i = 0; i < 30; i++) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          readingsByDay[dateStr] = 0;
        }

        // Fill in actual counts
        readings.forEach(reading => {
          const dateStr = reading.timestamp.toISOString().split('T')[0];
          if (readingsByDay[dateStr] !== undefined) {
            readingsByDay[dateStr]++;
          }
        });

        return {
          totalReadings: readings.length,
          readingsByDay
        };
      } catch (error) {
        console.error('Error fetching readings data:', error);
        return {
          totalReadings: 0,
          readingsByDay: {}
        };
      }
    },

    // System Health Actions
    async fetchSystemHealth({ commit }) {
      try {
        const healthRef = doc(db, 'system_health', 'current');
        const healthDoc = await getDoc(healthRef);

        let health;
        if (healthDoc.exists()) {
          health = healthDoc.data();
        } else {
          // If no health document exists, create a default one
          health = {
            status: 'healthy',
            lastUpdated: new Date().toISOString(),
            components: {
              database: { status: 'healthy' },
              authentication: { status: 'healthy' },
              storage: { status: 'healthy' },
              api: { status: 'healthy' }
            },
            metrics: {
              responseTime: 120, // ms
              uptime: 99.9, // percentage
              errorRate: 0.1 // percentage
            }
          };
        }

        commit('SET_SYSTEM_HEALTH', health);
        return health;
      } catch (error) {
        console.error('Error fetching system health:', error);
        throw error;
      }
    },

    // Settings Actions
    async fetchSettings() {
      try {
        const settingsRef = doc(db, 'admin_settings', 'general');
        const settingsDoc = await getDoc(settingsRef);

        if (settingsDoc.exists()) {
          return settingsDoc.data();
        }

        return null;
      } catch (error) {
        console.error('Error fetching admin settings:', error);
        throw error;
      }
    },

    async updateSettings(_, settings) {
      try {
        const now = new Date().toISOString();
        const settingsRef = doc(db, 'admin_settings', 'general');

        await updateDoc(settingsRef, {
          ...settings,
          updatedAt: now
        });

        return true;
      } catch (error) {
        console.error('Error updating admin settings:', error);
        throw error;
      }
    }
  },

  getters: {
    // User getters
    getUserById: (state) => (id) => {
      return state.users.find(user => user.id === id);
    },
    getPractitionerById: (state) => (id) => {
      return state.practitioners.find(practitioner => practitioner.id === id);
    },
    getPatientById: (state) => (id) => {
      return state.patients.find(patient => patient.id === id);
    },
    activePractitioners: (state) => {
      return state.practitioners.filter(p => p.status === 'active');
    },
    activePatients: (state) => {
      return state.patients.filter(p => p.status === 'active');
    },
    systemStatus: (state) => {
      return state.systemHealth?.status || 'unknown';
    },

    // Loading and error state getters
    isLoading: (state) => state.loading,
    error: (state) => state.error,

    // Pagination getters
    paginatedUsers: (state) => state.users, // For now, return all users without pagination
    totalPages: (state) => Math.ceil(state.users.length / 10) // Assuming 10 users per page
  }
};
