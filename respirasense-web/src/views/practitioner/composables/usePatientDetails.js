import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { useFormatters } from '@/composables/useFormatters';

/**
 * Composable for patient details functionality
 * @returns {Object} Patient details utility functions and state
 */
export function usePatientDetails() {
  const store = useStore();
  const router = useRouter();
  const route = useRoute();
  const { formatDate, formatDecimal } = useFormatters();

  // State
  const patient = ref(null);
  const readings = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const isDownloading = ref(false);

  /**
   * Fetch patient data
   */
  const fetchPatientData = async () => {
    try {
      loading.value = true;
      error.value = null;

      const patientId = route.params.id;
      console.log('PatientDetails: Fetching data for patient ID:', patientId);

      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Check if we already have the patient in the store
      const storedPatient = store.getters['practitioner/getSelectedPatient'];
      console.log('PatientDetails: Stored patient in Vuex:', storedPatient);

      if (storedPatient && storedPatient.id === patientId) {
        console.log('PatientDetails: Using stored patient data');
        patient.value = storedPatient;
      } else {
        console.log('PatientDetails: Fetching patient details from API');
        try {
          // Try the action from src/router/store first
          const patientData = await store.dispatch('practitioner/get_patient_details', patientId);
          console.log('PatientDetails: Received patient data from router/store:', patientData);
          patient.value = patientData;
        } catch (err) {
          console.error('Error with router/store action, trying src/store:', err);
          // Try the action from src/store as fallback
          const patientData = await store.dispatch('practitioner/getPatientDetails', patientId);
          console.log('PatientDetails: Received patient data from src/store:', patientData);
          patient.value = patientData;
        }
      }

      // Fetch patient readings
      console.log('PatientDetails: Fetching patient readings');
      try {
        // Try the action from src/router/store first
        const readingsData = await store.dispatch('practitioner/get_patient_readings', patientId);
        console.log('PatientDetails: Received readings data from router/store:', readingsData);
        readings.value = readingsData;
      } catch (err) {
        console.error('Error with router/store action, trying src/store:', err);
        // Try the action from src/store as fallback
        const readingsData = await store.dispatch('practitioner/getPatientReadings', patientId);
        console.log('PatientDetails: Received readings data from src/store:', readingsData);
        readings.value = readingsData;
      }

    } catch (err) {
      error.value = 'Failed to load patient data: ' + (err.message || 'Unknown error');
      console.error('Error loading patient data:', err);
    } finally {
      loading.value = false;
    }
  };

  /**
   * Navigate back to dashboard
   */
  const goBack = () => {
    router.push('/practitioner/dashboard');
  };

  /**
   * Navigate to detailed report
   */
  const viewDetailedReport = () => {
    if (patient.value && patient.value.id) {
      router.push(`/practitioner/patient/${patient.value.id}/detailed-report`);
    }
  };

  /**
   * Retry loading patient data
   */
  const retryLoading = async () => {
    error.value = null;
    await fetchPatientData();
  };

  /**
   * Download patient data as PDF
   */
  const downloadPatientData = async () => {
    try {
      isDownloading.value = true;

      // Import jsPDF dynamically to reduce initial load time
      const { default: jsPDF } = await import('jspdf');
      await import('jspdf-autotable');

      const doc = new jsPDF();

      // Add title
      doc.setFontSize(20);
      doc.text(`Patient Report: ${patient.value.name}`, 14, 22);

      // Add patient info
      doc.setFontSize(12);
      doc.text(`Email: ${patient.value.email || 'N/A'}`, 14, 32);
      doc.text(`Age: ${patient.value.age || 'N/A'}`, 14, 38);
      doc.text(`Height: ${patient.value.height || 'N/A'}`, 14, 44);
      doc.text(`Weight: ${patient.value.weight || 'N/A'}`, 14, 50);
      doc.text(`Gender: ${patient.value.gender || 'N/A'}`, 14, 56);
      doc.text(`Report Date: ${formatDate(new Date())}`, 14, 62);

      // Add readings table
      if (readings.value.length > 0) {
        doc.text('Health Readings', 14, 72);

        const tableColumn = ["Date", "Respiratory Rate", "Oxygen Saturation", "Heart Rate", "Temperature", "Risk Level"];
        const tableRows = [];

        readings.value.forEach(reading => {
          const readingDate = formatDate(reading.timestamp);
          const row = [
            readingDate,
            formatDecimal(reading.respiratoryRate) + ' bpm',
            formatDecimal(reading.oxygenSaturation) + '%',
            formatDecimal(reading.heartRate) + ' bpm',
            formatDecimal(reading.temperature) + '°C',
            reading.riskLevel?.toUpperCase() || 'N/A'
          ];
          tableRows.push(row);
        });

        doc.autoTable({
          head: [tableColumn],
          body: tableRows,
          startY: 75,
          theme: 'grid',
          styles: { fontSize: 8 },
          headStyles: { fillColor: [220, 50, 50] }
        });
      } else {
        doc.text('No health readings available', 14, 72);
      }

      // Save the PDF
      doc.save(`${patient.value.name.replace(/\s+/g, '_')}_report_${new Date().toISOString().split('T')[0]}.pdf`);

    } catch (err) {
      error.value = 'Failed to download patient data';
      console.error('Error downloading patient data:', err);
    } finally {
      isDownloading.value = false;
    }
  };

  /**
   * Get risk level class for styling
   * @param {string} riskLevel - The risk level
   * @returns {string} CSS class for the risk level
   */
  const getRiskLevelClass = (riskLevel) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high':
        return 'high-risk';
      case 'medium':
        return 'medium-risk';
      case 'low':
        return 'low-risk';
      case 'normal':
        return 'normal';
      default:
        return 'unknown';
    }
  };

  /**
   * Get risk level icon
   * @param {string} riskLevel - The risk level
   * @returns {string} FontAwesome icon class for the risk level
   */
  const getRiskLevelIcon = (riskLevel) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high':
        return 'fas fa-exclamation-triangle';
      case 'medium':
        return 'fas fa-exclamation-circle';
      case 'low':
        return 'fas fa-info-circle';
      case 'normal':
        return 'fas fa-check-circle';
      default:
        return 'fas fa-question-circle';
    }
  };

  // Initialize data
  onMounted(async () => {
    try {
      // First check if we have access to this patient
      const patientId = route.params.id;
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Try to get the list of patients first to validate access
      const patients = store.getters['practitioner/getPatients'] || [];

      // If we don't have any patients loaded, try to fetch them
      if (patients.length === 0) {
        try {
          console.log('No patients in store, fetching patients list');
          await store.dispatch('practitioner/fetch_patients');
        } catch (err) {
          console.error('Error fetching patients list:', err);
          // Continue anyway, we'll try to fetch the specific patient
        }
      }

      // Now check if we have access to this specific patient
      const updatedPatients = store.getters['practitioner/getPatients'] || [];
      const hasAccess = updatedPatients.some(p => p.id === patientId);

      if (!hasAccess) {
        console.log('Patient not in practitioner\'s list, but continuing anyway');
        // We'll continue anyway and let the Firestore rules handle access control
      }

      // Fetch the patient data
      await fetchPatientData();
    } catch (err) {
      error.value = 'Failed to validate patient access: ' + (err.message || 'Unknown error');
      console.error('Error in patient validation:', err);
    }
  });

  return {
    // State
    patient,
    readings,
    loading,
    error,
    isDownloading,

    // Methods
    goBack,
    retryLoading,
    viewDetailedReport,
    downloadPatientData,
    getRiskLevelClass,
    getRiskLevelIcon,
    formatDate,
    formatDecimal
  };
}
