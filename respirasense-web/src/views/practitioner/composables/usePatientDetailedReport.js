import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { format } from 'date-fns';
import { useFormatters } from '@/composables/useFormatters';
import { auth } from '@/plugins/firebase/firebase';
import { collection, query, where, orderBy, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import logoImg from '@/assets/LogoRespiraSense.png';

/**
 * Composable for practitioner detailed report functionality
 * @returns {Object} Detailed report utility functions and state
 */
export function usePatientDetailedReport() {
  const store = useStore();
  const router = useRouter();
  const route = useRoute();
  const { formatDecimal, formatDate, formatDateTime } = useFormatters();

  // State
  const loading = ref(false);
  const error = ref(null);
  const fromDate = ref('');
  const toDate = ref('');
  const patient = ref(null);
  const report = ref({
    readings: [],
    statistics: {
      averages: {},
      trends: {},
      anomalies: []
    }
  });

  // Filters
  const filters = ref({
    copdStatus: 'all',
    threshold: 'all'
  });

  /**
   * Filtered readings based on selected filters
   */
  const filteredReadings = computed(() => {
    if (!report.value.readings) return [];

    let filtered = [...report.value.readings];

    // Filter by COPD status
    if (filters.value.copdStatus !== 'all') {
      filtered = filtered.filter(reading => reading.riskLevel === filters.value.copdStatus);
    }

    return filtered;
  });

  /**
   * Fetch report data
   */
  const fetchReport = async () => {
    try {
      loading.value = true;
      error.value = null;

      const patientId = route.params.id;
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Get patient details if not already loaded
      if (!patient.value || patient.value.id !== patientId) {
        try {
          const patientData = await store.dispatch('practitioner/get_patient_details', patientId);
          patient.value = patientData;
        } catch (err) {
          console.error('Error fetching patient details:', err);
          // Try the other store action as fallback
          const patientData = await store.dispatch('practitioner/getPatientDetails', patientId);
          patient.value = patientData;
        }
      }

      // Convert dates to timestamps
      const fromTimestamp = fromDate.value ? new Date(fromDate.value) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const toTimestamp = toDate.value ? new Date(toDate.value) : new Date();

      // Ensure fromDate is at the start of the day and toDate is at the end of the day
      fromTimestamp.setHours(0, 0, 0, 0);
      toTimestamp.setHours(23, 59, 59, 999);

      // Fetch readings from Firestore (using readings collection, not health_readings)
      const readingsRef = collection(db, `patients/${patientId}/readings`);
      const q = query(
        readingsRef,
        where('timestamp', '>=', Timestamp.fromDate(fromTimestamp)),
        where('timestamp', '<=', Timestamp.fromDate(toTimestamp)),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const readings = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));

      // Calculate statistics
      const statistics = calculateStatistics(readings);

      // Update report
      report.value = {
        readings,
        statistics
      };

      return report.value;
    } catch (err) {
      console.error('Error fetching report:', err);
      error.value = 'Failed to load report data: ' + (err.message || 'Unknown error');
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Calculate statistics from readings
   * @param {Array} readings - The readings data
   * @returns {Object} Statistics object
   */
  const calculateStatistics = (readings) => {
    if (!readings || readings.length === 0) {
      return {
        averages: {},
        trends: {},
        anomalies: []
      };
    }

    // Calculate averages
    const metrics = ['respiratoryRate', 'oxygenSaturation', 'heartRate', 'temperature'];
    const sums = {};
    const counts = {};

    metrics.forEach(metric => {
      sums[metric] = 0;
      counts[metric] = 0;
    });

    readings.forEach(reading => {
      metrics.forEach(metric => {
        if (reading[metric] !== undefined && reading[metric] !== null) {
          sums[metric] += reading[metric];
          counts[metric]++;
        }
      });
    });

    const averages = {};
    metrics.forEach(metric => {
      averages[metric] = counts[metric] > 0 ? sums[metric] / counts[metric] : 0;
    });

    // Calculate trends (simple implementation)
    const trends = {};
    metrics.forEach(metric => {
      if (readings.length >= 2) {
        const firstValue = readings[readings.length - 1][metric];
        const lastValue = readings[0][metric];
        const change = lastValue - firstValue;
        const percentChange = firstValue !== 0 ? (change / firstValue) * 100 : 0;

        let description = '';
        if (Math.abs(percentChange) < 5) {
          description = `${formatMetricName(metric)} has remained stable.`;
        } else if (percentChange > 0) {
          description = `${formatMetricName(metric)} has increased by ${Math.abs(percentChange).toFixed(1)}%.`;
        } else {
          description = `${formatMetricName(metric)} has decreased by ${Math.abs(percentChange).toFixed(1)}%.`;
        }

        trends[metric] = {
          change,
          percentChange,
          description
        };
      } else {
        trends[metric] = {
          change: 0,
          percentChange: 0,
          description: `Not enough data to determine ${formatMetricName(metric)} trend.`
        };
      }
    });

    // Identify anomalies (simple implementation)
    const anomalies = [];
    readings.forEach(reading => {
      if (reading.respiratoryRate > 25 || reading.respiratoryRate < 10) {
        anomalies.push({
          date: reading.timestamp,
          description: `Abnormal respiratory rate: ${reading.respiratoryRate} bpm`
        });
      }

      if (reading.oxygenSaturation < 92) {
        anomalies.push({
          date: reading.timestamp,
          description: `Low oxygen saturation: ${reading.oxygenSaturation}%`
        });
      }

      if (reading.heartRate > 100 || reading.heartRate < 50) {
        anomalies.push({
          date: reading.timestamp,
          description: `Abnormal heart rate: ${reading.heartRate} bpm`
        });
      }

      if (reading.temperature > 38 || reading.temperature < 36) {
        anomalies.push({
          date: reading.timestamp,
          description: `Abnormal temperature: ${reading.temperature}°C`
        });
      }
    });

    return {
      averages,
      trends,
      anomalies
    };
  };

  /**
   * Format metric name for display
   * @param {string} metric - The metric name
   * @returns {string} Formatted metric name
   */
  const formatMetricName = (metric) => {
    switch (metric) {
      case 'respiratoryRate': return 'Respiratory Rate';
      case 'oxygenSaturation': return 'Oxygen Saturation';
      case 'heartRate': return 'Heart Rate';
      case 'temperature': return 'Temperature';
      default: return metric;
    }
  };

  /**
   * Format metric value for display
   * @param {string} metric - The metric name
   * @param {number} value - The metric value
   * @returns {string} Formatted metric value
   */
  const formatMetricValue = (metric, value) => {
    if (value === undefined || value === null) return 'N/A';

    switch (metric) {
      case 'respiratoryRate': return `${formatDecimal(value)} bpm`;
      case 'oxygenSaturation': return `${formatDecimal(value)}%`;
      case 'heartRate': return `${formatDecimal(value)} bpm`;
      case 'temperature': return `${formatDecimal(value)}°C`;
      default: return formatDecimal(value);
    }
  };

  /**
   * Format risk level for display
   * @param {string} riskLevel - The risk level
   * @returns {string} Formatted risk level
   */
  const formatRiskLevel = (riskLevel) => {
    if (!riskLevel) return 'Unknown';
    return riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1);
  };

  /**
   * Apply filters to readings
   */
  const applyFilters = () => {
    // This function is called when filters are changed
    // The filteredReadings computed property will handle the filtering
  };

  /**
   * Get chart data for a specific metric
   * @param {string} metric - The metric name
   * @returns {Array} Chart data
   */
  const getChartData = (metric) => {
    try {
      if (!report.value.readings || report.value.readings.length === 0) {
        return [];
      }

      // Sort readings by timestamp (oldest first)
      const sortedReadings = [...report.value.readings].sort((a, b) => a.timestamp - b.timestamp);

      // Extract data points
      const data = sortedReadings.map(reading => ({
        x: reading.timestamp,
        y: reading[metric]
      })).filter(point => point.y !== undefined && point.y !== null);

      return [{
        label: formatMetricName(metric),
        data
      }];
    } catch (error) {
      console.error(`Error generating chart data for ${metric}:`, error);
      return [];
    }
  };

  /**
   * Get risk distribution data for pie chart
   * @returns {Array} Risk distribution data
   */
  const getRiskDistribution = () => {
    try {
      // Count readings by risk level
      const riskCounts = {
        normal: 0,
        warning: 0,
        danger: 0
      };

      // Count readings by risk level
      report.value.readings.forEach(reading => {
        const riskLevel = reading.riskLevel || 'normal';
        if (riskCounts[riskLevel] !== undefined) {
          riskCounts[riskLevel]++;
        } else {
          riskCounts[riskLevel] = 1;
        }
      });

      // Convert to array format for pie chart
      const data = [
        { label: 'Normal', value: riskCounts.normal },
        { label: 'Warning', value: riskCounts.warning },
        { label: 'Danger', value: riskCounts.danger }
      ].filter(item => item.value > 0); // Remove empty categories

      return data;
    } catch (error) {
      console.error('Error generating risk distribution data:', error);
      return [];
    }
  };

  /**
   * Get CSS class for a metric value
   * @param {string} metric - The metric name
   * @param {number} value - The metric value
   * @returns {string} CSS class
   */
  const getMetricClass = (metric, value) => {
    if (value === undefined || value === null) return '';

    switch (metric) {
      case 'respiratoryRate':
        return value > 25 || value < 10 ? 'danger' : value > 20 || value < 12 ? 'warning' : 'normal';
      case 'oxygenSaturation':
        return value < 92 ? 'danger' : value < 95 ? 'warning' : 'normal';
      case 'heartRate':
        return value > 100 || value < 50 ? 'danger' : value > 90 || value < 60 ? 'warning' : 'normal';
      case 'temperature':
        return value > 38 || value < 36 ? 'danger' : value > 37.5 || value < 36.5 ? 'warning' : 'normal';
      default:
        return '';
    }
  };

  /**
   * Generate and download PDF report
   */
  const downloadPdf = async () => {
    // Create PDF document
    const doc = new jsPDF();

    // ===== PAGE 1: HEADER, SUMMARY, CHARTS =====
    let yPos = 10;

    // Add RespiraSense logo (left-aligned)
    try {
      doc.addImage(logoImg, 'PNG', 14, yPos, 40, 20);
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
    }

    // Add report title (right-aligned)
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('Patient Health Report', 195, yPos + 10, { align: 'right' });

    yPos += 25;

    // Add patient information
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Patient Information', 14, yPos);

    yPos += 8;

    doc.setFont('helvetica', 'normal');
    doc.text(`Name: ${patient.value?.name || 'Unknown'}`, 14, yPos);

    yPos += 6;

    doc.text(`Email: ${patient.value?.email || 'Unknown'}`, 14, yPos);

    yPos += 6;

    if (patient.value?.age) {
      doc.text(`Age: ${patient.value.age}`, 14, yPos);
      yPos += 6;
    }

    if (patient.value?.gender) {
      doc.text(`Gender: ${patient.value.gender}`, 14, yPos);
      yPos += 6;
    }

    doc.text(`Report Period: ${formatDate(new Date(fromDate.value))} to ${formatDate(new Date(toDate.value))}`, 14, yPos);

    yPos += 6;

    doc.text(`Generated by: ${auth.currentUser?.displayName || 'Practitioner'}`, 14, yPos);

    yPos += 6;

    doc.text(`Date Generated: ${formatDate(new Date())}`, 14, yPos);

    yPos += 15;

    // Add summary statistics
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Summary Statistics', 14, yPos);

    yPos += 10;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');

    const metrics = ['respiratoryRate', 'oxygenSaturation', 'heartRate', 'temperature'];
    metrics.forEach(metric => {
      const average = report.value.statistics.averages[metric];
      if (average !== undefined) {
        doc.text(`Average ${formatMetricName(metric)}: ${formatMetricValue(metric, average)}`, 14, yPos);
        yPos += 6;
      }
    });

    yPos += 10;

    // Add readings table
    if (report.value.readings && report.value.readings.length > 0) {
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Health Readings', 14, yPos);

      yPos += 10;

      const tableColumn = ["Date", "Respiratory Rate", "Oxygen Saturation", "Heart Rate", "Temperature", "Risk Level"];
      const tableRows = [];

      report.value.readings.forEach(reading => {
        const readingDate = formatDate(reading.timestamp);
        const row = [
          readingDate,
          formatDecimal(reading.respiratoryRate) + ' bpm',
          formatDecimal(reading.oxygenSaturation) + '%',
          formatDecimal(reading.heartRate) + ' bpm',
          formatDecimal(reading.temperature) + '°C',
          reading.riskLevel?.toUpperCase() || 'N/A'
        ];
        tableRows.push(row);
      });

      doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: yPos,
        theme: 'grid',
        styles: { fontSize: 8 },
        headStyles: { fillColor: [220, 50, 50] }
      });
    }

    // Add footer with RespiraSense branding on all pages
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text('RespiraSense Health Report - Confidential Medical Information', 105, doc.internal.pageSize.height - 10, { align: 'center' });
      doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 5, { align: 'center' });
    }

    // Save the PDF
    const fileName = patient.value?.name
      ? `${patient.value.name.replace(/\s+/g, '_')}_health_report_${format(new Date(), 'yyyy-MM-dd')}.pdf`
      : `patient_health_report_${format(new Date(), 'yyyy-MM-dd')}.pdf`;

    doc.save(fileName);
  };

  /**
   * Navigate back to the patient details page
   */
  const goBack = () => {
    router.push(`/practitioner/patient/${route.params.id}`);
  };

  // Initialize data
  onMounted(() => {
    // Set default date range to last 30 days
    const today = new Date();
    toDate.value = format(today, 'yyyy-MM-dd');
    fromDate.value = format(new Date(today.setMonth(today.getMonth() - 1)), 'yyyy-MM-dd');
    fetchReport();
  });

  return {
    loading,
    error,
    fromDate,
    toDate,
    patient,
    report,
    filters,
    filteredReadings,
    fetchReport,
    formatMetricName,
    formatMetricValue,
    formatRiskLevel,
    formatDate,
    formatDateTime,
    formatDecimal,
    applyFilters,
    getChartData,
    getRiskDistribution,
    getMetricClass,
    downloadPdf,
    goBack
  };
}
