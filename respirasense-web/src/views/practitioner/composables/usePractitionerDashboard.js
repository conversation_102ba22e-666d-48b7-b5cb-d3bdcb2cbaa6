import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useFormatters } from '@/composables/useFormatters';

/**
 * Composable for practitioner dashboard functionality
 * @returns {Object} Dashboard utility functions and state
 */
export function usePractitionerDashboard() {
  const store = useStore();
  const router = useRouter();
  const { formatDate, formatDecimal } = useFormatters();

  // State
  const patients = ref([]);
  const recentActivity = ref([]);
  const pendingRequests = ref([]);
  const patientLinkingRequests = ref([]);
  const patientTrendData = ref({});
  const loading = ref(false);
  const error = ref(null);

  /**
   * Fetch dashboard data
   */
  const fetchDashboardData = async () => {
    try {
      loading.value = true;
      error.value = null;

      // Fetch patients assigned to this practitioner
      const patientsData = await store.dispatch('practitioner/fetch_patients');
      patients.value = patientsData;

      // Fetch recent activity
      const activityData = await store.dispatch('practitioner/fetch_recent_activity');
      recentActivity.value = activityData;

      // Fetch pending requests
      const requestsData = await store.dispatch('practitioner/fetch_pending_requests');
      pendingRequests.value = requestsData;

      // Fetch patient linking requests
      const linkingRequestsData = await store.dispatch('practitioner/fetch_patient_linking_requests');
      patientLinkingRequests.value = linkingRequestsData;

      // Fetch trend data for each patient
      if (patientsData && patientsData.length > 0) {
        await Promise.all(patientsData.map(async (patient) => {
          try {
            // In a real implementation, you would fetch actual trend data from your API
            // For now, we'll just create some dummy data
            patientTrendData.value[patient.id] = generateDummyTrendData();
          } catch (error) {
            console.error(`Error fetching trend data for patient ${patient.id}:`, error);
          }
        }));
      }

    } catch (err) {
      error.value = 'Failed to load dashboard data';
      console.error('Error loading dashboard data:', err);
    } finally {
      loading.value = false;
    }
  };

  /**
   * Generate dummy trend data for demo purposes
   * @returns {Array} Array of data points
   */
  const generateDummyTrendData = () => {
    const data = [];
    for (let i = 0; i < 7; i++) {
      data.push({
        value: Math.floor(Math.random() * 30) + 60 // Random value between 60 and 90
      });
    }
    return data;
  };

  /**
   * View patient details
   * @param {string} patientId - The ID of the patient to view
   */
  const viewPatientDetails = async (patientId) => {
    try {
      // First, get the patient details to store in Vuex
      const patient = patients.value.find(p => p.id === patientId);

      if (patient) {
        // Store the patient in Vuex before navigating
        store.commit('practitioner/SET_SELECTED_PATIENT', patient);
        router.push(`/practitioner/patient/${patientId}`);
      } else {
        // Try to fetch the patient details first
        try {
          // Try the action from src/router/store first
          const patientData = await store.dispatch('practitioner/get_patient_details', patientId);
          if (patientData) {
            router.push(`/practitioner/patient/${patientId}`);
          } else {
            throw new Error('Patient not found');
          }
        } catch (err) {
          console.error('Error with router/store action, trying src/store:', err);
          // Try the action from src/store as fallback
          const patientData = await store.dispatch('practitioner/getPatientDetails', patientId);
          if (patientData) {
            router.push(`/practitioner/patient/${patientId}`);
          } else {
            console.error('Patient not found');
            error.value = 'Patient not found';
          }
        }
      }
    } catch (err) {
      console.error('Error navigating to patient details:', err);
      error.value = 'Failed to load patient details';
    }
  };

  /**
   * Approve a patient request
   * @param {string} requestId - The ID of the request to approve
   */
  const approveRequest = async (requestId) => {
    try {
      await store.dispatch('practitioner/approve_request', requestId);
      // Refresh data after approval
      await fetchDashboardData();
    } catch (err) {
      error.value = 'Failed to approve request';
      console.error('Error approving request:', err);
    }
  };

  /**
   * Reject a patient request
   * @param {string} requestId - The ID of the request to reject
   */
  const rejectRequest = async (requestId) => {
    try {
      await store.dispatch('practitioner/reject_request', requestId);
      // Refresh data after rejection
      await fetchDashboardData();
    } catch (err) {
      error.value = 'Failed to reject request';
      console.error('Error rejecting request:', err);
    }
  };

  /**
   * Get risk level class for styling
   * @param {string} riskLevel - The risk level
   * @returns {string} CSS class for the risk level
   */
  const getRiskLevelClass = (riskLevel) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high':
        return 'high-risk';
      case 'medium':
        return 'medium-risk';
      case 'low':
        return 'low-risk';
      case 'normal':
        return 'normal';
      default:
        return 'unknown';
    }
  };

  /**
   * Get risk level icon
   * @param {string} riskLevel - The risk level
   * @returns {string} FontAwesome icon class for the risk level
   */
  const getRiskLevelIcon = (riskLevel) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high':
        return 'fas fa-exclamation-triangle';
      case 'medium':
        return 'fas fa-exclamation-circle';
      case 'low':
        return 'fas fa-info-circle';
      case 'normal':
        return 'fas fa-check-circle';
      default:
        return 'fas fa-question-circle';
    }
  };

  // Computed properties
  const activePatientCount = computed(() => {
    // Count all patients regardless of status since they are linked
    return patients.value ? patients.value.length : 0;
  });

  const pendingRequestsCount = computed(() => {
    // Combine both regular pending requests and patient linking requests
    const regularRequests = pendingRequests.value ? pendingRequests.value.length : 0;
    const linkingRequests = patientLinkingRequests.value ?
      patientLinkingRequests.value.filter(r => r.status === 'pending').length : 0;
    return regularRequests + linkingRequests;
  });

  // Initialize data
  onMounted(async () => {
    await fetchDashboardData();
  });

  return {
    // State
    patients,
    recentActivity,
    pendingRequests,
    patientLinkingRequests,
    patientTrendData,
    loading,
    error,

    // Computed
    activePatientCount,
    pendingRequestsCount,

    // Methods
    viewPatientDetails,
    approveRequest,
    rejectRequest,
    getRiskLevelClass,
    getRiskLevelIcon,
    formatDate,
    formatDecimal,
    generateDummyTrendData
  };
}
