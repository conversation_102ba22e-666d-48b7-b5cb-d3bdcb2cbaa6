<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="detailed-report">
        <div class="back-navigation">
          <button @click="goBack" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Patient Details
          </button>
        </div>

        <div class="report-header">
          <h1>{{ patient?.name || 'Patient' }} - Detailed Health Report</h1>
          <button @click="downloadPdf" class="download-btn" :disabled="loading || !report?.statistics">
            <i class="fas fa-download"></i> Download PDF
          </button>
        </div>

        <div class="filters-section">
          <div class="date-range-picker">
            <input type="date" v-model="fromDate" @change="fetchReport">
            <span>to</span>
            <input type="date" v-model="toDate" @change="fetchReport">
          </div>

          <div class="filter-controls">
            <div class="filter-group">
              <label>COPD Risk Level:</label>
              <select v-model="filters.copdStatus" @change="applyFilters">
                <option value="all">All</option>
                <option value="normal">Normal</option>
                <option value="warning">Warning</option>
                <option value="danger">Danger</option>
              </select>
            </div>
          </div>
        </div>

        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <span>Loading report...</span>
        </div>

        <div v-else-if="error" class="error">
          {{ error }}
        </div>

        <div v-else-if="report?.statistics" class="report-content">
          <!-- Statistics Summary -->
          <div class="statistics-summary">
            <h2>Summary Statistics</h2>
            <div class="stats-grid">
              <div v-for="(value, metric) in report.statistics.averages"
                  :key="metric"
                  class="stat-card">
                <h3>Average {{ formatMetricName(metric) }}</h3>
                <p>{{ formatMetricValue(metric, value) }}</p>
              </div>
            </div>
          </div>

          <!-- Charts Section -->
          <div class="charts-section">
            <h2>Health Metrics Trends</h2>

            <div class="chart-container">
              <ChartJSLineChart
                :key="'rr-chart-' + fromDate + '-' + toDate"
                :series="getChartData('respiratoryRate')"
                :loading="loading"
                title="Respiratory Rate"
                yAxisLabel="Breaths per minute"
                metricType="respiratoryRate"
                :thresholds="{ upper: 20, lower: 12 }"
              />
            </div>

            <div class="chart-container">
              <ChartJSLineChart
                :key="'o2-chart-' + fromDate + '-' + toDate"
                :series="getChartData('oxygenSaturation')"
                :loading="loading"
                title="Oxygen Saturation"
                yAxisLabel="SpO₂ (%)"
                metricType="oxygenSaturation"
                :thresholds="{ lower: 95 }"
              />
            </div>

            <div class="chart-container">
              <ChartJSLineChart
                :key="'hr-chart-' + fromDate + '-' + toDate"
                :series="getChartData('heartRate')"
                :loading="loading"
                title="Heart Rate"
                yAxisLabel="Beats per minute"
                metricType="heartRate"
                :thresholds="{ upper: 100, lower: 60 }"
              />
            </div>

            <div class="chart-container">
              <ChartJSLineChart
                :key="'temp-chart-' + fromDate + '-' + toDate"
                :series="getChartData('temperature')"
                :loading="loading"
                title="Temperature"
                yAxisLabel="Temperature (°C)"
                metricType="temperature"
                :thresholds="{ upper: 37.5 }"
              />
            </div>

            <!-- COPD Risk Classification Breakdown -->
            <div class="chart-container">
              <ChartJSPieChart
                :key="'risk-chart-' + fromDate + '-' + toDate"
                :data="getRiskDistribution()"
                :loading="loading"
                title="Risk Level Distribution"
              />
            </div>
          </div>

          <!-- Trends -->
          <div class="trends-section">
            <h2>Trends Analysis</h2>
            <div v-for="(trend, metric) in report.statistics.trends"
                :key="metric"
                class="trend-item">
              <h3>{{ formatMetricName(metric) }}</h3>
              <p>{{ trend.description }}</p>
            </div>
          </div>

          <!-- Anomalies -->
          <div class="anomalies-section">
            <h2>Notable Events</h2>
            <div v-if="report.statistics.anomalies.length === 0" class="empty-state">
              <p>No notable events detected in this time period.</p>
            </div>
            <div v-else v-for="(anomaly, index) in report.statistics.anomalies"
                :key="index"
                class="anomaly-item">
              <p>{{ formatDate(anomaly.date) }}: {{ anomaly.description }}</p>
            </div>
          </div>

          <!-- Readings Table -->
          <div class="readings-table-section">
            <h2>Readings Data</h2>
            <div v-if="filteredReadings.length === 0" class="empty-state">
              <p>No readings available for the selected filters.</p>
            </div>
            <table v-else class="readings-table">
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Respiratory Rate</th>
                  <th>Oxygen Saturation</th>
                  <th>Heart Rate</th>
                  <th>Temperature</th>
                  <th>COPD Risk</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="reading in filteredReadings" :key="reading.id" :class="reading.riskLevel">
                  <td>{{ formatDateTime(reading.timestamp) }}</td>
                  <td>{{ formatDecimal(reading.respiratoryRate) }} bpm</td>
                  <td>{{ formatDecimal(reading.oxygenSaturation) }}%</td>
                  <td>{{ formatDecimal(reading.heartRate) }} bpm</td>
                  <td>{{ formatDecimal(reading.temperature) }}°C</td>
                  <td class="risk-cell">{{ reading.riskLevel.toUpperCase() }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div v-else class="empty-state">
          <i class="fas fa-chart-line"></i>
          <p>No health data available for this patient.</p>
          <p class="empty-state-subtitle">Try selecting a different date range or check back later.</p>
        </div>
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import ChartJSLineChart from '@/components/charts/ChartJSLineChart.vue';
import ChartJSPieChart from '@/components/charts/ChartJSPieChart.vue';
import { usePatientDetailedReport } from './composables/usePatientDetailedReport';

export default {
  name: 'PatientDetailedReport',

  components: {
    AppHeader,
    AppFooter,
    ChartJSLineChart,
    ChartJSPieChart
  },

  setup() {
    return usePatientDetailedReport();
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.detailed-report {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .back-navigation {
    margin-bottom: 20px;

    .back-btn {
      display: flex;
      align-items: center;
      background: none;
      border: none;
      color: $primary;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      padding: 0;

      i {
        margin-right: 8px;
      }

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .download-btn {
      display: flex;
      align-items: center;
      background-color: $primary;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 10px 15px;
      font-size: 14px;
      cursor: pointer;

      i {
        margin-right: 8px;
      }

      &:hover {
        background-color: darken($primary, 10%);
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }

  .filters-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;

    .date-range-picker {
      display: flex;
      align-items: center;
      gap: 10px;

      input[type="date"] {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
    }

    .filter-controls {
      display: flex;
      gap: 15px;
      margin-top: 10px;

      @media (min-width: 768px) {
        margin-top: 0;
      }

      .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-size: 14px;
          font-weight: 500;
        }

        select {
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: white;
        }
      }
    }
  }

  .loading, .error, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    text-align: center;
  }

  .loading {
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary, 0.1);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }
  }

  .error {
    color: #dc3545;
  }

  .empty-state {
    color: #6c757d;

    i {
      font-size: 48px;
      margin-bottom: 15px;
      color: #ddd;
    }

    .empty-state-subtitle {
      font-size: 14px;
      margin-top: 5px;
    }
  }

  .report-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .statistics-summary, .charts-section, .trends-section, .anomalies-section, .readings-table-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: $dark-grey;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;

    .stat-card {
      background-color: #f8f9fa;
      border-radius: 6px;
      padding: 15px;
      text-align: center;

      h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
        font-weight: 500;
        color: $dark-grey;
      }

      p {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: $primary;
      }
    }
  }

  .chart-container {
    margin-bottom: 30px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .trend-item, .anomaly-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    h3 {
      margin: 0 0 5px 0;
      font-size: 16px;
      font-weight: 500;
      color: $dark-grey;
    }

    p {
      margin: 0;
      color: #666;
    }
  }

  .readings-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      font-weight: 600;
      background-color: #f8f9fa;
    }

    tr {
      &.normal {
        background-color: rgba(40, 167, 69, 0.05);
      }

      &.warning {
        background-color: rgba(255, 193, 7, 0.05);
      }

      &.danger {
        background-color: rgba(220, 53, 69, 0.05);
      }
    }

    .risk-cell {
      font-weight: 600;

      &.normal {
        color: #28a745;
      }

      &.warning {
        color: #ffc107;
      }

      &.danger {
        color: #dc3545;
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
