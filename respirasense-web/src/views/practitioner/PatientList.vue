<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="patient-list-view">
        <!-- Header -->
        <div class="view-header">
          <div class="header-left">
            <button @click="goBack" class="btn-back">
              <i class="fas fa-arrow-left"></i> Back to Dashboard
            </button>
          </div>
          <div class="section-title">
            <i class="fas fa-users"></i>
            <h1>My Patients</h1>
          </div>
          <div class="header-actions">
            <div class="search-container">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                v-model="searchTerm"
                placeholder="Search patients..."
                class="search-input"
                @input="handleSearch"
              >
              <button v-if="searchTerm" @click="clearSearch" class="clear-search-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="view-toggle">
              <button
                @click="viewMode = 'grid'"
                :class="['view-toggle-btn', { active: viewMode === 'grid' }]"
                title="Grid View"
              >
                <i class="fas fa-th"></i>
              </button>
              <button
                @click="viewMode = 'list'"
                :class="['view-toggle-btn', { active: viewMode === 'list' }]"
                title="List View"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Search Results Info -->
        <div v-if="searchTerm && !loading" class="search-results-info">
          <p v-if="filteredPatients.length === 0">
            No patients found matching "<strong>{{ searchTerm }}</strong>"
          </p>
          <p v-else>
            Found {{ filteredPatients.length }} patient{{ filteredPatients.length !== 1 ? 's' : '' }} matching "<strong>{{ searchTerm }}</strong>"
          </p>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <div class="spinner"></div>
          <p>Loading patients...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ error }}</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="!patients || patients.length === 0" class="empty-state">
          <i class="fas fa-user-plus"></i>
          <p>No patients assigned yet</p>
          <p class="empty-state-subtitle">Use the Link with Patient form on the dashboard to connect with patients</p>
        </div>

        <!-- Patient Grid View -->
        <div v-else-if="viewMode === 'grid'" class="patients-grid">
          <div v-for="patient in filteredPatients" :key="patient.id" class="patient-tile">
            <UserAvatar
              :userId="patient.id"
              :photoURL="patient.photoURL"
              :name="patient.name"
              :status="patient.status || 'active'"
              size="large"
              :showStatus="true"
              :clickable="true"
              @click="viewPatientDetails(patient)"
            />
            <div class="patient-info">
              <h3 class="patient-name">{{ patient.name || 'Unknown Patient' }}</h3>
              <p v-if="patient.email" class="patient-email">{{ patient.email }}</p>
              <p v-if="patient.lastUpdate" class="last-update">
                <i class="fas fa-clock"></i> {{ formatDate(patient.lastUpdate) }}
              </p>
            </div>
            <div class="patient-actions">
              <button @click="viewPatientDetails(patient)" class="action-btn view-btn" title="View Details">
                <i class="fas fa-eye"></i>
              </button>
              <button @click="confirmUnlink(patient)" class="action-btn unlink-btn" title="Unlink Patient">
                <i class="fas fa-unlink"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Patient List View -->
        <div v-else class="patients-list">
          <table>
            <thead>
              <tr>
                <th>Patient</th>
                <th>Email</th>
                <th>Status</th>
                <th>Last Update</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="patient in filteredPatients" :key="patient.id" class="patient-row">
                <td class="patient-name-cell">
                  <UserAvatar
                    :userId="patient.id"
                    :photoURL="patient.photoURL"
                    :status="patient.status || 'active'"
                    size="small"
                    :showStatus="true"
                  />
                  <span>{{ patient.name || 'Unknown Patient' }}</span>
                </td>
                <td>{{ patient.email || 'No email' }}</td>
                <td>
                  <span class="status-badge" :class="patient.status || 'active'">
                    {{ formatStatus(patient.status || 'active') }}
                  </span>
                </td>
                <td>{{ patient.lastUpdate ? formatDate(patient.lastUpdate) : 'Never' }}</td>
                <td class="actions-cell">
                  <button @click="viewPatientDetails(patient)" class="action-btn view-btn" title="View Details">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button @click="confirmUnlink(patient)" class="action-btn unlink-btn" title="Unlink Patient">
                    <i class="fas fa-unlink"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Confirmation Modal -->
        <div v-if="showConfirmation" class="confirmation-modal">
          <div class="modal-content">
            <div class="modal-header">
              <h4>Confirm Unlink</h4>
              <button @click="cancelUnlink" class="close-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="modal-body">
              <p>Are you sure you want to unlink from <strong>{{ selectedPatient?.name || 'this patient' }}</strong>?</p>
              <p class="warning">This action cannot be undone. The patient will need to approve a new request to link again.</p>
            </div>
            <div class="modal-footer">
              <button @click="cancelUnlink" class="cancel-btn">
                <i class="fas fa-times"></i> Cancel
              </button>
              <button @click="unlinkPatient" class="confirm-btn">
                <i class="fas fa-unlink"></i> Unlink
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format, formatDistanceToNow } from 'date-fns';
import { removePatientFromPractitioner } from '@/utils/firestore-helpers';
import { auth } from '@/plugins/firebase/firebase';
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import UserAvatar from '@/components/common/UserAvatar.vue';

export default {
  name: 'PatientList',

  components: {
    AppHeader,
    AppFooter,
    UserAvatar
  },

  setup() {
    const store = useStore();
    const router = useRouter();
    const loading = ref(false);
    const error = ref(null);
    const showConfirmation = ref(false);
    const selectedPatient = ref(null);
    const searchTerm = ref('');
    const viewMode = ref('grid'); // 'grid' or 'list'
    const defaultUserIcon = require('@/assets/DefaultUserIcon.webp');

    // Get patients from store
    const patients = computed(() => {
      return store.getters['practitioner/getPatients'] || [];
    });

    // Filter patients based on search term
    const filteredPatients = computed(() => {
      if (!searchTerm.value.trim()) {
        return patients.value;
      }

      const term = searchTerm.value.toLowerCase().trim();

      return patients.value.filter(patient => {
        // Search in name
        if (patient.name && patient.name.toLowerCase().includes(term)) {
          return true;
        }

        // Search in email
        if (patient.email && patient.email.toLowerCase().includes(term)) {
          return true;
        }

        // Search in status
        if (patient.status && patient.status.toLowerCase().includes(term)) {
          return true;
        }

        // Search in ID (for advanced users)
        if (patient.id && patient.id.toLowerCase().includes(term)) {
          return true;
        }

        return false;
      });
    });

    // Handle search input with debounce
    let searchTimeout;
    const handleSearch = () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        // This is where you could add analytics or other search-related logic
        console.log('Searching for:', searchTerm.value);
      }, 300);
    };

    // Clear search
    const clearSearch = () => {
      searchTerm.value = '';
    };

    // Handle image loading errors
    const handleImageError = (event) => {
      event.target.src = defaultUserIcon;
    };

    // Format date
    const formatDate = (date) => {
      if (!date) return 'N/A';

      const dateObj = new Date(date);

      // If date is within the last 24 hours, show relative time
      const now = new Date();
      const diffMs = now - dateObj;
      const diffHours = diffMs / (1000 * 60 * 60);

      if (diffHours < 24) {
        return formatDistanceToNow(dateObj, { addSuffix: true });
      }

      return format(dateObj, 'MMM d, yyyy h:mm a');
    };

    // Format status
    const formatStatus = (status) => {
      switch (status.toLowerCase()) {
        case 'active': return 'Active';
        case 'pending': return 'Pending';
        case 'inactive': return 'Inactive';
        default: return status;
      }
    };

    // View patient details
    const viewPatientDetails = (patient) => {
      // Store the patient in Vuex before navigating
      store.commit('practitioner/SET_SELECTED_PATIENT', patient);
      router.push(`/practitioner/patient/${patient.id}`);
    };

    // Go back to dashboard
    const goBack = () => {
      router.push('/practitioner/dashboard');
    };

    // Confirm unlink
    const confirmUnlink = (patient) => {
      selectedPatient.value = patient;
      showConfirmation.value = true;
    };

    // Cancel unlink
    const cancelUnlink = () => {
      selectedPatient.value = null;
      showConfirmation.value = false;
    };

    // Unlink patient
    const unlinkPatient = async () => {
      if (!selectedPatient.value) return;

      try {
        loading.value = true;
        const user = auth.currentUser;

        if (!user) {
          throw new Error('No authenticated user found');
        }

        await removePatientFromPractitioner(selectedPatient.value.id, user.uid);

        // Refresh patient list
        await store.dispatch('practitioner/fetch_patients');

        // Close modal
        showConfirmation.value = false;
        selectedPatient.value = null;
      } catch (err) {
        console.error('Error unlinking patient:', err);
        error.value = 'Failed to unlink patient';
      } finally {
        loading.value = false;
      }
    };

    // Save view mode preference to localStorage
    watch(viewMode, (newMode) => {
      localStorage.setItem('patientListViewMode', newMode);
    });

    // Fetch patients on mount
    onMounted(async () => {
      try {
        loading.value = true;

        // Load view mode preference from localStorage
        const savedViewMode = localStorage.getItem('patientListViewMode');
        if (savedViewMode) {
          viewMode.value = savedViewMode;
        }

        await store.dispatch('practitioner/fetch_patients');
      } catch (err) {
        console.error('Error loading patients:', err);
        error.value = 'Failed to load patients';
      } finally {
        loading.value = false;
      }
    });

    return {
      patients,
      filteredPatients,
      loading,
      error,
      showConfirmation,
      selectedPatient,
      searchTerm,
      viewMode,
      formatDate,
      formatStatus,
      viewPatientDetails,
      goBack,
      confirmUnlink,
      cancelUnlink,
      unlinkPatient,
      handleSearch,
      clearSearch,
      handleImageError
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.patient-list-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 15px;

    .header-left {
      order: 1;

      .btn-back {
        padding: 8px 16px;
        background-color: transparent;
        border: 1px solid $primary;
        color: $primary;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba($primary, 0.1);
        }
      }
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      order: 2;
      flex-grow: 1;
      justify-content: center;

      i {
        font-size: 24px;
        color: $primary;
      }

      h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        color: $dark-grey;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
      order: 3;

      .search-container {
        position: relative;
        width: 250px;

        .search-icon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: lighten($dark-grey, 30%);
          font-size: 0.9rem;
        }

        .search-input {
          width: 100%;
          padding: 8px 35px 8px 35px;
          border: 1px solid lighten($dark-grey, 40%);
          border-radius: 20px;
          font-size: 0.9rem;
          transition: all 0.2s ease;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 2px rgba($primary, 0.1);
          }

          &::placeholder {
            color: lighten($dark-grey, 30%);
          }
        }

        .clear-search-btn {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: lighten($dark-grey, 30%);
          cursor: pointer;
          font-size: 0.9rem;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;

          &:hover {
            color: $dark-grey;
          }
        }
      }

      .view-toggle {
        display: flex;
        border: 1px solid lighten($dark-grey, 40%);
        border-radius: 6px;
        overflow: hidden;

        .view-toggle-btn {
          background: none;
          border: none;
          padding: 8px 12px;
          cursor: pointer;
          color: $dark-grey;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba($primary, 0.05);
          }

          &.active {
            background-color: $primary;
            color: white;
          }
        }
      }
    }
  }

  .search-results-info {
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: rgba($primary, 0.05);
    border-radius: 6px;
    border-left: 3px solid $primary;

    p {
      margin: 0;
      font-size: 0.95rem;
      color: $dark-grey;

      strong {
        color: $primary;
      }
    }
  }

  .loading-container, .empty-state, .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    text-align: center;

    i {
      font-size: 3rem;
      margin-bottom: 20px;
      color: lighten($dark-grey, 30%);
    }

    p {
      margin: 0;
      font-size: 1.1rem;
      color: $dark-grey;

      &.empty-state-subtitle {
        margin-top: 10px;
        font-size: 0.9rem;
        color: lighten($dark-grey, 20%);
      }
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba($primary, 0.1);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
  }

  .error-message {
    i {
      color: $primary;
    }
  }

  // Grid View Styles
  .patients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;

    .patient-tile {
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      display: flex;
      flex-direction: column;
      position: relative;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .user-avatar {
        margin: 20px auto;
      }

      .patient-info {
        padding: 15px;
        flex-grow: 1;

        .patient-name {
          margin: 0 0 5px 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: $dark-grey;
          text-align: center;
        }

        .patient-email {
          margin: 0 0 10px 0;
          font-size: 0.9rem;
          color: lighten($dark-grey, 20%);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .last-update {
          margin: 0;
          font-size: 0.85rem;
          color: lighten($dark-grey, 30%);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;

          i {
            font-size: 0.8rem;
          }
        }
      }

      .patient-actions {
        display: flex;
        border-top: 1px solid lighten($dark-grey, 45%);

        .action-btn {
          flex: 1;
          padding: 12px;
          border: none;
          background: none;
          cursor: pointer;
          color: $dark-grey;
          font-size: 1rem;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba($primary, 0.05);
          }

          &.view-btn:hover {
            color: $primary;
          }

          &.unlink-btn:hover {
            color: #dc3545;
          }

          &:not(:last-child) {
            border-right: 1px solid lighten($dark-grey, 45%);
          }
        }
      }
    }
  }

  // List View Styles
  .patients-list {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid lighten($dark-grey, 45%);
      }

      th {
        background-color: lighten($dark-grey, 48%);
        font-weight: 600;
        color: $dark-grey;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      tr:last-child td {
        border-bottom: none;
      }

      tr:hover td {
        background-color: rgba($primary, 0.02);
      }

      .patient-name-cell {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;

        &.active {
          background-color: rgba(#28a745, 0.1);
          color: #28a745;
        }

        &.pending {
          background-color: rgba(#ffc107, 0.1);
          color: darken(#ffc107, 15%);
        }

        &.inactive {
          background-color: rgba(#dc3545, 0.1);
          color: #dc3545;
        }
      }

      .actions-cell {
        white-space: nowrap;

        .action-btn {
          padding: 6px 10px;
          border: none;
          background: none;
          cursor: pointer;
          color: $dark-grey;
          font-size: 0.9rem;
          border-radius: 4px;
          transition: all 0.2s ease;
          margin-right: 5px;

          &:hover {
            background-color: rgba($primary, 0.05);
          }

          &.view-btn:hover {
            color: $primary;
          }

          &.unlink-btn:hover {
            color: #dc3545;
          }
        }
      }
    }
  }

  // Confirmation Modal Styles
  .confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: white;
      border-radius: 10px;
      width: 90%;
      max-width: 450px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      animation: modal-appear 0.3s ease-out;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid lighten($dark-grey, 45%);

        h4 {
          margin: 0;
          font-size: 1.2rem;
          color: $dark-grey;
        }

        .close-btn {
          background: none;
          border: none;
          color: lighten($dark-grey, 20%);
          font-size: 1.2rem;
          cursor: pointer;
          padding: 5px;

          &:hover {
            color: $dark-grey;
          }
        }
      }

      .modal-body {
        padding: 20px;

        p {
          margin: 0 0 15px 0;
          font-size: 1rem;
          color: $dark-grey;

          &.warning {
            color: #dc3545;
            font-size: 0.9rem;
            font-style: italic;
          }
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 15px 20px;
        border-top: 1px solid lighten($dark-grey, 45%);

        button {
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 0.95rem;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;

          i {
            font-size: 0.9rem;
          }
        }

        .cancel-btn {
          background-color: lighten($dark-grey, 45%);
          border: none;
          color: $dark-grey;

          &:hover {
            background-color: lighten($dark-grey, 40%);
          }
        }

        .confirm-btn {
          background-color: #dc3545;
          border: none;
          color: white;

          &:hover {
            background-color: darken(#dc3545, 5%);
          }
        }
      }
    }
  }
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Responsive Styles
@media (max-width: 768px) {
  .patient-list-view {
    .view-header {
      flex-direction: column;
      align-items: stretch;

      .header-left, .section-title, .header-actions {
        width: 100%;
        justify-content: center;
      }

      .header-actions {
        flex-direction: column;

        .search-container {
          width: 100%;
        }
      }
    }

    .patients-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .patients-list {
      table {
        th, td {
          padding: 10px;
        }

        th:nth-child(2), td:nth-child(2), // Email column
        th:nth-child(4), td:nth-child(4) { // Last Update column
          display: none;
        }
      }
    }
  }
}
</style>
