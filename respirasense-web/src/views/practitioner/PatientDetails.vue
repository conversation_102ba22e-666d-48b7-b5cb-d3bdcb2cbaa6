<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="patient-details">
        <!-- Back Button -->
        <div class="back-button">
          <button @click="goBack" class="btn-back">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <div class="spinner"></div>
          <p>Loading patient data...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ error }}</p>
          <div class="error-actions">
            <button @click="goBack" class="btn-back">
              <i class="fas fa-arrow-left"></i> Return to Dashboard
            </button>
            <button @click="retryLoading" class="btn-retry">
              <i class="fas fa-sync"></i> Retry
            </button>
          </div>
        </div>

        <!-- Patient Data -->
        <div v-else-if="patient" class="patient-content">
          <!-- Patient Profile -->
          <div class="profile-section">
            <div class="profile-header">
              <div class="profile-avatar">
                <UserAvatar
                  :userId="patient.id"
                  :photoURL="patient.photoURL"
                  :name="patient.name"
                  :status="patient.status || 'active'"
                  size="xlarge"
                  :showStatus="true"
                />
              </div>
              <div class="profile-info">
                <h1>{{ patient.name }}</h1>
                <p class="profile-email">
                  <i class="fas fa-envelope"></i> {{ patient.email }}
                </p>
                <div class="profile-details">
                  <div class="detail-item" v-if="patient.age">
                    <i class="fas fa-birthday-cake"></i>
                    <span>Age: {{ patient.age }}</span>
                  </div>
                  <div class="detail-item" v-if="patient.gender">
                    <i class="fas fa-venus-mars"></i>
                    <span>Gender: {{ patient.gender }}</span>
                  </div>
                  <div class="detail-item" v-if="patient.height">
                    <i class="fas fa-ruler-vertical"></i>
                    <span>Height: {{ patient.height }} cm</span>
                  </div>
                  <div class="detail-item" v-if="patient.weight">
                    <i class="fas fa-weight"></i>
                    <span>Weight: {{ patient.weight }} kg</span>
                  </div>
                </div>
              </div>
              <div class="profile-actions">
                <button @click="viewDetailedReport" class="btn-view-report">
                  <i class="fas fa-chart-line"></i> View Detailed Report
                </button>
                <button @click="downloadPatientData" class="btn-download" :disabled="isDownloading">
                  <i class="fas fa-download"></i> {{ isDownloading ? 'Downloading...' : 'Download Report' }}
                </button>
              </div>
            </div>

            <!-- Medical History -->
            <div v-if="patient.medicalHistory && patient.medicalHistory.length" class="medical-history">
              <h3><i class="fas fa-notes-medical"></i> Medical History</h3>
              <ul>
                <li v-for="(condition, index) in patient.medicalHistory" :key="index">
                  {{ condition }}
                </li>
              </ul>
            </div>

            <!-- Medications -->
            <div v-if="patient.medications && patient.medications.length" class="medications">
              <h3><i class="fas fa-pills"></i> Current Medications</h3>
              <ul>
                <li v-for="(medication, index) in patient.medications" :key="index">
                  {{ medication }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Health Readings -->
          <div class="readings-section">
            <div class="section-header">
              <h2><i class="fas fa-heartbeat"></i> Health Readings</h2>
            </div>

            <div v-if="readings.length === 0" class="empty-readings">
              <i class="fas fa-chart-line"></i>
              <p>No health readings available</p>
            </div>

            <div v-else class="readings-timeline">
              <div v-for="(reading, index) in readings"
                   :key="reading.id"
                   class="timeline-item"
                   :class="[getRiskLevelClass(reading.riskLevel), { 'last-item': index === readings.length - 1 }]">
                <div class="timeline-date">
                  {{ formatDate(reading.timestamp) }}
                </div>
                <div class="timeline-content">
                  <div class="reading-header">
                    <h4>
                      <i :class="getRiskLevelIcon(reading.riskLevel)"></i>
                      COPD Risk Level: {{ reading.riskLevel ? reading.riskLevel.toUpperCase() : 'UNKNOWN' }}
                    </h4>
                  </div>
                  <div class="reading-metrics">
                    <div class="metric">
                      <i class="fas fa-lungs"></i>
                      <span>Respiratory Rate: {{ formatDecimal(reading.respiratoryRate) }} bpm</span>
                    </div>
                    <div class="metric">
                      <i class="fas fa-wind"></i>
                      <span>Oxygen Saturation: {{ formatDecimal(reading.oxygenSaturation) }}%</span>
                    </div>
                    <div class="metric">
                      <i class="fas fa-heartbeat"></i>
                      <span>Heart Rate: {{ formatDecimal(reading.heartRate) }} bpm</span>
                    </div>
                    <div class="metric">
                      <i class="fas fa-thermometer-half"></i>
                      <span>Temperature: {{ formatDecimal(reading.temperature) }}°C</span>
                    </div>
                  </div>
                  <div v-if="reading.notes" class="reading-notes">
                    <i class="fas fa-sticky-note"></i>
                    <p>{{ reading.notes }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import UserAvatar from '@/components/common/UserAvatar.vue';
import { usePatientDetails } from './composables/usePatientDetails';

export default {
  name: 'PatientDetails',

  components: {
    AppHeader,
    AppFooter,
    UserAvatar
  },

  setup() {
    return usePatientDetails();
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';
.patient-details {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .back-button {
    margin-bottom: 20px;

    .btn-back {
      display: flex;
      align-items: center;
      background: none;
      border: none;
      color: $primary;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      padding: 0;

      i {
        margin-right: 8px;
      }

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 0;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary-light, 0.1);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .error-message {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    text-align: center;

    i {
      font-size: 36px;
      margin-bottom: 15px;
    }

    p {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 500;
    }

    .error-actions {
      display: flex;
      gap: 15px;
      margin-top: 15px;

      button {
        padding: 10px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        i {
          font-size: 14px;
          margin: 0;
        }
      }

      .btn-back {
        background-color: transparent;
        border: 1px solid #6c757d;
        color: #6c757d;

        &:hover {
          background-color: rgba(108, 117, 125, 0.1);
        }
      }

      .btn-retry {
        background-color: $primary;
        border: none;
        color: white;

        &:hover {
          background-color: darken($primary, 5%);
        }
      }
    }
  }

  .patient-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;

    @media (min-width: 992px) {
      grid-template-columns: 350px 1fr;
    }

    .profile-section {
      background-color: $white;
      border-radius: 8px;
      box-shadow: $shadow-sm;
      padding: 20px;
      border-top: 3px solid $primary;

      .profile-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-bottom: 20px;

        @media (min-width: 768px) {
          flex-direction: row;
          text-align: left;
          align-items: flex-start;
        }

        .profile-avatar {
          margin-bottom: 15px;

          @media (min-width: 768px) {
            margin-right: 20px;
            margin-bottom: 0;
          }
        }

        .profile-info {
          flex: 1;

          h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: 600;
            color: $primary;
          }

          .profile-email {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0 15px 0;
            color: $dark-grey;
            font-size: 14px;

            @media (min-width: 768px) {
              justify-content: flex-start;
            }

            i {
              margin-right: 5px;
            }
          }

          .profile-details {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;

            .detail-item {
              display: flex;
              align-items: center;
              font-size: 14px;
              color: $dark-grey;

              i {
                margin-right: 5px;
                width: 16px;
                color: $primary;
              }
            }
          }
        }

        .profile-actions {
          margin-top: 20px;
          display: flex;
          flex-direction: column;
          gap: 10px;

          @media (min-width: 768px) {
            margin-top: 0;
            margin-left: 20px;
          }

          .btn-view-report {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: $white;
            color: $primary;
            border: 1px solid $primary;
            border-radius: 4px;
            padding: 10px 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;

            i {
              margin-right: 5px;
            }

            &:hover {
              background-color: rgba($primary-light, 0.05);
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }

          .btn-download {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: $primary;
            color: $white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;

            i {
              margin-right: 5px;
            }

            &:hover {
              background-color: darken($primary, 5%);
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &:disabled {
              background-color: lighten($primary-light, 15%);
              cursor: not-allowed;
              transform: none;
              box-shadow: none;
            }
          }
        }
      }

      .medical-history, .medications {
        margin-top: 25px;

        h3 {
          font-size: 18px;
          margin: 0 0 15px 0;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: $primary;
          }
        }

        ul {
          margin: 0;
          padding: 0 0 0 20px;

          li {
            margin-bottom: 8px;
            color: $dark-grey;
          }
        }
      }
    }

    .readings-section {
      background-color: $white;
      border-radius: 8px;
      box-shadow: $shadow-sm;
      padding: 20px;
      border-top: 3px solid $primary;

      .section-header {
        margin-bottom: 20px;

        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          display: flex;
          align-items: center;
          color: $primary;
          padding-bottom: 12px;
          border-bottom: 1px solid rgba($primary-light, 0.1);

          i {
            margin-right: 10px;
            color: $primary;
          }
        }
      }

      .empty-readings {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #666;

        i {
          font-size: 48px;
          margin-bottom: 15px;
          color: #ccc;
        }

        p {
          margin: 0;
          font-size: 16px;
        }
      }

      .readings-timeline {
        position: relative;

        &:before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: 20px;
          width: 2px;
          background-color: rgba($primary-light, 0.2);
        }

        .timeline-item {
          position: relative;
          padding-left: 50px;
          padding-bottom: 30px;

          &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 16px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #ccc;
            z-index: 1;
          }

          &.high-risk:before {
            background-color: $danger;
          }

          &.medium-risk:before {
            background-color: $primary;
          }

          &.low-risk:before {
            background-color: $primary-light;
          }

          &.normal:before {
            background-color: $success;
          }

          &.last-item {
            padding-bottom: 0;
          }

          .timeline-date {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: $primary;
            background-color: rgba($primary-light, 0.05);
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
          }

          .timeline-content {
            background-color: $white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: $shadow-sm;

            .reading-header {
              margin-bottom: 15px;

              h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                display: flex;
                align-items: center;

                i {
                  margin-right: 8px;
                }
              }

              .high-risk h4 {
                color: $danger;
              }

              .medium-risk h4 {
                color: $primary;
              }

              .low-risk h4 {
                color: $primary-light;
              }

              .normal h4 {
                color: $success;
              }
            }

            .reading-metrics {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
              gap: 10px;
              margin-bottom: 15px;
              background-color: rgba($primary-light, 0.05);
              padding: 8px 12px;
              border-radius: 6px;

              .metric {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: $dark-grey;

                i {
                  margin-right: 8px;
                  width: 16px;
                  color: $primary;
                }

                span {
                  font-size: 0.9rem;
                  font-weight: 500;
                  color: $dark-grey;
                  background-color: $white;
                  padding: 4px 8px;
                  border-radius: 4px;
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                }
              }
            }

            .reading-notes {
              display: flex;
              background-color: rgba($primary-light, 0.05);
              padding: 10px;
              border-radius: 4px;
              border-left: 3px solid rgba($primary-light, 0.2);

              i {
                margin-right: 8px;
                margin-top: 3px;
                color: $primary;
              }

              p {
                margin: 0;
                font-size: 14px;
                color: $dark-grey;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .patient-details {
    .patient-content {
      grid-template-columns: 1fr;
    }

    .readings-section {
      .readings-timeline {
        .timeline-item {
          .timeline-content {
            .reading-metrics {
              grid-template-columns: 1fr;
            }
          }
        }
      }
    }
  }
}
</style>
