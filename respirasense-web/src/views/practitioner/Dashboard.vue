<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="practitioner-dashboard">
        <div class="dashboard-header">
          <div class="section-title">
            <i class="fas fa-user-md"></i>
            <h1>Practitioner Dashboard</h1>
          </div>
          <div class="header-actions">
            <button @click="viewAllPatients" class="view-all-btn">
              <i class="fas fa-users"></i> View All Patients
            </button>
          </div>
        </div>

        <!-- Dashboard Stats -->
        <div class="dashboard-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3>Active Patients</h3>
              <p class="stat-value">{{ activePatientCount }}</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-bell"></i>
            </div>
            <div class="stat-content">
              <h3>Pending Requests</h3>
              <p class="stat-value">{{ pendingRequestsCount }}</p>
            </div>
          </div>
        </div>

        <!-- Main Dashboard Tabs -->
        <div class="dashboard-tabs">
          <div class="tab-header">
            <button
              @click="mainTab = 'patient-management'"
              class="tab-button"
              :class="{ active: mainTab === 'patient-management' }"
              title="Manage your patients and linking requests"
            >
              <div class="tab-icon">
                <i class="fas fa-user-plus"></i>
              </div>
              <span>Patient Management</span>
            </button>
            <button
              @click="mainTab = 'patient-readings'"
              class="tab-button"
              :class="{ active: mainTab === 'patient-readings' }"
              title="View patient self-reading activity"
            >
              <div class="tab-icon">
                <i class="fas fa-clipboard-check"></i>
              </div>
              <span>Patient Self-Readings</span>
            </button>
            <button
              @click="mainTab = 'patient-activity'"
              class="tab-button"
              :class="{ active: mainTab === 'patient-activity' }"
              title="View recent patient activity"
            >
              <div class="tab-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <span>Recent Activity</span>
            </button>
          </div>

          <!-- Patient Management Tab Content -->
          <div v-if="mainTab === 'patient-management'" class="tab-content">
            <div class="section-container">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-users"></i>
                  <h2>My Patients</h2>
                </div>
                <div class="section-actions">
                  <button @click="showAddPatientModal = true" class="add-patient-btn">
                    <i class="fas fa-user-plus"></i> Add New Patient
                  </button>
                </div>
              </div>

              <PatientManagement />

              <!-- Patient Linking Requests Section -->
              <div v-if="pendingLinkingRequestsCount > 0" class="linking-requests-section">
                <div class="section-header">
                  <div class="section-title">
                    <i class="fas fa-paper-plane"></i>
                    <h3>Pending Linking Requests</h3>
                    <span class="badge">{{ pendingLinkingRequestsCount }}</span>
                  </div>
                </div>
                <PatientLinkingRequests />
              </div>
            </div>
          </div>

          <!-- Patient Self-Readings Tab Content -->
          <div v-if="mainTab === 'patient-readings'" class="tab-content">
            <div class="section-container">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-clipboard-check"></i>
                  <h2>Patient Self-Readings</h2>
                </div>
                <div class="time-range-selector">
                  <label for="timeRange">Time Range:</label>
                  <select id="timeRange" v-model="selectedTimeRange" class="time-range-select">
                    <option value="1m">1 Month</option>
                    <option value="3m">3 Months</option>
                    <option value="6m">6 Months</option>
                    <option value="1y">1 Year</option>
                  </select>
                </div>
              </div>

              <div v-if="loading" class="loading-container">
                <div class="spinner"></div>
                <p>Loading patient data...</p>
              </div>

              <div v-else-if="patients.length === 0" class="empty-state">
                <i class="fas fa-user-plus"></i>
                <p>No patients assigned yet</p>
                <p class="empty-state-subtitle">Patient readings will appear here once patients are linked</p>
                <button @click="showAddPatientModal = true" class="btn-primary">
                  <i class="fas fa-user-plus"></i> Link with Patient
                </button>
              </div>

              <div v-else class="patient-readings-container">
                <div v-for="patient in patients" :key="patient.id" class="patient-reading-card" :class="getRiskLevelClass(patient.riskLevel)">
                  <div class="card-header">
                    <div class="patient-identity">
                      <UserAvatar
                        :userId="patient.id"
                        :photoURL="patient.photoURL"
                        :name="patient.name"
                        :status="patient.status || 'active'"
                        size="medium"
                        :showStatus="true"
                      />
                      <div class="patient-details">
                        <h3 class="patient-name">{{ patient.name }}</h3>
                        <p class="patient-email"><i class="fas fa-envelope"></i> {{ patient.email }}</p>
                        <div v-if="patient.riskLevel && patient.riskLevel !== 'unknown'" class="risk-level">
                          <i :class="getRiskLevelIcon(patient.riskLevel)"></i>
                          <span>{{ patient.riskLevel.toUpperCase() }} Risk</span>
                        </div>
                      </div>
                    </div>
                    <div class="reading-stats">
                      <div v-if="patientStats[patient.id]" class="stat-items">
                        <div class="stat-item">
                          <span class="stat-value">{{ patientStats[patient.id].totalReadings }}</span>
                          <span class="stat-label">Total Readings</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-value">{{ patientStats[patient.id].adherenceRate }}%</span>
                          <span class="stat-label">Adherence Rate</span>
                        </div>
                        <div class="stat-item last-reading-stat">
                          <span class="stat-value">{{ patientStats[patient.id].lastReading }}</span>
                          <span class="stat-label">Last Reading</span>
                        </div>
                      </div>
                      <div v-else class="loading-stats">
                        <div class="spinner-small"></div>
                        <span>Loading stats...</span>
                      </div>
                    </div>
                  </div>

                  <div class="reading-chart-container">
                    <div class="chart-header">
                      <h4><i class="fas fa-chart-line"></i> Reading Activity</h4>
                      <div class="time-range-selector">
                        <select v-model="selectedTimeRange" class="time-range-dropdown">
                          <option value="1m">1 Month</option>
                          <option value="3m">3 Months</option>
                          <option value="6m">6 Months</option>
                          <option value="1y">1 Year</option>
                        </select>
                      </div>
                    </div>
                    <div class="heatmap-wrapper" :class="{ 'loading': loading }">
                      <HeatmapBar
                        :key="`heatmap-${patient.id}-${selectedTimeRange}`"
                        :series="getPatientReadingPresenceData(patient.id)"
                        :time-range="selectedTimeRange"
                        :loading="loading"
                        :from-date="getDateRangeForTimeRange(selectedTimeRange).fromDate"
                        :to-date="getDateRangeForTimeRange(selectedTimeRange).toDate"
                        @cell-click="handleHeatmapCellClick"
                      />
                    </div>
                  </div>

                  <div class="card-actions">
                    <button @click="viewPatientDetails(patient.id)" class="btn-primary">
                      <i class="fas fa-eye"></i> View Details
                    </button>
                    <button @click="viewPatientReports(patient.id)" class="btn-outline">
                      <i class="fas fa-file-medical-alt"></i> View Reports
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Patient Activity Tab Content -->
          <div v-if="mainTab === 'patient-activity'" class="tab-content">
            <div class="section-container">
              <div class="section-header">
                <div class="section-title">
                  <i class="fas fa-activity"></i>
                  <h2>Recent Patient Activity</h2>
                </div>
              </div>

              <PatientActivityTimeline
                :activities="patientActivities"
                :loading="loading"
                @load-more="handleLoadMoreActivities"
              />
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ error }}</p>
        </div>
      </div>
    </main>
    <AppFooter />

    <!-- Add Patient Modal -->
    <AddPatientModal
      :is-open="showAddPatientModal"
      @close="showAddPatientModal = false"
      @patient-added="handlePatientAdded"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { format, startOfDay, endOfDay, differenceInDays } from 'date-fns';
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import PatientLinkingRequests from '@/components/practitioner/PatientLinkingRequests.vue';
import PatientManagement from '@/components/practitioner/PatientManagement.vue';
import PatientActivityTimeline from '@/components/practitioner/PatientActivityTimeline.vue';
import HeatmapBar from '@/components/charts/HeatmapBar.vue';
import AddPatientModal from '@/components/practitioner/AddPatientModal.vue';
import UserAvatar from '@/components/common/UserAvatar.vue';
import { usePractitionerDashboard } from './composables/usePractitionerDashboard';
import { useStore } from 'vuex';

export default {
  name: 'PractitionerDashboard',

  components: {
    AppHeader,
    AppFooter,
    PatientLinkingRequests,
    PatientManagement,
    PatientActivityTimeline,
    HeatmapBar,
    AddPatientModal,
    UserAvatar
  },

  setup() {
    const store = useStore();
    const router = useRouter();
    const mainTab = ref('patient-readings');
    const showAddPatientModal = ref(false);
    const selectedTimeRange = ref('6m'); // Default to 6 months
    const defaultUserIcon = require('@/assets/DefaultUserIcon.webp');
    const patientActivities = ref([]);
    const patientStats = ref({});
    const loading = ref(false);

    // Get the count of pending linking requests
    const pendingLinkingRequestsCount = computed(() => {
      const requests = store.getters['practitioner/getPatientLinkingRequests'] || [];
      return requests.filter(r => r.status === 'pending').length;
    });

    const dashboard = usePractitionerDashboard();

    // Initialize patient activities and stats
    onMounted(async () => {
      try {
        loading.value = true;

        // Fetch patient activities
        const activities = await store.dispatch('practitioner/fetch_patient_activities');
        if (activities) {
          patientActivities.value = activities;
        }

        // Initialize patient stats
        const patients = await store.dispatch('practitioner/fetch_patients');
        if (patients && patients.length > 0) {
          // Load stats for each patient
          for (const patient of patients) {
            try {
              patientStats.value[patient.id] = await getReadingStats(patient.id);
            } catch (err) {
              console.error(`Error loading stats for patient ${patient.id}:`, err);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing dashboard data:', error);
      } finally {
        loading.value = false;
      }
    });

    // Watch for changes in the selected time range
    watch(selectedTimeRange, async (newRange, oldRange) => {
      // Set loading state
      loading.value = true;

      // Clear the reading data cache when the time range changes
      readingDataCache.value = {};

      // The chart will automatically update because we're using the selectedTimeRange
      // as a prop in the ChartJSTimeSeriesChart component and we added a key that includes the time range

      // Refresh patient stats for the new time range
      try {
        const patients = await store.dispatch('practitioner/fetch_patients');
        if (patients && patients.length > 0) {
          // Update stats for each patient with the new time range
          for (const patient of patients) {
            try {
              patientStats.value[patient.id] = await getReadingStats(patient.id);
            } catch (err) {
              console.error(`Error refreshing stats for patient ${patient.id}:`, err);
            }
          }
        }
      } catch (error) {
        console.error('Error refreshing patient stats:', error);
      } finally {
        // Clear loading state
        loading.value = false;
      }
    });

    // Navigate to the patient list view
    const viewAllPatients = () => {
      router.push('/practitioner/patients');
    };

    // Handle image loading errors
    const handleImageError = (event) => {
      event.target.src = defaultUserIcon;
    };

    // Handle patient added event from modal
    const handlePatientAdded = async () => {
      // Refresh dashboard data
      await dashboard.fetchDashboardData();
    };

    // Handle load more activities event
    const handleLoadMoreActivities = (moreActivities) => {
      if (moreActivities && moreActivities.length > 0) {
        patientActivities.value = [...patientActivities.value, ...moreActivities];
      }
    };

    // Cache for patient reading data to prevent unnecessary API calls
    const readingDataCache = ref({});

    // Get reading presence data for a specific patient using real data
    const getPatientReadingPresenceData = async (patientId) => {
      // Check if we have cached data for this patient and time range
      const cacheKey = `${patientId}-${selectedTimeRange.value}`;
      if (readingDataCache.value[cacheKey]) {
        return readingDataCache.value[cacheKey];
      }
      try {

        // Define valid time ranges and their corresponding days
        const validTimeRanges = {
          '1m': { days: 30, step: 1 },
          '3m': { days: 90, step: 1 },
          '6m': { days: 180, step: 1 },
          '1y': { days: 365, step: 2 } // Use larger step size for year view to reduce density
        };

        // Get the configuration for the selected time range, or use default (6 months)
        const timeRangeConfig = validTimeRanges[selectedTimeRange.value] || validTimeRanges['6m'];

        // Get date range for the selected time range
        const { fromDate, toDate } = getDateRangeForTimeRange(selectedTimeRange.value);

        // Fetch all readings for this patient within the date range
        const patientReadings = await getAllPatientReadingsInRange(patientId, fromDate, toDate);



        // Create a map of dates with readings (count unique days, not total readings)
        const readingDates = new Map();
        patientReadings.forEach(reading => {
          if (reading.timestamp) {
            // Use toDateString() to get just the date part without time
            const dateStr = new Date(reading.timestamp).toDateString();
            readingDates.set(dateStr, true);
          }
        });

        // Generate data points for each day in the time range
        const data = [];
        const daysToGenerate = timeRangeConfig.days;
        const stepSize = timeRangeConfig.step || 1; // Default to 1 if step is not defined

        // Define now here to avoid reference error
        const now = new Date();

        for (let i = 0; i <= daysToGenerate; i += stepSize) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);

          // Check if there's a reading for this date
          const hasReading = readingDates.has(date.toDateString()) ? 1 : 0;

          data.push({
            x: date.toISOString(),
            y: hasReading
          });
        }

        // Ensure we have valid data
        if (data.length === 0) {
          // Add fallback data points to prevent chart errors
          data.push({ x: new Date(now.getTime() - 86400000).toISOString(), y: 0 });
          data.push({ x: now.toISOString(), y: 0 });
        }

        // Create the series with the real data
        const series = [{
          name: 'Reading Taken',
          data: data
        }];

        // Cache the results
        readingDataCache.value[cacheKey] = series;

        return series;
      } catch (error) {
        console.error(`Error fetching reading data for patient ${patientId}:`, error);

        // Create a minimal valid dataset to prevent chart errors
        const fallbackSeries = [{
          name: 'Reading Taken',
          data: [
            { x: new Date(Date.now() - 86400000).toISOString(), y: 0 },
            { x: new Date().toISOString(), y: 0 }
          ]
        }];

        // Cache the fallback data
        readingDataCache.value[cacheKey] = fallbackSeries;

        return fallbackSeries;
      }
    };

    // Get all readings for a patient within a date range
    const getAllPatientReadingsInRange = async (patientId, fromDate, toDate) => {
      try {
        // Use the store's existing method to get all patient readings
        const allReadings = await store.dispatch('practitioner/get_patient_readings', patientId) || [];

        // Filter readings by date range
        const filteredReadings = allReadings.filter(reading => {
          if (!reading.timestamp) return false;
          const readingDate = new Date(reading.timestamp);
          return readingDate >= fromDate && readingDate <= toDate;
        });

        // We'll just use the filtered readings since the date range specific method
        // is not available in the main store
        return filteredReadings;
      } catch (error) {
        console.error(`Error fetching all readings for patient ${patientId}:`, error);
        return [];
      }
    };

    // Get reading statistics for a specific patient using real data
    const getReadingStats = async (patientId) => {
      try {
        // Get date range for the selected time range
        const { fromDate, toDate } = getDateRangeForTimeRange(selectedTimeRange.value);

        // Fetch all readings for this patient within the date range
        const readings = await getAllPatientReadingsInRange(patientId, fromDate, toDate);

        // Check if we have valid readings
        if (!readings || !Array.isArray(readings) || readings.length === 0) {
          return {
            totalReadings: 0,
            adherenceRate: 0,
            lastReading: 'Never'
          };
        }

        // Calculate total readings
        const totalReadings = readings.length;

        // Count unique days with readings (a patient can take multiple readings per day)
        const daysWithReadings = new Set();
        readings.forEach(reading => {
          if (reading.timestamp) {
            // Get just the date part (year-month-day) to count unique days
            const readingDate = new Date(reading.timestamp);
            const dateString = readingDate.toISOString().split('T')[0];
            daysWithReadings.add(dateString);
          }
        });

        // Calculate the total number of days in the selected range
        const daysConfig = {
          '1m': 30,
          '3m': 90,
          '6m': 180,
          '1y': 365
        };
        const daysInRange = daysConfig[selectedTimeRange.value] || 180;

        // Calculate adherence rate based on days with readings divided by total days in range
        const uniqueDaysWithReadings = daysWithReadings.size;
        const adherenceRate = Math.min(100, Math.round((uniqueDaysWithReadings / daysInRange) * 100));

        // Find the last reading date from the filtered readings
        // Sort by timestamp in descending order to get the most recent reading first
        const sortedReadings = [...readings].sort((a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        const lastReadingDate = sortedReadings.length > 0 ? new Date(sortedReadings[0].timestamp) : null;

        // Format the last reading date
        let lastReading = 'Never';
        if (lastReadingDate) {
          // Always show the full date with year to avoid confusion
          lastReading = format(lastReadingDate, 'MMM d, yyyy');

          // Add a relative time indicator in parentheses for context
          const now = new Date();
          const diffMs = now - lastReadingDate;
          const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

          if (diffDays === 0) {
            lastReading += ' (Today)';
          } else if (diffDays === 1) {
            lastReading += ' (Yesterday)';
          } else if (diffDays > 0 && diffDays < 7) {
            lastReading += ` (${diffDays} days ago)`;
          } else if (diffDays < 0) {
            // Handle future dates (this shouldn't happen in real data)
            lastReading += ' (Future date)';
          }
        }

        return {
          totalReadings,
          adherenceRate,
          lastReading
        };
      } catch (error) {
        console.error(`Error calculating reading stats for patient ${patientId}:`, error);

        // Return default values in case of error
        return {
          totalReadings: 0,
          adherenceRate: 0,
          lastReading: 'Never'
        };
      }
    };

    // Navigate to patient reports
    const viewPatientReports = (patientId) => {
      // Store the patient ID in Vuex before navigating
      store.commit('practitioner/SET_SELECTED_PATIENT_ID', patientId);
      router.push(`/practitioner/patient/${patientId}/reports`);
    };

    // Get date range for a specific time range
    const getDateRangeForTimeRange = (timeRange) => {
      const daysConfig = {
        '1m': 30,
        '3m': 90,
        '6m': 180,
        '1y': 365
      };

      const now = new Date();
      const toDate = now;
      const fromDate = new Date(now);
      fromDate.setDate(now.getDate() - (daysConfig[timeRange] || 180));

      return { fromDate, toDate };
    };

    // Handle heatmap cell click
    const handleHeatmapCellClick = (point) => {
      // You could navigate to a specific date view or show a modal with details
      // For now, we'll just handle the click event
    };

    return {
      ...dashboard,
      mainTab,
      showAddPatientModal,
      selectedTimeRange,
      patientActivities,
      patientStats,
      readingDataCache,
      loading,
      pendingLinkingRequestsCount,
      viewAllPatients,
      handleImageError,
      handlePatientAdded,
      handleLoadMoreActivities,
      getPatientReadingPresenceData,
      getReadingStats,
      viewPatientReports,
      handleHeatmapCellClick,
      getDateRangeForTimeRange
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.practitioner-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-tabs {
    margin-top: 20px;

    .tab-header {
      display: flex;
      border-bottom: 2px solid rgba($primary-light, 0.2);
      margin-bottom: 20px;
      gap: 8px;

      .tab-button {
        padding: 12px 20px;
        background: none;
        border: none;
        border-bottom: 3px solid transparent;
        font-size: 1rem;
        font-weight: 500;
        color: $dark-grey;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.25s ease;
        position: relative;
        border-radius: 8px 8px 0 0;
        margin-bottom: -2px;

        .tab-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: rgba($primary-light, 0.1);
          transition: all 0.25s ease;

          i {
            color: $primary;
            font-size: 1rem;
          }
        }

        &:hover {
          color: $primary;

          .tab-icon {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }

        &.active {
          color: $primary;
          border-bottom-color: $primary;
          background-color: rgba($primary-light, 0.08);
          font-weight: 600;

          .tab-icon {
            background-color: $primary;
            transform: scale(1.1);
            box-shadow: 0 2px 5px rgba($primary, 0.3);

            i {
              color: $white;
            }
          }
        }

        &:hover:not(.active) {
          background-color: rgba($primary-light, 0.05);
          color: $primary;

          .tab-icon {
            background-color: rgba($primary-light, 0.2);
            transform: scale(1.05);
          }
        }

        .badge {
          position: absolute;
          top: 6px;
          right: 6px;
          background-color: $primary;
          color: white;
          font-size: 0.7rem;
          font-weight: 600;
          min-width: 18px;
          height: 18px;
          border-radius: 9px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 5px;
        }
      }
    }

    .tab-content {
      .tab-pane {
        animation: fadeIn 0.3s ease;
      }
    }
  }

  .linking-requests-section {
    margin-top: 30px;

    .section-header {
      margin-bottom: 20px;

      .section-title {
        display: flex;
        align-items: center;

        i {
          font-size: 18px;
          margin-right: 10px;
          color: $primary;
        }

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: $primary;
        }

        .badge {
          margin-left: 10px;
          background-color: $primary;
          color: white;
          font-size: 0.7rem;
          font-weight: 600;
          min-width: 18px;
          height: 18px;
          border-radius: 9px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 5px;
        }
      }
    }
  }

  .dashboard-header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.2);

    .section-title {
      display: flex;
      align-items: center;

      i {
        font-size: 24px;
        margin-right: 10px;
        color: $primary;
      }

      h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        color: $primary;
      }
    }

    .header-actions {
      .view-all-btn {
        padding: 8px 16px;
        background-color: $white;
        color: $primary;
        border: 1px solid $primary;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba($primary-light, 0.05);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        i {
          font-size: 14px;
          color: $primary;
        }
      }
    }
  }

  .dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 25px;

    .stat-card {
      background-color: $white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: $shadow-sm;
      display: flex;
      align-items: center;
      border-top: 3px solid $primary;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba($primary-light, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        i {
          font-size: 18px;
          color: $primary;
        }
      }

      .stat-content {
        flex: 1;

        h3 {
          margin: 0 0 3px 0;
          font-size: 14px;
          color: $dark-grey;
        }

        .stat-value {
          margin: 0;
          font-size: 22px;
          font-weight: 600;
          color: $primary;
        }
      }
    }
  }

  .section-container {
    background-color: $white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: $shadow-sm;
    margin-bottom: 30px;
    border-top: 3px solid $primary;

    .section-header {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba($primary-light, 0.1);

      .section-title {
        display: flex;
        align-items: center;

        i {
          font-size: 20px;
          margin-right: 10px;
          color: $primary;
        }

        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: $primary;
        }
      }

      .section-actions {
        .add-patient-btn {
          padding: 8px 16px;
          background-color: $white;
          color: $primary;
          border: 1px solid $primary;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;

          i {
            font-size: 14px;
            color: $primary;
          }

          &:hover {
            background-color: rgba($primary-light, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary-light, 0.1);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    i {
      font-size: 48px;
      margin-bottom: 15px;
      color: #ccc;
    }

    p {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }

    .empty-state-subtitle {
      margin-top: 5px;
      font-size: 14px;
      color: #999;
    }
  }

  // Patient Trends List
  .patient-trends-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;

    .patient-trend-card {
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      border-left: 4px solid #ccc;
      background-color: #f9f9f9;

      &.high-risk {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
      }

      &.medium-risk {
        border-left-color: #fd7e14;
        background-color: rgba(253, 126, 20, 0.05);
      }

      &.low-risk {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
      }

      &.normal {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
      }

      .trend-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .risk-level {
          display: inline-flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;

          i {
            margin-right: 5px;
          }
        }
      }

      .trend-chart {
        flex: 1;
        margin-bottom: 15px;
        position: relative;
        height: 80px;
      }

      .patient-profile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px dashed rgba(0, 0, 0, 0.1);

        .patient-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid $primary-light;
        }

        .view-details-btn {
          padding: 6px 10px;
          background-color: $primary;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 13px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          i {
            margin-right: 5px;
          }

          &:hover {
            background-color: darken($primary, 10%);
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }

  .patient-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .patient-card {
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      border-left: 4px solid #ccc;
      background-color: #f9f9f9;

      &.high-risk {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
      }

      &.medium-risk {
        border-left-color: #fd7e14;
        background-color: rgba(253, 126, 20, 0.05);
      }

      &.low-risk {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
      }

      &.normal {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
      }

      .patient-info {
        margin-bottom: 15px;

        h3 {
          margin: 0 0 10px 0;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0 0 5px 0;
          font-size: 14px;
          color: #666;
          display: flex;
          align-items: center;

          i {
            margin-right: 5px;
          }
        }

        .no-readings {
          color: #999;
          font-style: italic;
        }

        .risk-level {
          display: inline-flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
          margin-top: 10px;

          i {
            margin-right: 5px;
          }
        }
      }

      .patient-actions {
        .view-details-btn {
          width: 100%;
          padding: 8px 12px;
          background-color: $primary;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          i {
            margin-right: 5px;
          }

          &:hover {
            background-color: darken($primary, 10%);
          }
        }
      }
    }
  }

  .request-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .request-card {
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background-color: #f9f9f9;
      border-left: 4px solid $primary;

      .request-info {
        margin-bottom: 15px;

        h3 {
          margin: 0 0 10px 0;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0 0 5px 0;
          font-size: 14px;
          color: #666;
          display: flex;
          align-items: center;

          i {
            margin-right: 5px;
            width: 16px;
          }
        }
      }

      .request-actions {
        display: flex;
        gap: 10px;

        button {
          flex: 1;
          padding: 8px 12px;
          border: none;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          i {
            margin-right: 5px;
          }
        }

        .approve-btn {
          background-color: #28a745;
          color: white;

          &:hover {
            background-color: darken(#28a745, 5%);
          }
        }

        .reject-btn {
          background-color: #dc3545;
          color: white;

          &:hover {
            background-color: darken(#dc3545, 5%);
          }
        }
      }
    }
  }

  .error-message {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    i {
      font-size: 20px;
      margin-right: 10px;
    }

    p {
      margin: 0;
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Time Range Selector
.time-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: $dark-grey;

  label {
    font-weight: 500;
  }

  .time-range-select {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid lighten($dark-grey, 40%);
    font-size: 0.9rem;
    background-color: white;
    cursor: pointer;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }

    &:hover {
      border-color: lighten($primary, 20%);
    }
  }
}

// Patient Readings Container
.patient-readings-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;

  .patient-reading-card {
    background-color: $white;
    border-radius: 8px;
    box-shadow: $shadow-sm;
    overflow: hidden;
    border-top: 3px solid $primary;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.high-risk {
      border-left: 4px solid $danger;
    }

    &.medium-risk {
      border-left: 4px solid $primary;
    }

    &.low-risk {
      border-left: 4px solid $primary-light;
    }

    &.normal {
      border-left: 4px solid $success;
    }

    .card-header {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba($primary-light, 0.1);
      flex-wrap: wrap;
      gap: 15px;

      .patient-identity {
        display: flex;
        align-items: center;
        gap: 15px;

        .patient-details {
          .patient-name {
            margin: 0 0 5px 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: $primary;
          }

          .patient-email {
            margin: 0 0 5px 0;
            font-size: 0.9rem;
            color: $dark-grey;
            display: flex;
            align-items: center;

            i {
              margin-right: 5px;
              color: $primary;
            }
          }

          .risk-level {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            background-color: rgba($primary-light, 0.05);

            i {
              margin-right: 5px;
              color: $primary;
            }

            span {
              color: $dark-grey;
            }
          }
        }
      }

      .reading-stats {
        display: flex;
        gap: 15px;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          min-width: 80px;
          background-color: rgba($primary-light, 0.05);
          padding: 8px 12px;
          border-radius: 6px;
          transition: transform 0.2s ease;

          &:hover {
            transform: translateY(-2px);
          }

          .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: $primary;
          }

          .stat-label {
            font-size: 0.8rem;
            color: $dark-grey;
            text-align: center;
          }
        }

        .last-reading-stat {
          min-width: 150px;
          border-left: 3px solid rgba($primary-light, 0.3);

          .stat-value {
            font-size: 1rem;
            line-height: 1.2;
            white-space: normal;
            max-width: 150px;
          }
        }
      }
    }

    .reading-chart-container {
      padding: 20px;
      border-top: 1px solid rgba($primary-light, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h4 {
          margin: 0;
          font-size: 1rem;
          font-weight: 600;
          color: $primary;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: $primary;
          }
        }
      }

      .heatmap-wrapper {
        position: relative;

        // Add a loading indicator
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255, 255, 255, 0.7);
          z-index: 1;
          display: none;
        }

        &.loading::before {
          display: block;
        }
      }

      .time-range-selector {
        .time-range-dropdown {
          padding: 6px 10px;
          border-radius: 4px;
          border: 1px solid rgba($primary-light, 0.3);
          background-color: $white;
          font-size: 0.9rem;
          color: $dark-grey;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: $primary;
          }

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 2px rgba($primary, 0.2);
          }
        }
      }
    }

    .card-actions {
      padding: 15px 20px;
      display: flex;
      gap: 10px;
      border-top: 1px solid rgba($primary-light, 0.1);

      button {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        i {
          font-size: 0.9rem;
        }
      }

      .btn-primary {
        background-color: $primary;
        border: none;
        color: white;

        &:hover {
          background-color: darken($primary, 5%);
          transform: translateY(-1px);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
      }

      .btn-outline {
        background-color: $white;
        border: 1px solid $primary;
        color: $primary;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba($primary-light, 0.05);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .practitioner-dashboard {
    .dashboard-stats {
      grid-template-columns: 1fr;
    }

    .patient-trends-list {
      grid-template-columns: 1fr;
    }

    .patient-list, .request-list {
      grid-template-columns: 1fr;
    }

    .patient-readings-container {
      .patient-reading-card {
        .card-header {
          flex-direction: column;
          align-items: flex-start;

          .patient-identity {
            width: 100%;
          }

          .reading-stats {
            width: 100%;
            justify-content: space-between;
          }
        }

        .reading-chart-container {
          .chart-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;

            .time-range-selector {
              width: 100%;

              .time-range-dropdown {
                width: 100%;
              }
            }
          }

          .heatmap-wrapper {
            margin-top: 10px;
          }
        }

        .card-actions {
          flex-direction: column;

          button {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
