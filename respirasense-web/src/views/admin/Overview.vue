<template>
  <div class="overview-container">
    <div class="stats-grid">
      <stat-card
        title="Total Patients"
        :value="stats.totalPatients"
        icon="users"
      />
      <stat-card
        title="Active Patients"
        :value="stats.activePatients"
        icon="user-check"
      />
      <stat-card
        title="Critical Alerts"
        :value="stats.criticalAlerts"
        icon="alert-triangle"
        :is-warning="stats.criticalAlerts > 0"
      />
      <stat-card
        title="Daily Readings"
        :value="stats.dailyReadings"
        icon="activity"
      />
    </div>

    <div class="charts-container">
      <ChartJSLineChart
        :series="chartData"
        title="Patient Readings Trend"
        :loading="loading"
        yAxisLabel="Number of Readings"
        height="350"
      />
    </div>

    <div class="alerts-container">
      <h3>Recent Alerts</h3>
      <alert-list :alerts="recentAlerts" />
    </div>

    <system-status
      v-if="isAdmin"
      :health-status="systemHealth"
    />

    <!-- Pending Registrations Section -->
    <pending-registrations />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import OverviewService from '@/services/OverviewService';
import StatCard from '@/components/dashboard/StatCard.vue';
import ChartJSLineChart from '@/components/charts/ChartJSLineChart.vue';
import AlertList from '@/components/alerts/AlertList.vue';
import SystemStatus from '@/components/dashboard/SystemStatus.vue';
import PendingRegistrations from './components/PendingRegistrations.vue';

export default {
  name: 'Overview',
  components: {
    StatCard,
    ChartJSLineChart,
    AlertList,
    SystemStatus,
    PendingRegistrations
  },
  setup() {
    const stats = ref({
      totalPatients: 0,
      activePatients: 0,
      criticalAlerts: 0,
      dailyReadings: 0
    });
    const chartData = ref([]);
    const recentAlerts = ref([]);
    const systemHealth = ref({});
    const isAdmin = ref(false); // Set this based on user role
    const loading = ref(true);

    const loadOverviewData = async () => {
      console.log('🔍 Starting to load overview data...');
      loading.value = true;

      try {
        // Load all data in parallel
        console.log('📊 Fetching overview stats, alerts, metrics, and health...');
        const [statsData, alerts, metrics, health] = await Promise.all([
          OverviewService.getOverviewStats(),
          OverviewService.getRecentAlerts(),
          OverviewService.getDailyMetrics(),
          OverviewService.getSystemHealth()
        ]);

        console.log('📈 Stats Data:', statsData);
        console.log('⚠️ Alerts:', alerts);
        console.log('📊 Metrics:', metrics);
        console.log('💻 System Health:', health);

        stats.value = statsData;
        recentAlerts.value = alerts;
        chartData.value = formatChartData(metrics);
        systemHealth.value = health;

        console.log('✅ Overview data loaded successfully');
      } catch (error) {
        console.error('❌ Error loading overview data:', error);
      } finally {
        loading.value = false;
      }
    };

    const formatChartData = (metrics) => {
      console.log('📊 Formatting chart data from metrics:', metrics);

      // Ensure metrics is an array and has data
      if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
        console.warn('No metrics data available');
        return [];
      }

      // Sort metrics by date
      const sortedMetrics = [...metrics].sort((a, b) => {
        return new Date(a.date) - new Date(b.date);
      });

      const formatted = [{
        name: 'Daily Readings',
        data: sortedMetrics.map(m => ({
          x: new Date(m.date), // Convert string date to Date object
          y: m.totalReadings || 0 // Use totalReadings instead of readingsCount
        }))
      }];

      console.log('📊 Formatted chart data:', formatted);
      return formatted;
    };

    onMounted(() => {
      console.log('🔄 Overview component mounted');
      loadOverviewData();
    });

    return {
      stats,
      chartData,
      recentAlerts,
      systemHealth,
      isAdmin,
      loading
    };
  }
};
</script>

<style scoped>
.overview-container {
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.charts-container {
  margin-bottom: 30px;
}

.alerts-container {
  margin-bottom: 30px;
}

h3 {
  margin-bottom: 15px;
}
</style>