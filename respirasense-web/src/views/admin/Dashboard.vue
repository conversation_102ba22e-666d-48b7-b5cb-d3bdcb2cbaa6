<template>
  <div class="admin-dashboard">
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1>Admin Dashboard</h1>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <button class="notification-btn">
              <i class="fas fa-bell"></i>
              <span v-if="notifications.length" class="notification-badge">
                {{ notifications.length }}
              </span>
            </button>
            <div class="admin-profile" @click="toggleProfileMenu" ref="profileRef">
              <img :src="adminProfile.photoURL || require('@/assets/DefaultUserIcon.webp')"
                   alt="Admin"
                   class="admin-avatar"
                   @error="handleImageError">
              <span>{{ adminProfile.displayName || 'Admin' }}</span>
              <i class="fas fa-chevron-down ml-2"></i>

              <!-- Profile Dropdown Menu -->
              <div v-if="showProfileMenu" class="profile-dropdown">
                <div class="profile-header">
                  <img :src="adminProfile.photoURL || require('@/assets/DefaultUserIcon.webp')"
                       alt="Admin"
                       class="profile-photo-large"
                       @error="handleImageError">
                  <div class="profile-info">
                    <span class="user-name">{{ adminProfile.displayName || 'Admin' }}</span>
                    <span class="user-email">{{ adminProfile.email }}</span>
                  </div>
                </div>
                <div class="profile-actions">
                  <router-link to="/admin/profile" class="menu-item">
                    <i class="fas fa-user"></i> Manage Account
                  </router-link>
                  <router-link to="/admin/dashboard/settings" class="menu-item">
                    <i class="fas fa-cog"></i> Settings
                  </router-link>
                  <button @click="handleLogout" class="menu-item logout">
                    <i class="fas fa-sign-out-alt"></i> Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <nav class="main-nav">
        <router-link
          v-for="tab in navigationTabs"
          :key="tab.path"
          :to="`/admin/dashboard${tab.path}`"
          class="nav-link"
          :class="{ active: isActiveTab(tab.path) }"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
        </router-link>
      </nav>
    </header>

    <main class="dashboard-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import request from "@/Rest";
import { auth } from "@/plugins/firebase/firebase";

export default {
  name: 'AdminDashboard',

  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const adminProfile = ref({});
    const notifications = ref([]);
    const isLoading = ref(true);
    const error = ref(null);
    const showProfileMenu = ref(false);
    const profileRef = ref(null);

    const navigationTabs = [
      { name: 'Overview', path: '', icon: 'fas fa-chart-line' },
      { name: 'Users', path: '/users', icon: 'fas fa-users' },
      { name: 'Practitioners', path: '/practitioners', icon: 'fas fa-user-md' },
      { name: 'Analytics', path: '/analytics', icon: 'fas fa-chart-bar' },
      { name: 'Settings', path: '/settings', icon: 'fas fa-cog' }
    ];

    const isActiveTab = (tabPath) => {
      if (tabPath === '' && route.path === '/admin/dashboard') {
        return true;
      }
      return route.path === `/admin/dashboard${tabPath}`;
    };

    const handleImageError = (e) => {
      e.target.src = require('@/assets/DefaultUserIcon.webp');
    };

    const toggleProfileMenu = () => {
      showProfileMenu.value = !showProfileMenu.value;
    };

    const handleClickOutside = (event) => {
      if (profileRef.value && !profileRef.value.contains(event.target)) {
        showProfileMenu.value = false;
      }
    };

    const handleLogout = async () => {
      try {
        await auth.signOut();
        router.push('/auth/login');
      } catch (error) {
        console.error('Logout error:', error);
      }
    };

    const fetchAdminProfile = async () => {
      console.log('👤 Fetching admin profile...');
      try {
        const user = auth.currentUser;
        if (!user) {
          console.warn('⚠️ No authenticated user found');
          router.push('/auth/login');
          return;
        }

        console.log('🔍 Getting user role document for:', user.uid);
        const profileDoc = await request.GET(`users_roles/${user.uid}`).Execute();

        if (profileDoc.exists()) {
          const data = profileDoc.data();
          console.log('📄 User role data:', data);

          adminProfile.value = {
            ...data,
            displayName: user.displayName || data.name || 'Admin',
            photoURL: user.photoURL || null
          };
          console.log('✅ Admin profile set:', adminProfile.value);
        } else {
          console.warn('⚠️ No profile document found for user');
        }
      } catch (err) {
        console.error('❌ Error fetching admin profile:', err);
        error.value = 'Failed to load admin profile';
      } finally {
        isLoading.value = false;
        console.log('🔄 Admin profile fetch complete');
      }
    };

    const fetchNotifications = async () => {
      try {
        // This would be replaced with actual notification fetching logic
        notifications.value = [];
      } catch (err) {
        console.error('Error fetching notifications:', err);
      }
    };

    onMounted(() => {
      console.log('🔄 Admin Dashboard mounted');
      fetchAdminProfile();
      fetchNotifications();
      document.addEventListener('click', handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });

    // Debug current route changes
    watch(() => route.path, (newPath) => {
      console.log('🔄 Route changed to:', newPath);
    });

    // Debug notifications
    watch(() => notifications.value, (newNotifications) => {
      console.log('🔔 Notifications updated:', newNotifications);
    });

    return {
      adminProfile,
      notifications,
      isLoading,
      error,
      navigationTabs,
      isActiveTab,
      handleImageError,
      showProfileMenu,
      toggleProfileMenu,
      profileRef,
      handleLogout
    };
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.admin-dashboard {
  min-height: 100vh;
  background-color: $background-app;
}

.dashboard-header {
  background: $white;
  padding: 1rem 2rem;
  box-shadow: $shadow-sm;
  border-bottom: 1px solid rgba($primary-light, 0.1);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h1 {
      color: $primary;
      margin: 0;
      font-size: 1.8rem;
    }
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: $dark-grey;

  .notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: $danger;
    color: $white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  position: relative;

  .admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba($primary-light, 0.2);
  }

  span {
    color: $dark-grey;
  }

  .profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 250px;
    background: $white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
    margin-top: 10px;
    overflow: hidden;

    .profile-header {
      padding: 16px;
      background: rgba($primary-subtle, 0.2);
      display: flex;
      align-items: center;
      gap: 12px;

      .profile-photo-large {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid $white;
      }

      .profile-info {
        display: flex;
        flex-direction: column;

        .user-name {
          font-weight: bold;
          color: $dark-grey;
        }

        .user-email {
          font-size: 0.8rem;
          color: rgba($dark-grey, 0.8);
        }
      }
    }

    .profile-actions {
      padding: 8px 0;

      .menu-item {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        color: $dark-grey;
        text-decoration: none;
        transition: background-color 0.2s;
        cursor: pointer;
        width: 100%;
        text-align: left;
        border: none;
        background: none;
        font-size: 0.9rem;

        &:hover {
          background-color: rgba($primary-subtle, 0.1);
        }

        i {
          margin-right: 10px;
          width: 16px;
          color: $primary;
        }

        &.logout {
          border-top: 1px solid rgba($dark-grey, 0.1);
          margin-top: 8px;
          padding-top: 12px;
          color: $danger;

          i {
            color: $danger;
          }
        }
      }
    }
  }
}

.main-nav {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;

  .nav-link {
    text-decoration: none;
    color: $dark-grey;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;

    &:hover {
      background: rgba($primary-subtle, 0.1);
    }

    &.active {
      color: $primary;
      background: rgba($primary-subtle, 0.2);
      font-weight: 600;
    }

    i {
      margin-right: 0.5rem;
      color: inherit;
    }
  }
}

.dashboard-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
