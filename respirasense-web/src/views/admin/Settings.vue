<template>
  <div class="admin-settings">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-cog"></i>
        <h2>System Settings</h2>
      </div>
      <div class="header-actions">
        <button @click="resetSettings" class="btn-secondary">
          <i class="fas fa-undo"></i> Reset Changes
        </button>
        <button @click="saveSettings" class="btn-primary" :disabled="isSaving">
          <i v-if="isSaving" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-save"></i>
          {{ isSaving ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading settings...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{{ error }}</p>
      <button @click="fetchSettings" class="btn-retry">Retry</button>
    </div>

    <div v-else class="settings-content">
      <!-- System Settings Section -->
      <div class="settings-section">
        <h3>System Configuration</h3>
        <div class="form-group">
          <label for="systemName">System Name</label>
          <input
            id="systemName"
            v-model="settings.system.name"
            type="text"
            placeholder="RespiraSense"
          />
        </div>

        <div class="form-group">
          <label for="timezone">Default Timezone</label>
          <select id="timezone" v-model="settings.system.timezone">
            <option v-for="tz in timezones" :key="tz" :value="tz">{{ tz }}</option>
          </select>
        </div>

        <div class="form-group">
          <label for="sessionTimeout">Session Timeout (minutes)</label>
          <input
            id="sessionTimeout"
            v-model.number="settings.system.sessionTimeout"
            type="number"
            min="5"
            max="120"
          />
          <small>How long until inactive users are automatically logged out</small>
        </div>
      </div>

      <!-- Security Settings Section -->
      <div class="settings-section">
        <h3>Security Settings</h3>
        <div class="form-group">
          <label for="minPasswordLength">Minimum Password Length</label>
          <input
            id="minPasswordLength"
            v-model.number="settings.security.minPasswordLength"
            type="number"
            min="8"
            max="20"
          />
        </div>

        <div class="form-group checkbox-group">
          <input
            id="requireMFA"
            v-model="settings.security.requireMFA"
            type="checkbox"
          />
          <label for="requireMFA">Require Multi-Factor Authentication for Admin Users</label>
        </div>

        <div class="form-group checkbox-group">
          <input
            id="enforcePasswordHistory"
            v-model="settings.security.enforcePasswordHistory"
            type="checkbox"
          />
          <label for="enforcePasswordHistory">Enforce Password History (prevent reuse of recent passwords)</label>
        </div>
      </div>

      <!-- User Management Settings -->
      <div class="settings-section">
        <h3>User Management</h3>
        <div class="form-group checkbox-group">
          <input
            id="autoApprove"
            v-model="settings.users.autoApprove"
            type="checkbox"
          />
          <label for="autoApprove">Auto-approve new practitioner accounts</label>
        </div>

        <div class="form-group">
          <label for="maxLoginAttempts">Max Failed Login Attempts</label>
          <input
            id="maxLoginAttempts"
            v-model.number="settings.users.maxLoginAttempts"
            type="number"
            min="3"
            max="10"
          />
        </div>

        <div class="form-group">
          <label for="lockoutDuration">Account Lockout Duration (minutes)</label>
          <input
            id="lockoutDuration"
            v-model.number="settings.users.lockoutDuration"
            type="number"
            min="5"
            max="1440"
          />
        </div>
      </div>

      <!-- Monitoring Settings -->
      <div class="settings-section">
        <h3>System Monitoring</h3>
        <div class="form-group checkbox-group">
          <input
            id="enableAuditLog"
            v-model="settings.monitoring.enableAuditLog"
            type="checkbox"
          />
          <label for="enableAuditLog">Enable Audit Logging</label>
        </div>

        <div class="form-group">
          <label for="logRetentionDays">Log Retention Period (days)</label>
          <input
            id="logRetentionDays"
            v-model.number="settings.monitoring.logRetentionDays"
            type="number"
            min="30"
            max="365"
          />
        </div>

        <div class="form-group checkbox-group">
          <input
            id="enableErrorReporting"
            v-model="settings.monitoring.enableErrorReporting"
            type="checkbox"
          />
          <label for="enableErrorReporting">Enable Automatic Error Reporting</label>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="settings-section">
        <h3>Notification Settings</h3>
        <div class="form-group checkbox-group">
          <input
            id="enableEmailNotifications"
            v-model="settings.notifications.enableEmailNotifications"
            type="checkbox"
          />
          <label for="enableEmailNotifications">Enable Email Notifications</label>
        </div>

        <div class="form-group checkbox-group">
          <input
            id="alertCriticalReadings"
            v-model="settings.notifications.alertCriticalReadings"
            type="checkbox"
          />
          <label for="alertCriticalReadings">Alert Practitioners on Critical Patient Readings</label>
        </div>

        <div class="form-group checkbox-group">
          <input
            id="dailySummary"
            v-model="settings.notifications.dailySummary"
            type="checkbox"
          />
          <label for="dailySummary">Send Daily Summary to Administrators</label>
        </div>
      </div>
    </div>

    <div class="settings-footer" v-if="!isLoading && !error">
      <button @click="resetSettings" class="btn-secondary">
        <i class="fas fa-undo"></i> Reset Changes
      </button>
      <button @click="saveSettings" class="btn-primary" :disabled="isSaving">
        <i v-if="isSaving" class="fas fa-spinner fa-spin"></i>
        <i v-else class="fas fa-save"></i>
        {{ isSaving ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'AdminSettings',

  setup() {
    const store = useStore();
    const isLoading = ref(true);
    const isSaving = ref(false);
    const error = ref(null);
    const originalSettings = ref(null);

    const timezones = [
      'UTC',
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Paris',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Australia/Sydney',
      'Pacific/Auckland'
    ];

    const settings = reactive({
      system: {
        name: 'RespiraSense',
        timezone: 'UTC',
        sessionTimeout: 30
      },
      security: {
        minPasswordLength: 12,
        requireMFA: true,
        enforcePasswordHistory: true
      },
      users: {
        autoApprove: false,
        maxLoginAttempts: 5,
        lockoutDuration: 30
      },
      monitoring: {
        enableAuditLog: true,
        logRetentionDays: 90,
        enableErrorReporting: true
      },
      notifications: {
        enableEmailNotifications: true,
        alertCriticalReadings: true,
        dailySummary: false
      }
    });

    const fetchSettings = async () => {
      isLoading.value = true;
      error.value = null;

      try {
        const adminSettings = await store.dispatch('admin/fetchSettings');

        if (adminSettings) {
          // Deep copy the settings to originalSettings for reset functionality
          originalSettings.value = JSON.parse(JSON.stringify(adminSettings));

          // Update the reactive settings object with fetched values
          Object.keys(adminSettings).forEach(section => {
            if (settings[section]) {
              Object.keys(adminSettings[section]).forEach(key => {
                if (settings[section][key] !== undefined) {
                  settings[section][key] = adminSettings[section][key];
                }
              });
            }
          });
        } else {
          // If no settings exist yet, use the defaults and save them
          originalSettings.value = JSON.parse(JSON.stringify(settings));
          await store.dispatch('admin/updateSettings', settings);
        }
      } catch (err) {
        console.error('Failed to fetch settings:', err);
        error.value = 'Failed to load settings. Please try again.';
      } finally {
        isLoading.value = false;
      }
    };

    const saveSettings = async () => {
      isSaving.value = true;

      try {
        await store.dispatch('admin/updateSettings', settings);
        originalSettings.value = JSON.parse(JSON.stringify(settings));
        alert('Settings saved successfully!');
      } catch (err) {
        console.error('Failed to save settings:', err);
        alert('Failed to save settings. Please try again.');
      } finally {
        isSaving.value = false;
      }
    };

    const resetSettings = () => {
      if (!originalSettings.value) return;

      // Reset to original values
      Object.keys(originalSettings.value).forEach(section => {
        if (settings[section]) {
          Object.keys(originalSettings.value[section]).forEach(key => {
            if (settings[section][key] !== undefined) {
              settings[section][key] = originalSettings.value[section][key];
            }
          });
        }
      });
    };

    onMounted(() => {
      fetchSettings();
    });

    return {
      isLoading,
      isSaving,
      error,
      settings,
      timezones,
      fetchSettings,
      saveSettings,
      resetSettings
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.admin-settings {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 1.5rem;
      color: $primary;
    }

    h2 {
      margin: 0;
      color: $dark-grey;
      font-size: 1.5rem;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba($primary, 0.2);
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    color: $dark-grey;
    font-size: 0.9rem;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  i {
    font-size: 2rem;
    color: $danger;
    margin-bottom: 16px;
  }

  p {
    color: $dark-grey;
    margin-bottom: 16px;
  }

  .btn-retry {
    padding: 8px 16px;
    background-color: $primary;
    color: $white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}

.settings-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.settings-section {
  background-color: $white;
  border-radius: 8px;
  box-shadow: $shadow-sm;
  padding: 20px;

  h3 {
    margin: 0 0 16px 0;
    color: $primary;
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba($primary-light, 0.1);
  }

  .form-group {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      margin-bottom: 6px;
      color: $dark-grey;
      font-size: 0.9rem;
      font-weight: 500;
    }

    input[type="text"],
    input[type="number"],
    select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid rgba($dark-grey, 0.2);
      border-radius: 4px;
      font-size: 0.9rem;
      transition: border-color 0.2s, box-shadow 0.2s;

      &:focus {
        outline: none;
        border-color: $primary;
        box-shadow: 0 0 0 2px rgba($primary, 0.1);
      }
    }

    small {
      display: block;
      margin-top: 4px;
      color: rgba($dark-grey, 0.6);
      font-size: 0.8rem;
    }

    &.checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;

      input[type="checkbox"] {
        margin: 0;
        width: 16px;
        height: 16px;
      }

      label {
        margin-bottom: 0;
        cursor: pointer;
      }
    }
  }
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba($dark-grey, 0.1);
}

.btn-primary,
.btn-secondary {
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;

  i {
    font-size: 0.9rem;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.btn-primary {
  background-color: $primary;
  border: none;
  color: $white;

  &:hover:not(:disabled) {
    background-color: darken($primary, 10%);
  }
}

.btn-secondary {
  background-color: transparent;
  border: 1px solid rgba($dark-grey, 0.2);
  color: $dark-grey;

  &:hover:not(:disabled) {
    background-color: rgba($dark-grey, 0.05);
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .settings-content {
    grid-template-columns: 1fr;
  }
}
</style>