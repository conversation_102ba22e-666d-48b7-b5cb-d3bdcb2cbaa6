<template>
  <div class="user-management">
    <div class="header">
      <h1>User Management</h1>
      <button @click="showCreateModal = true" class="btn btn-primary">
        <i class="fas fa-user-plus"></i> Create User
      </button>
    </div>

    <div class="filters">
      <div class="search-bar">
        <input
          v-model="filters.search"
          placeholder="Search by name or email..."
          class="search-input"
        />
        <i class="fas fa-search search-icon"></i>
      </div>

      <div class="filter-group">
        <select v-model="filters.role" class="filter-select">
          <option value="">All Roles</option>
          <option v-for="role in availableRoles" :key="role" :value="role">
            {{ formatRole(role) }}
          </option>
        </select>

        <select v-model="filters.status" class="filter-select">
          <option value="">All Statuses</option>
          <option value="active">Active</option>
          <option value="pending">Pending</option>
          <option value="inactive">Inactive</option>
        </select>

        <button @click="resetFilters" class="btn btn-sm btn-secondary">
          <i class="fas fa-undo"></i> Reset
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading users...</p>
    </div>

    <div v-else>
      <table class="users-table">
        <thead>
          <tr>
            <th @click="sortBy('name')">
              Name
              <i :class="getSortIcon('name')"></i>
            </th>
            <th @click="sortBy('email')">
              Email
              <i :class="getSortIcon('email')"></i>
            </th>
            <th @click="sortBy('role')">
              Role
              <i :class="getSortIcon('role')"></i>
            </th>
            <th @click="sortBy('status')">
              Status
              <i :class="getSortIcon('status')"></i>
            </th>
            <th @click="sortBy('createdAt')">
              Created
              <i :class="getSortIcon('createdAt')"></i>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in paginatedUsers" :key="user.id">
            <td>{{ user.name || 'N/A' }}</td>
            <td>{{ user.email }}</td>
            <td>
              <span :class="['role-badge', user.role]">{{ formatRole(user.role) }}</span>
            </td>
            <td>
              <span :class="['status-badge', user.status]">{{ user.status }}</span>
            </td>
            <td>{{ formatDate(user.createdAt) }}</td>
            <td class="actions">
              <button
                @click="viewUserDetails(user)"
                class="btn btn-sm btn-info"
                title="View Details">
                <i class="fas fa-eye"></i>
              </button>
              <button
                @click="editUser(user)"
                class="btn btn-sm btn-warning"
                title="Edit User"
                :disabled="isSuperAdmin(user)">
                <i class="fas fa-edit"></i>
              </button>
              <button
                @click="toggleUserStatus(user)"
                :class="['btn btn-sm', user.status === 'active' ? 'btn-danger' : 'btn-success']"
                :title="user.status === 'active' ? 'Deactivate User' : 'Activate User'"
                :disabled="isSuperAdmin(user)">
                <i :class="['fas', user.status === 'active' ? 'fa-user-slash' : 'fa-user-check']"></i>
              </button>
              <button
                @click="confirmDeleteUser(user)"
                class="btn btn-sm btn-danger"
                title="Delete User"
                :disabled="isSuperAdmin(user)">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="pagination" v-if="totalPages > 1">
        <button
          :disabled="currentPage === 1"
          @click="changePage(currentPage - 1)"
          class="btn btn-sm">
          Previous
        </button>
        <span>Page {{ currentPage }} of {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          @click="changePage(currentPage + 1)"
          class="btn btn-sm">
          Next
        </button>
      </div>
    </div>

    <CreateUserModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @user-created="handleUserCreated"
    />

    <EditUserModal
      v-if="showEditModal"
      :user="selectedUser"
      @close="showEditModal = false"
      @user-updated="handleUserUpdated"
    />

    <UserDetailsModal
      v-if="showDetailsModal"
      :user="selectedUser"
      @close="showDetailsModal = false"
    />

    <!-- Delete User Confirmation Modal -->
    <div v-if="showDeleteConfirmation" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Confirm Delete User</h3>
          <button class="close-btn" @click="cancelDelete">&times;</button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the user <strong>{{ selectedUser?.email }}</strong>?</p>
          <p class="warning-text">This action cannot be undone. The user will be completely removed from the system.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="cancelDelete">Cancel</button>
          <button
            class="btn btn-danger"
            @click="deleteUser"
            :disabled="isDeleting"
          >
            <span v-if="isDeleting">
              <i class="fas fa-spinner fa-spin"></i> Deleting...
            </span>
            <span v-else>Delete User</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import CreateUserModal from './components/CreateUserModal.vue';
import EditUserModal from './components/EditUserModal.vue';
import UserDetailsModal from './components/UserDetailsModal.vue';
import { formatDate } from '@/utils/dateUtils';
import { deleteUser as deleteUserUtil } from '@/utils/user-management';

export default {
  name: 'UserManagement',

  components: {
    CreateUserModal,
    EditUserModal,
    UserDetailsModal
  },

  setup() {
    const store = useStore();
    const loading = ref(false);
    const showCreateModal = ref(false);
    const showEditModal = ref(false);
    const showDetailsModal = ref(false);
    const showDeleteConfirmation = ref(false);
    const isDeleting = ref(false);
    const selectedUser = ref(null);
    const currentPage = ref(1);
    const itemsPerPage = 10;

    const filters = ref({
      search: '',
      role: '',
      status: '',
      dateRange: 'all',
      sortBy: 'createdAt',
      sortDir: 'desc'
    });

    const availableRoles = ['admin', 'practitioner', 'patient', 'superAdmin'];
    const users = ref([]);

    const fetchUsers = async () => {
      loading.value = true;
      try {
        const result = await store.dispatch('admin/fetchUsers');
        users.value = result || [];
      } catch (error) {
        console.error('Failed to fetch users:', error);
      } finally {
        loading.value = false;
      }
    };

    const filteredUsers = computed(() => {
      let result = [...users.value];

      // Apply search filter
      if (filters.value.search) {
        const searchTerm = filters.value.search.toLowerCase();
        result = result.filter(user =>
          user.name?.toLowerCase().includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm)
        );
      }

      // Apply role filter
      if (filters.value.role) {
        result = result.filter(user => user.role === filters.value.role);
      }

      // Apply status filter
      if (filters.value.status) {
        result = result.filter(user => user.status === filters.value.status);
      }

      // Apply sorting
      result.sort((a, b) => {
        const sortBy = filters.value.sortBy;
        const sortDir = filters.value.sortDir === 'asc' ? 1 : -1;

        if (sortBy === 'createdAt') {
          return (a[sortBy]?.seconds - b[sortBy]?.seconds) * sortDir;
        }

        if (!a[sortBy]) return sortDir;
        if (!b[sortBy]) return -sortDir;

        return a[sortBy].localeCompare(b[sortBy]) * sortDir;
      });

      return result;
    });

    const paginatedUsers = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      return filteredUsers.value.slice(start, end);
    });

    const totalPages = computed(() => {
      return Math.ceil(filteredUsers.value.length / itemsPerPage);
    });

    const resetFilters = () => {
      filters.value = {
        search: '',
        role: '',
        status: '',
        dateRange: 'all',
        sortBy: 'createdAt',
        sortDir: 'desc'
      };
      currentPage.value = 1;
    };

    const sortBy = (field) => {
      if (filters.value.sortBy === field) {
        filters.value.sortDir = filters.value.sortDir === 'asc' ? 'desc' : 'asc';
      } else {
        filters.value.sortBy = field;
        filters.value.sortDir = 'asc';
      }
    };

    const getSortIcon = (field) => {
      if (filters.value.sortBy !== field) {
        return 'fas fa-sort';
      }
      return filters.value.sortDir === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    };

    const viewUserDetails = (user) => {
      selectedUser.value = user;
      showDetailsModal.value = true;
    };

    const editUser = (user) => {
      selectedUser.value = user;
      showEditModal.value = true;
    };

    const toggleUserStatus = async (user) => {
      const newStatus = user.status === 'active' ? 'inactive' : 'active';
      try {
        await store.dispatch('admin/updateUserStatus', {
          userId: user.id,
          status: newStatus
        });

        // Update local state
        const index = users.value.findIndex(u => u.id === user.id);
        if (index !== -1) {
          users.value[index].status = newStatus;
        }
      } catch (error) {
        console.error('Failed to update user status:', error);
      }
    };

    const handleUserCreated = () => {
      fetchUsers();
    };

    const handleUserUpdated = () => {
      fetchUsers();
    };

    const confirmDeleteUser = (user) => {
      selectedUser.value = user;
      showDeleteConfirmation.value = true;
    };

    const cancelDelete = () => {
      showDeleteConfirmation.value = false;
      selectedUser.value = null;
    };

    const deleteUser = async () => {
      if (!selectedUser.value) return;

      isDeleting.value = true;
      try {
        // Call the enhanced deleteUser utility function
        // Pass true for isAdmin parameter since this is an admin operation
        await deleteUserUtil(selectedUser.value.id, true);

        // Remove the user from the local state
        users.value = users.value.filter(u => u.id !== selectedUser.value.id);

        // Close the confirmation modal
        showDeleteConfirmation.value = false;
        selectedUser.value = null;
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert(`Failed to delete user: ${error.message || 'Unknown error'}`);
      } finally {
        isDeleting.value = false;
      }
    };

    const changePage = (page) => {
      currentPage.value = page;
    };

    const formatRole = (role) => {
      return role.charAt(0).toUpperCase() + role.slice(1);
    };

    const isSuperAdmin = (user) => {
      return user.role === 'superAdmin';
    };

    watch(filters, () => {
      currentPage.value = 1;
    }, { deep: true });

    onMounted(fetchUsers);

    return {
      loading,
      users,
      filteredUsers,
      paginatedUsers,
      filters,
      showCreateModal,
      showEditModal,
      showDetailsModal,
      showDeleteConfirmation,
      isDeleting,
      selectedUser,
      currentPage,
      totalPages,
      availableRoles,
      resetFilters,
      sortBy,
      getSortIcon,
      viewUserDetails,
      editUser,
      toggleUserStatus,
      confirmDeleteUser,
      cancelDelete,
      deleteUser,
      handleUserCreated,
      handleUserUpdated,
      changePage,
      formatRole,
      isSuperAdmin,
      formatDate
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';
.user-management {
  padding: 20px;
  background-color: #f8f9fc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      font-size: 24px;
      color: #333;
      margin: 0;
    }

    .btn-primary {
      background-color: $primary;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: darken($primary, 10%);
      }
    }
  }

  .filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;

    .search-bar {
      position: relative;
      flex: 1;
      min-width: 200px;

      .search-input {
        width: 100%;
        padding: 8px 12px 8px 36px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
      }
    }

    .filter-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;

      .filter-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        font-size: 14px;
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: darken(#6c757d, 10%);
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid $primary;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  .users-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;

    th, td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
      cursor: pointer;
      user-select: none;
      white-space: nowrap;

      i {
        margin-left: 5px;
      }
    }

    tr:last-child td {
      border-bottom: none;
    }

    .role-badge, .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;
    }

    .role-badge {
      &.admin, &.superAdmin {
        background-color: #cce5ff;
        color: #004085;
      }

      &.practitioner {
        background-color: #d1ecf1;
        color: #0c5460;
      }

      &.patient {
        background-color: #d4edda;
        color: #155724;
      }
    }

    .status-badge {
      &.active {
        background-color: #d4edda;
        color: #155724;
      }

      &.pending {
        background-color: #fff3cd;
        color: #856404;
      }

      &.inactive {
        background-color: #f8d7da;
        color: #721c24;
      }
    }

    .actions {
      display: flex;
      gap: 8px;
      white-space: nowrap;

      .btn-sm {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: background-color 0.3s;

        &.btn-info {
          background-color: #17a2b8;
          color: white;

          &:hover {
            background-color: darken(#17a2b8, 10%);
          }
        }

        &.btn-warning {
          background-color: #ffc107;
          color: #212529;

          &:hover {
            background-color: darken(#ffc107, 10%);
          }
        }

        &.btn-danger {
          background-color: #dc3545;
          color: white;

          &:hover {
            background-color: darken(#dc3545, 10%);
          }
        }

        &.btn-success {
          background-color: #28a745;
          color: white;

          &:hover {
            background-color: darken(#28a745, 10%);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 20px;

    .btn-sm {
      padding: 6px 12px;
      border-radius: 4px;
      background-color: #6c757d;
      color: white;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover:not(:disabled) {
        background-color: darken(#6c757d, 10%);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #666;

      &:hover {
        color: #333;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    .warning-text {
      color: $danger;
      font-weight: 500;
      margin-top: 1rem;
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #eee;

    .btn {
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      border: none;
      font-weight: 500;

      &.btn-secondary {
        background-color: #6c757d;
        color: white;

        &:hover {
          background-color: darken(#6c757d, 10%);
        }
      }

      &.btn-danger {
        background-color: $danger;
        color: white;

        &:hover {
          background-color: darken($danger, 10%);
        }

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
