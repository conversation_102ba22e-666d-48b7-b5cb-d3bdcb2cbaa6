<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>User Details</h3>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <div class="user-info">
          <div class="info-group">
            <label>Name</label>
            <p>{{ user.name || 'N/A' }}</p>
          </div>
          <div class="info-group">
            <label>Email</label>
            <p>{{ user.email }}</p>
          </div>
          <div class="info-group">
            <label>Role</label>
            <p><span :class="['role-badge', user.role]">{{ user.role }}</span></p>
          </div>
          <div class="info-group">
            <label>Status</label>
            <p><span :class="['status-badge', user.status]">{{ user.status }}</span></p>
          </div>
          <div class="info-group">
            <label>Created At</label>
            <p>{{ formatDate(user.createdAt) }}</p>
          </div>
          <div class="info-group">
            <label>Last Login</label>
            <p>{{ user.lastLogin ? getTimeAgo(user.lastLogin) : 'Never' }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="$emit('close')">Close</button>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, getTimeAgo } from '@/utils/dateUtils';

export default {
  name: 'UserDetailsModal',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  emits: ['close'],
  
  setup() {
    return {
      formatDate,
      getTimeAgo
    };
  }
};
</script>

<style scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.info-group {
  margin-bottom: 1rem;
}

.info-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.25rem;
  color: #666;
}

.info-group p {
  margin: 0;
}

.role-badge, .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9em;
}

.role-badge.admin { background: #e3f2fd; color: #1976d2; }
.role-badge.practitioner { background: #e8f5e9; color: #2e7d32; }
.role-badge.patient { background: #fff3e0; color: #f57c00; }

.status-badge.active { background: #e8f5e9; color: #2e7d32; }
.status-badge.inactive { background: #ffebee; color: #c62828; }

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
</style>