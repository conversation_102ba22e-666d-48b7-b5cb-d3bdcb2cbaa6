<template>
  <div class="pending-registrations">
    <div class="section-header">
      <h2>Pending Registrations</h2>
      <button @click="refreshData" class="refresh-btn">
        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
      </button>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading pending registrations...</p>
    </div>

    <div v-else-if="pendingUsers.length === 0" class="empty-state">
      <i class="fas fa-check-circle"></i>
      <p>No pending registrations to approve</p>
    </div>

    <div v-else class="registrations-list">
      <div v-for="user in pendingUsers" :key="user.id" class="registration-card">
        <div class="user-info">
          <div class="user-avatar">
            <i :class="getRoleIcon(user.role)"></i>
          </div>
          <div class="user-details">
            <h3>{{ user.name }}</h3>
            <p class="user-email">{{ user.email }}</p>
            <p class="user-role">
              <span class="role-badge" :class="getRoleBadgeClass(user.role)">
                {{ formatRole(user.role) }}
              </span>
              <span class="registration-date">
                Registered {{ formatDate(user.createdAt) }}
              </span>
            </p>
          </div>
        </div>

        <!-- Practitioner-specific information -->
        <div v-if="user.role === 'practitioner'" class="practitioner-info">
          <div class="info-row">
            <span class="info-label">Specialty:</span>
            <span class="info-value">{{ user.specialty || 'Not specified' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">License Number:</span>
            <span class="info-value">{{ user.licenseNumber || 'Not specified' }}</span>
          </div>
        </div>

        <!-- Patient-specific information -->
        <div v-if="user.role === 'patient'" class="patient-info">
          <div class="info-row">
            <span class="info-label">Age:</span>
            <span class="info-value">{{ user.age || 'Not specified' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Gender:</span>
            <span class="info-value">{{ user.gender || 'Not specified' }}</span>
          </div>
        </div>

        <div class="action-buttons">
          <button 
            @click="approveUser(user.id)" 
            class="approve-btn"
            :disabled="processingId === user.id"
          >
            <i class="fas fa-check"></i> Approve
          </button>
          <button 
            @click="rejectUser(user.id)" 
            class="reject-btn"
            :disabled="processingId === user.id"
          >
            <i class="fas fa-times"></i> Reject
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { notify } from '@/utils/notifications';

export default {
  name: 'PendingRegistrations',
  
  setup() {
    const store = useStore();
    const pendingUsers = ref([]);
    const loading = ref(true);
    const processingId = ref(null);
    
    const fetchPendingRegistrations = async () => {
      loading.value = true;
      try {
        pendingUsers.value = await store.dispatch('admin/fetchPendingRegistrations');
      } catch (error) {
        console.error('Error fetching pending registrations:', error);
        notify({
          type: 'error',
          text: 'Failed to load pending registrations'
        });
      } finally {
        loading.value = false;
      }
    };
    
    const approveUser = async (userId) => {
      processingId.value = userId;
      try {
        await store.dispatch('admin/approveRegistration', userId);
        
        // Remove the user from the list
        pendingUsers.value = pendingUsers.value.filter(user => user.id !== userId);
        
        notify({
          type: 'success',
          text: 'User registration approved successfully'
        });
      } catch (error) {
        console.error('Error approving user:', error);
        notify({
          type: 'error',
          text: 'Failed to approve user registration'
        });
      } finally {
        processingId.value = null;
      }
    };
    
    const rejectUser = async (userId) => {
      processingId.value = userId;
      try {
        await store.dispatch('admin/rejectRegistration', userId);
        
        // Remove the user from the list
        pendingUsers.value = pendingUsers.value.filter(user => user.id !== userId);
        
        notify({
          type: 'success',
          text: 'User registration rejected'
        });
      } catch (error) {
        console.error('Error rejecting user:', error);
        notify({
          type: 'error',
          text: 'Failed to reject user registration'
        });
      } finally {
        processingId.value = null;
      }
    };
    
    const refreshData = () => {
      fetchPendingRegistrations();
    };
    
    const formatDate = (date) => {
      if (!date) return 'Unknown';
      
      const now = new Date();
      const diff = now - new Date(date);
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (days === 0) return 'Today';
      if (days === 1) return 'Yesterday';
      if (days < 7) return `${days} days ago`;
      
      return new Date(date).toLocaleDateString();
    };
    
    const getRoleIcon = (role) => {
      switch (role) {
        case 'patient': return 'fas fa-user';
        case 'practitioner': return 'fas fa-user-md';
        case 'admin': return 'fas fa-user-shield';
        default: return 'fas fa-user';
      }
    };
    
    const getRoleBadgeClass = (role) => {
      switch (role) {
        case 'patient': return 'role-patient';
        case 'practitioner': return 'role-practitioner';
        case 'admin': return 'role-admin';
        default: return '';
      }
    };
    
    const formatRole = (role) => {
      if (!role) return 'Unknown';
      return role.charAt(0).toUpperCase() + role.slice(1);
    };
    
    onMounted(() => {
      fetchPendingRegistrations();
    });
    
    return {
      pendingUsers,
      loading,
      processingId,
      approveUser,
      rejectUser,
      refreshData,
      formatDate,
      getRoleIcon,
      getRoleBadgeClass,
      formatRole
    };
  }
};
</script>

<style lang="scss" scoped>
.pending-registrations {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #333;
  }
}

.refresh-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 1rem;
  
  &:hover {
    color: darken(#dc3545, 10%);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  
  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #dc3545;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #666;
  
  i {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 15px;
  }
}

.registrations-list {
  display: grid;
  gap: 15px;
}

.registration-card {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }
}

.user-info {
  display: flex;
  margin-bottom: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  
  i {
    font-size: 1.5rem;
    color: #dc3545;
  }
}

.user-details {
  flex: 1;
  
  h3 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
  }
  
  .user-email {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 0.9rem;
  }
}

.role-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-right: 10px;
  
  &.role-patient {
    background-color: #e6f7ff;
    color: #0066cc;
  }
  
  &.role-practitioner {
    background-color: #f0f7ed;
    color: #52c41a;
  }
  
  &.role-admin {
    background-color: #fff2e8;
    color: #fa541c;
  }
}

.registration-date {
  font-size: 0.8rem;
  color: #999;
}

.practitioner-info, .patient-info {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 5px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .info-label {
    font-weight: 500;
    width: 120px;
    color: #666;
  }
  
  .info-value {
    flex: 1;
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  
  button {
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .approve-btn {
    background-color: #28a745;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: darken(#28a745, 10%);
    }
  }
  
  .reject-btn {
    background-color: #dc3545;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: darken(#dc3545, 10%);
    }
  }
}
</style>
