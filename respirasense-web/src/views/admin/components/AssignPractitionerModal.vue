<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Assign Practitioner to Patient</h3>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <div v-if="loading" class="loading-container">
          <div class="spinner"></div>
          <p>Loading practitioners...</p>
        </div>
        <form v-else @submit.prevent="handleSubmit">
          <div class="form-group">
            <label>Patient</label>
            <input :value="patient.name || patient.email" type="text" disabled>
          </div>
          
          <div class="form-group">
            <label>Select Practitioner</label>
            <select v-model="selectedPractitionerId" required>
              <option value="">-- Select a practitioner --</option>
              <option v-for="practitioner in practitioners" :key="practitioner.id" :value="practitioner.id">
                {{ practitioner.name || practitioner.email }}
              </option>
            </select>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" @click="$emit('close')">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              {{ isSubmitting ? 'Assigning...' : 'Assign Practitioner' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';
import { assignPatientToPractitioner } from '@/utils/firestore-helpers';

export default {
  name: 'AssignPractitionerModal',
  props: {
    patient: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'assignment-updated'],
  
  setup(props, { emit }) {
    const store = useStore();
    const loading = ref(true);
    const isSubmitting = ref(false);
    const practitioners = ref([]);
    const selectedPractitionerId = ref('');
    
    // Fetch all practitioners
    const fetchPractitioners = async () => {
      loading.value = true;
      try {
        const practitionersRef = collection(db, 'users_roles');
        const q = query(practitionersRef, where('role', '==', 'practitioner'));
        const querySnapshot = await getDocs(q);
        
        practitioners.value = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      } catch (error) {
        console.error('Failed to fetch practitioners:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // Handle form submission
    const handleSubmit = async () => {
      if (!selectedPractitionerId.value) return;
      
      isSubmitting.value = true;
      try {
        // Assign the practitioner to the patient
        await assignPatientToPractitioner(props.patient.id, selectedPractitionerId.value);
        
        // Update the users_roles document to include practitionerId
        await store.dispatch('admin/updateUser', {
          userId: props.patient.id,
          practitionerId: selectedPractitionerId.value
        });
        
        emit('assignment-updated');
        emit('close');
      } catch (error) {
        console.error('Failed to assign practitioner:', error);
        alert('Failed to assign practitioner: ' + error.message);
      } finally {
        isSubmitting.value = false;
      }
    };
    
    onMounted(fetchPractitioners);
    
    return {
      loading,
      isSubmitting,
      practitioners,
      selectedPractitionerId,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #e74c3c;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group input:disabled {
  background: #f5f5f5;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: #e74c3c;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>
