<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit User</h3>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label>Name</label>
            <input v-model="form.name" type="text" required>
          </div>
          <div class="form-group">
            <label>Email</label>
            <input v-model="form.email" type="email" required disabled>
          </div>
          <div class="form-group">
            <label>Role</label>
            <select v-model="form.role" required>
              <option value="patient">Patient</option>
              <option value="practitioner">Practitioner</option>
              <option value="admin">Admin</option>
            </select>
          </div>
          <div class="form-group">
            <label>New Password (leave blank to keep current)</label>
            <input v-model="form.password" type="password" placeholder="New password">
            <small class="form-hint">For testing purposes only. Will be replaced with a more secure process later.</small>
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" @click="$emit('close')">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              {{ loading ? 'Saving...' : 'Save Changes' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'EditUserModal',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'user-updated'],

  setup(props, { emit }) {
    const store = useStore();
    const loading = ref(false);
    const form = ref({
      name: '',
      email: '',
      role: '',
      password: ''
    });

    onMounted(() => {
      form.value = { ...props.user };
    });

    const handleSubmit = async () => {
      loading.value = true;
      try {
        // Create a copy of the form data without the password
        const userData = { ...form.value };
        delete userData.password;

        // Update user data in Firestore
        await store.dispatch('admin/updateUser', {
          userId: props.user.id,
          ...userData
        });

        // If a new password was provided, update it
        if (form.value.password) {
          try {
            // For testing purposes, we'll use a direct approach to update the password
            // In a production environment, this should be done through a more secure process

            // In a real implementation, we would use Firebase Auth to update the password
            // For testing purposes, we're just logging it

            // Update the password
            // Note: This is a simplified approach for testing purposes
            // In a real application, you would need proper authentication and security measures

            // This is a placeholder for a more secure implementation
            console.log(`Password for user ${props.user.id} would be updated to: ${form.value.password}`);

            // Clear the password field
            form.value.password = '';
          } catch (passwordError) {
            console.error('Failed to update password:', passwordError);
            alert('User data updated, but password update failed: ' + passwordError.message);
          }
        }

        emit('user-updated');
        emit('close');
      } catch (error) {
        console.error('Failed to update user:', error);
        alert('Failed to update user: ' + error.message);
      } finally {
        loading.value = false;
      }
    };

    return {
      form,
      loading,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group input:disabled {
  background: #f5f5f5;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.form-hint {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}
</style>