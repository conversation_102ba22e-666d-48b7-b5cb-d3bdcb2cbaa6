<template>
  <div class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Create New User</h2>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label>Email <span class="required">*</span></label>
            <input v-model="form.email" required type="email" placeholder="Email">
          </div>

          <div class="form-group">
            <label>Role <span class="required">*</span></label>
            <select v-model="form.role" required @change="handleRoleChange">
              <option value="patient">Patient</option>
              <option value="practitioner">Practitioner</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div class="form-group">
            <label>Name <span class="required">*</span></label>
            <input v-model="form.name" required type="text" placeholder="Full Name">
          </div>

          <div class="form-group">
            <label>Password <span class="required">*</span></label>
            <input v-model="form.password" required type="password" placeholder="Password">
            <small class="form-hint">For testing purposes only. Will be replaced with a more secure process later.</small>
          </div>

          <!-- Practitioner-specific fields -->
          <div v-if="form.role === 'practitioner'" class="practitioner-fields">
            <div class="form-group">
              <label>Specialty</label>
              <select v-model="form.specialty">
                <option value="">Select specialty</option>
                <option value="General Practitioner">General Practitioner</option>
                <option value="Pulmonologist">Pulmonologist</option>
                <option value="Cardiologist">Cardiologist</option>
                <option value="Internal Medicine">Internal Medicine</option>
                <option value="Emergency Medicine">Emergency Medicine</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div v-if="form.specialty === 'Other'" class="form-group">
              <label>Other Specialty</label>
              <input v-model="form.otherSpecialty" type="text" placeholder="Enter specialty">
            </div>

            <div class="form-group">
              <label>License Number</label>
              <input v-model="form.licenseNumber" type="text" placeholder="License Number">
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="form.sendWelcomeEmail">
              Send welcome email with password reset link
            </label>
          </div>

          <div class="modal-actions">
            <button type="button" @click="$emit('close')" class="btn btn-secondary">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              {{ loading ? 'Creating...' : 'Create User' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useStore } from 'vuex';
import { createUserWithEmailAndPassword, sendPasswordResetEmail, auth } from '@/plugins/firebase/firebase';
import { doc, setDoc, collection, writeBatch, serverTimestamp } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';

export default {
  name: 'CreateUserModal',
  emits: ['close', 'user-created'],

  setup(props, { emit }) {
    const store = useStore();
    const loading = ref(false);
    const form = ref({
      email: '',
      name: '',
      password: '',
      role: 'patient',
      specialty: '',
      otherSpecialty: '',
      licenseNumber: '',
      status: 'pending',
      sendWelcomeEmail: true
    });

    const handleRoleChange = () => {
      // Reset role-specific fields when role changes
      if (form.value.role !== 'practitioner') {
        form.value.specialty = '';
        form.value.otherSpecialty = '';
        form.value.licenseNumber = '';
      }
    };

    const handleSubmit = async () => {
      loading.value = true;
      try {
        // Use the provided password instead of generating a random one
        const password = form.value.password;

        // Create the user in Firebase Auth
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          form.value.email,
          password
        );

        const userId = userCredential.user.uid;
        const now = new Date().toISOString();
        const batch = writeBatch(db);

        // Determine specialty for practitioners
        const specialty = form.value.role === 'practitioner' && form.value.specialty === 'Other'
          ? form.value.otherSpecialty
          : form.value.specialty;

        // Create base user document in users_roles collection
        const userRoleRef = doc(db, 'users_roles', userId);
        const userData = {
          email: form.value.email,
          name: form.value.name,
          role: form.value.role,
          status: form.value.status,
          created_at: now,
          updatedAt: now,
          last_login: now,
          provider: 'password'
        };

        // Add role-specific fields
        if (form.value.role === 'practitioner') {
          userData.specialty = specialty || null;
          userData.licenseNumber = form.value.licenseNumber || null;
          userData.patientCount = 0;

          // Create practitioner document
          const practitionerRef = doc(db, 'practitioners', userId);
          batch.set(practitionerRef, {
            name: form.value.name,
            email: form.value.email,
            specialty: specialty || null,
            licenseNumber: form.value.licenseNumber || null,
            status: form.value.status,
            createdAt: now,
            lastUpdate: now,
            patientCount: 0
          });
        } else if (form.value.role === 'patient') {
          // Create patient document
          const patientRef = doc(db, 'patients', userId);
          batch.set(patientRef, {
            name: form.value.name,
            email: form.value.email,
            createdAt: now,
            lastUpdate: now
          });
        }

        // Set the user_roles document
        batch.set(userRoleRef, userData);

        // Commit all the writes
        await batch.commit();

        // Send password reset email if requested
        // For testing purposes, we'll skip this since we're setting the password directly
        if (form.value.sendWelcomeEmail && false) { // Disabled for now
          await sendPasswordResetEmail(auth, form.value.email);
        }

        // Create audit log
        try {
          const auditLogRef = doc(collection(db, 'audit_logs'));
          await setDoc(auditLogRef, {
            action: `create_${form.value.role}`,
            userId: userId,
            performedBy: store.state.auth.user.uid,
            timestamp: serverTimestamp(),
            details: {
              email: form.value.email,
              name: form.value.name,
              role: form.value.role
            }
          });
        } catch (auditError) {
          console.error('Failed to create audit log:', auditError);
          // Continue even if audit log fails
        }

        emit('user-created');
        emit('close');
      } catch (error) {
        console.error('Failed to create user:', error);
        alert(error.message);
      } finally {
        loading.value = false;
      }
    };

    return {
      form,
      loading,
      handleRoleChange,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.required {
  color: #e74c3c;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input {
  width: auto;
}

.practitioner-fields {
  border-left: 3px solid #e74c3c;
  padding-left: 15px;
  margin: 15px 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  font-weight: 500;
}

.btn-primary {
  background-color: #e74c3c;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.form-hint {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}
</style>