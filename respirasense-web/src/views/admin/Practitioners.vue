<template>
  <div class="practitioners-management">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-user-md"></i>
        <h2>Practitioner Management</h2>
      </div>
      <button @click="showCreateModal = true" class="btn-primary">
        <i class="fas fa-user-plus"></i> Onboard New Practitioner
      </button>
    </div>

    <div class="filters-section">
      <div class="search-bar">
        <input 
          v-model="filters.search" 
          placeholder="Search practitioners..." 
          class="search-input"
        />
        <i class="fas fa-search search-icon"></i>
      </div>
      
      <div class="filter-controls">
        <div class="filter-group">
          <label>Status:</label>
          <select v-model="filters.status">
            <option value="">All</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label>Sort by:</label>
          <select v-model="filters.sortBy">
            <option value="name">Name</option>
            <option value="created_at">Date Added</option>
            <option value="last_login">Last Login</option>
            <option value="patients">Patient Count</option>
          </select>
        </div>
        
        <button @click="resetFilters" class="btn-reset">
          <i class="fas fa-redo"></i> Reset
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading practitioners...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{{ error }}</p>
      <button @click="fetchPractitioners" class="btn-retry">Retry</button>
    </div>

    <div v-else-if="!filteredPractitioners.length" class="empty-state">
      <i class="fas fa-user-md empty-icon"></i>
      <h3>No practitioners found</h3>
      <p v-if="hasFilters">Try adjusting your filters or search terms.</p>
      <p v-else>Get started by onboarding your first practitioner.</p>
      <button @click="showCreateModal = true" class="btn-primary">
        <i class="fas fa-user-plus"></i> Onboard New Practitioner
      </button>
    </div>

    <div v-else class="practitioners-list">
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Specialty</th>
            <th>Patients</th>
            <th>Status</th>
            <th>Last Login</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="practitioner in filteredPractitioners" :key="practitioner.id">
            <td class="practitioner-name">
              <img 
                :src="practitioner.photoURL || require('@/assets/DefaultUserIcon.webp')" 
                alt="Profile" 
                class="practitioner-avatar"
                @error="handleImageError"
              />
              <span>{{ practitioner.name || practitioner.displayName || 'Unnamed' }}</span>
            </td>
            <td>{{ practitioner.email }}</td>
            <td>{{ practitioner.specialty || 'General' }}</td>
            <td>{{ practitioner.patientCount || 0 }}</td>
            <td>
              <span :class="['status-badge', practitioner.status]">
                {{ practitioner.status }}
              </span>
            </td>
            <td>{{ formatDate(practitioner.last_login) }}</td>
            <td class="actions">
              <button 
                @click="viewPractitionerDetails(practitioner)" 
                class="btn-action"
                title="View Details">
                <i class="fas fa-eye"></i>
              </button>
              <button 
                @click="editPractitioner(practitioner)"
                class="btn-action"
                title="Edit Practitioner">
                <i class="fas fa-edit"></i>
              </button>
              <button 
                @click="togglePractitionerStatus(practitioner)"
                :class="['btn-action', practitioner.status === 'active' ? 'deactivate' : 'activate']"
                :title="practitioner.status === 'active' ? 'Deactivate Practitioner' : 'Activate Practitioner'">
                <i :class="['fas', practitioner.status === 'active' ? 'fa-user-slash' : 'fa-user-check']"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="pagination" v-if="totalPages > 1">
        <button 
          :disabled="currentPage === 1"
          @click="changePage(currentPage - 1)"
          class="btn-page">
          <i class="fas fa-chevron-left"></i> Previous
        </button>
        <div class="page-numbers">
          <button 
            v-for="page in displayedPages" 
            :key="page" 
            @click="changePage(page)"
            :class="['btn-page-number', { active: currentPage === page }]">
            {{ page }}
          </button>
        </div>
        <button 
          :disabled="currentPage === totalPages"
          @click="changePage(currentPage + 1)"
          class="btn-page">
          Next <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Create Practitioner Modal -->
    <OnboardPractitionerModal 
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @practitioner-created="handlePractitionerCreated"
    />

    <!-- Edit Practitioner Modal -->
    <EditPractitionerModal
      v-if="showEditModal"
      :practitioner="selectedPractitioner"
      @close="showEditModal = false"
      @practitioner-updated="handlePractitionerUpdated"
    />

    <!-- Practitioner Details Modal -->
    <PractitionerDetailsModal
      v-if="showDetailsModal"
      :practitioner="selectedPractitioner"
      @close="showDetailsModal = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/dateFormatter';
import OnboardPractitionerModal from '@/components/admin/OnboardPractitionerModal.vue';
import EditPractitionerModal from '@/components/admin/EditPractitionerModal.vue';
import PractitionerDetailsModal from '@/components/admin/PractitionerDetailsModal.vue';

export default {
  name: 'PractitionersManagement',
  
  components: {
    OnboardPractitionerModal,
    EditPractitionerModal,
    PractitionerDetailsModal
  },
  
  setup() {
    const store = useStore();
    const practitioners = ref([]);
    const isLoading = ref(true);
    const error = ref(null);
    const showCreateModal = ref(false);
    const showEditModal = ref(false);
    const showDetailsModal = ref(false);
    const selectedPractitioner = ref(null);
    const currentPage = ref(1);
    const itemsPerPage = 10;
    
    const filters = ref({
      search: '',
      status: '',
      sortBy: 'created_at',
      sortDir: 'desc'
    });

    // Computed properties
    const hasFilters = computed(() => {
      return filters.value.search || filters.value.status;
    });

    const filteredPractitioners = computed(() => {
      let result = [...practitioners.value];
      
      // Apply search filter
      if (filters.value.search) {
        const searchTerm = filters.value.search.toLowerCase();
        result = result.filter(practitioner => 
          (practitioner.name && practitioner.name.toLowerCase().includes(searchTerm)) ||
          (practitioner.email && practitioner.email.toLowerCase().includes(searchTerm)) ||
          (practitioner.specialty && practitioner.specialty.toLowerCase().includes(searchTerm))
        );
      }
      
      // Apply status filter
      if (filters.value.status) {
        result = result.filter(practitioner => 
          practitioner.status === filters.value.status
        );
      }
      
      // Apply sorting
      result.sort((a, b) => {
        let fieldA = a[filters.value.sortBy];
        let fieldB = b[filters.value.sortBy];
        
        // Handle dates
        if (filters.value.sortBy === 'created_at' || filters.value.sortBy === 'last_login') {
          fieldA = new Date(fieldA || 0);
          fieldB = new Date(fieldB || 0);
        }
        
        // Handle numbers
        if (filters.value.sortBy === 'patientCount') {
          fieldA = fieldA || 0;
          fieldB = fieldB || 0;
        }
        
        if (fieldA < fieldB) return filters.value.sortDir === 'asc' ? -1 : 1;
        if (fieldA > fieldB) return filters.value.sortDir === 'asc' ? 1 : -1;
        return 0;
      });
      
      return result;
    });

    const paginatedPractitioners = computed(() => {
      const startIndex = (currentPage.value - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      return filteredPractitioners.value.slice(startIndex, endIndex);
    });

    const totalPages = computed(() => {
      return Math.ceil(filteredPractitioners.value.length / itemsPerPage);
    });

    const displayedPages = computed(() => {
      const pages = [];
      const maxVisiblePages = 5;
      
      if (totalPages.value <= maxVisiblePages) {
        for (let i = 1; i <= totalPages.value; i++) {
          pages.push(i);
        }
      } else {
        let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage < maxVisiblePages - 1) {
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
          pages.push(i);
        }
      }
      
      return pages;
    });

    // Methods
    const fetchPractitioners = async () => {
      isLoading.value = true;
      error.value = null;
      
      try {
        const result = await store.dispatch('admin/fetchPractitioners');
        practitioners.value = result;
      } catch (err) {
        console.error('Failed to fetch practitioners:', err);
        error.value = 'Failed to load practitioners. Please try again.';
      } finally {
        isLoading.value = false;
      }
    };

    const resetFilters = () => {
      filters.value = {
        search: '',
        status: '',
        sortBy: 'created_at',
        sortDir: 'desc'
      };
      currentPage.value = 1;
    };

    const changePage = (page) => {
      currentPage.value = page;
    };

    const handleImageError = (e) => {
      e.target.src = require('@/assets/DefaultUserIcon.webp');
    };

    const viewPractitionerDetails = (practitioner) => {
      selectedPractitioner.value = practitioner;
      showDetailsModal.value = true;
    };

    const editPractitioner = (practitioner) => {
      selectedPractitioner.value = practitioner;
      showEditModal.value = true;
    };

    const togglePractitionerStatus = async (practitioner) => {
      try {
        const newStatus = practitioner.status === 'active' ? 'inactive' : 'active';
        await store.dispatch('admin/updatePractitionerStatus', {
          practitionerId: practitioner.id,
          status: newStatus
        });
        
        // Update local state
        const index = practitioners.value.findIndex(p => p.id === practitioner.id);
        if (index !== -1) {
          practitioners.value[index].status = newStatus;
        }
      } catch (err) {
        console.error('Failed to update practitioner status:', err);
        // Show error notification
      }
    };

    const handlePractitionerCreated = () => {
      fetchPractitioners();
      showCreateModal.value = false;
    };

    const handlePractitionerUpdated = () => {
      fetchPractitioners();
      showEditModal.value = false;
    };

    // Watch for filter changes to reset pagination
    watch([() => filters.value.search, () => filters.value.status, () => filters.value.sortBy], () => {
      currentPage.value = 1;
    });

    onMounted(() => {
      fetchPractitioners();
    });

    return {
      practitioners: paginatedPractitioners,
      filteredPractitioners: paginatedPractitioners,
      isLoading,
      error,
      filters,
      hasFilters,
      currentPage,
      totalPages,
      displayedPages,
      showCreateModal,
      showEditModal,
      showDetailsModal,
      selectedPractitioner,
      fetchPractitioners,
      resetFilters,
      changePage,
      handleImageError,
      viewPractitionerDetails,
      editPractitioner,
      togglePractitionerStatus,
      handlePractitionerCreated,
      handlePractitionerUpdated,
      formatDate
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.practitioners-management {
  padding: 20px;
  background-color: $white;
  border-radius: 8px;
  box-shadow: $shadow-sm;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    
    i {
      font-size: 1.5rem;
      color: $primary;
    }
    
    h2 {
      margin: 0;
      color: $dark-grey;
      font-size: 1.5rem;
    }
  }
  
  .btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    
    i {
      font-size: 0.9rem;
    }
  }
}

.filters-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: rgba($primary-subtle, 0.05);
  border-radius: 8px;
  
  .search-bar {
    position: relative;
    margin-bottom: 16px;
    
    .search-input {
      width: 100%;
      padding: 10px 16px 10px 40px;
      border: 1px solid rgba($dark-grey, 0.2);
      border-radius: 4px;
      font-size: 0.9rem;
      
      &:focus {
        outline: none;
        border-color: $primary;
        box-shadow: 0 0 0 2px rgba($primary, 0.1);
      }
    }
    
    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba($dark-grey, 0.5);
    }
  }
  
  .filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .filter-group {
      display: flex;
      align-items: center;
      gap: 8px;
      
      label {
        font-size: 0.9rem;
        color: $dark-grey;
        font-weight: 500;
      }
      
      select {
        padding: 8px 12px;
        border: 1px solid rgba($dark-grey, 0.2);
        border-radius: 4px;
        background-color: $white;
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: $primary;
        }
      }
    }
    
    .btn-reset {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      background-color: transparent;
      border: 1px solid rgba($dark-grey, 0.2);
      border-radius: 4px;
      color: $dark-grey;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background-color: rgba($dark-grey, 0.05);
      }
      
      i {
        font-size: 0.8rem;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba($primary, 0.2);
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  p {
    color: $dark-grey;
    font-size: 0.9rem;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  
  i {
    font-size: 2rem;
    color: $danger;
    margin-bottom: 16px;
  }
  
  p {
    color: $dark-grey;
    margin-bottom: 16px;
  }
  
  .btn-retry {
    padding: 8px 16px;
    background-color: $primary;
    color: $white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 3rem;
    color: rgba($dark-grey, 0.2);
    margin-bottom: 16px;
  }
  
  h3 {
    color: $dark-grey;
    margin-bottom: 8px;
  }
  
  p {
    color: rgba($dark-grey, 0.7);
    margin-bottom: 24px;
  }
}

.practitioners-list {
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid rgba($dark-grey, 0.1);
    }
    
    th {
      font-weight: 600;
      color: $dark-grey;
      background-color: rgba($primary-subtle, 0.05);
    }
    
    tr:hover {
      background-color: rgba($primary-subtle, 0.02);
    }
    
    .practitioner-name {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .practitioner-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
      
      &.active {
        background-color: rgba($success, 0.1);
        color: $success;
      }
      
      &.pending {
        background-color: rgba($warning, 0.1);
        color: $warning;
      }
      
      &.inactive {
        background-color: rgba($danger, 0.1);
        color: $danger;
      }
    }
    
    .actions {
      display: flex;
      gap: 8px;
      
      .btn-action {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        border-radius: 4px;
        background-color: rgba($primary-subtle, 0.1);
        color: $primary;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background-color: rgba($primary-subtle, 0.2);
        }
        
        &.deactivate {
          background-color: rgba($danger, 0.1);
          color: $danger;
          
          &:hover {
            background-color: rgba($danger, 0.2);
          }
        }
        
        &.activate {
          background-color: rgba($success, 0.1);
          color: $success;
          
          &:hover {
            background-color: rgba($success, 0.2);
          }
        }
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 8px;
  
  .btn-page {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: $white;
    border: 1px solid rgba($dark-grey, 0.2);
    border-radius: 4px;
    color: $dark-grey;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover:not(:disabled) {
      background-color: rgba($primary-subtle, 0.1);
      border-color: $primary;
      color: $primary;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    i {
      font-size: 0.8rem;
    }
  }
  
  .page-numbers {
    display: flex;
    gap: 4px;
    
    .btn-page-number {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $white;
      border: 1px solid rgba($dark-grey, 0.2);
      border-radius: 4px;
      color: $dark-grey;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover:not(.active) {
        background-color: rgba($primary-subtle, 0.1);
        border-color: $primary;
        color: $primary;
      }
      
      &.active {
        background-color: $primary;
        border-color: $primary;
        color: $white;
        font-weight: 600;
      }
    }
  }
}
</style>
