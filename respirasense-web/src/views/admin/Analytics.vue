<template>
  <div class="analytics-dashboard">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-chart-bar"></i>
        <h2>Analytics Dashboard</h2>
      </div>
      <div class="date-range-selector">
        <label for="dateRange">Time Period:</label>
        <select id="dateRange" v-model="dateRange">
          <option value="7">Last 7 Days</option>
          <option value="30">Last 30 Days</option>
          <option value="90">Last 90 Days</option>
          <option value="365">Last Year</option>
        </select>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading analytics data...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{{ error }}</p>
      <button @click="fetchAnalyticsData" class="btn-retry">Retry</button>
    </div>

    <div v-else class="analytics-content">
      <!-- Summary Cards -->
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="card-content">
            <h3>Total Patients</h3>
            <div class="card-value">{{ analytics.totalPatients }}</div>
            <div class="card-trend" :class="{ 'positive': patientsTrend > 0, 'negative': patientsTrend < 0 }">
              <i :class="patientsTrend > 0 ? 'fas fa-arrow-up' : patientsTrend < 0 ? 'fas fa-arrow-down' : 'fas fa-minus'"></i>
              <span>{{ Math.abs(patientsTrend) }}% from previous period</span>
            </div>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-user-md"></i>
          </div>
          <div class="card-content">
            <h3>Active Practitioners</h3>
            <div class="card-value">{{ analytics.activePractitioners }}</div>
            <div class="card-trend" :class="{ 'positive': practitionersTrend > 0, 'negative': practitionersTrend < 0 }">
              <i :class="practitionersTrend > 0 ? 'fas fa-arrow-up' : practitionersTrend < 0 ? 'fas fa-arrow-down' : 'fas fa-minus'"></i>
              <span>{{ Math.abs(practitionersTrend) }}% from previous period</span>
            </div>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-heartbeat"></i>
          </div>
          <div class="card-content">
            <h3>Total Readings</h3>
            <div class="card-value">{{ analytics.readingsData.totalReadings }}</div>
            <div class="card-trend" :class="{ 'positive': readingsTrend > 0, 'negative': readingsTrend < 0 }">
              <i :class="readingsTrend > 0 ? 'fas fa-arrow-up' : readingsTrend < 0 ? 'fas fa-arrow-down' : 'fas fa-minus'"></i>
              <span>{{ Math.abs(readingsTrend) }}% from previous period</span>
            </div>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <div class="card-content">
            <h3>Active Patients</h3>
            <div class="card-value">{{ analytics.activePatients }}</div>
            <div class="card-trend" :class="{ 'positive': activePatientsTrend > 0, 'negative': activePatientsTrend < 0 }">
              <i :class="activePatientsTrend > 0 ? 'fas fa-arrow-up' : activePatientsTrend < 0 ? 'fas fa-arrow-down' : 'fas fa-minus'"></i>
              <span>{{ Math.abs(activePatientsTrend) }}% from previous period</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <div class="chart-container">
          <h3>Readings Over Time</h3>
          <div class="chart-wrapper">
            <canvas ref="readingsChart"></canvas>
          </div>
        </div>

        <div class="chart-container">
          <h3>User Activity</h3>
          <div class="chart-wrapper">
            <canvas ref="userActivityChart"></canvas>
          </div>
        </div>
      </div>

      <!-- User Engagement Section -->
      <div class="engagement-section">
        <h3>User Engagement</h3>
        <div class="engagement-metrics">
          <div class="metric-card">
            <div class="metric-title">Average Readings per Patient</div>
            <div class="metric-value">{{ averageReadingsPerPatient.toFixed(1) }}</div>
          </div>
          <div class="metric-card">
            <div class="metric-title">Patient Retention Rate</div>
            <div class="metric-value">{{ (patientRetentionRate * 100).toFixed(1) }}%</div>
          </div>
          <div class="metric-card">
            <div class="metric-title">Active Patient Rate</div>
            <div class="metric-value">{{ (activePatientRate * 100).toFixed(1) }}%</div>
          </div>
        </div>
      </div>

      <!-- System Health Section -->
      <div class="system-health-section">
        <h3>System Health</h3>
        <div class="health-metrics">
          <div class="health-card" :class="systemHealth.status">
            <div class="health-icon">
              <i :class="getHealthIcon(systemHealth.status)"></i>
            </div>
            <div class="health-content">
              <div class="health-title">Overall Status</div>
              <div class="health-value">{{ formatHealthStatus(systemHealth.status) }}</div>
            </div>
          </div>

          <div class="health-card" :class="systemHealth.components.database.status">
            <div class="health-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="health-content">
              <div class="health-title">Database</div>
              <div class="health-value">{{ formatHealthStatus(systemHealth.components.database.status) }}</div>
            </div>
          </div>

          <div class="health-card" :class="systemHealth.components.authentication.status">
            <div class="health-icon">
              <i class="fas fa-lock"></i>
            </div>
            <div class="health-content">
              <div class="health-title">Authentication</div>
              <div class="health-value">{{ formatHealthStatus(systemHealth.components.authentication.status) }}</div>
            </div>
          </div>

          <div class="health-card" :class="systemHealth.components.api.status">
            <div class="health-icon">
              <i class="fas fa-code"></i>
            </div>
            <div class="health-content">
              <div class="health-title">API</div>
              <div class="health-value">{{ formatHealthStatus(systemHealth.components.api.status) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';

export default {
  name: 'AdminAnalytics',

  setup() {
    const store = useStore();
    const isLoading = ref(true);
    const error = ref(null);
    const dateRange = ref('30');
    const analytics = ref({
      totalPatients: 0,
      activePatients: 0,
      totalPractitioners: 0,
      activePractitioners: 0,
      readingsData: {
        totalReadings: 0,
        readingsByDay: {}
      },
      lastUpdated: null
    });
    const systemHealth = ref({
      status: 'unknown',
      components: {
        database: { status: 'unknown' },
        authentication: { status: 'unknown' },
        api: { status: 'unknown' },
        storage: { status: 'unknown' }
      },
      metrics: {
        responseTime: 0,
        uptime: 0,
        errorRate: 0
      }
    });

    // Chart references
    const readingsChart = ref(null);
    const userActivityChart = ref(null);

    // Chart instances
    let readingsChartInstance = null;
    let userActivityChartInstance = null;

    // Computed properties for trends (mock data for now)
    const patientsTrend = computed(() => 5.2);
    const practitionersTrend = computed(() => -2.1);
    const readingsTrend = computed(() => 12.7);
    const activePatientsTrend = computed(() => 8.3);

    // Computed properties for metrics
    const averageReadingsPerPatient = computed(() => {
      if (analytics.value.totalPatients === 0) return 0;
      return analytics.value.readingsData.totalReadings / analytics.value.totalPatients;
    });

    const patientRetentionRate = computed(() => {
      // Mock data for now
      return 0.85;
    });

    const activePatientRate = computed(() => {
      if (analytics.value.totalPatients === 0) return 0;
      return analytics.value.activePatients / analytics.value.totalPatients;
    });

    // Methods
    const fetchAnalyticsData = async () => {
      isLoading.value = true;
      error.value = null;

      try {
        const [analyticsData, healthData] = await Promise.all([
          store.dispatch('admin/fetchAnalytics'),
          store.dispatch('admin/fetchSystemHealth')
        ]);

        analytics.value = analyticsData;
        systemHealth.value = healthData;

        // Initialize charts after data is loaded
        initCharts();
      } catch (err) {
        console.error('Failed to fetch analytics data:', err);
        error.value = 'Failed to load analytics data. Please try again.';
      } finally {
        isLoading.value = false;
      }
    };

    const initCharts = () => {
      // Destroy existing chart instances if they exist
      if (readingsChartInstance) {
        readingsChartInstance.destroy();
      }

      if (userActivityChartInstance) {
        userActivityChartInstance.destroy();
      }

      // Initialize readings chart
      if (readingsChart.value) {
        const ctx = readingsChart.value.getContext('2d');

        // Prepare data for readings chart
        const readingsByDay = analytics.value.readingsData.readingsByDay || {};
        const labels = Object.keys(readingsByDay).sort();
        const data = labels.map(label => readingsByDay[label]);

        readingsChartInstance = new Chart(ctx, {
          type: 'line',
          data: {
            labels,
            datasets: [{
              label: 'Daily Readings',
              data,
              backgroundColor: 'rgba(183, 21, 64, 0.2)',
              borderColor: '#b71540',
              borderWidth: 2,
              tension: 0.4,
              pointBackgroundColor: '#b71540',
              pointRadius: 3
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                mode: 'index',
                intersect: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  precision: 0
                }
              }
            }
          }
        });
      }

      // Initialize user activity chart
      if (userActivityChart.value) {
        const ctx = userActivityChart.value.getContext('2d');

        // Mock data for user activity chart
        userActivityChartInstance = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['Patients', 'Practitioners'],
            datasets: [
              {
                label: 'Total',
                data: [analytics.value.totalPatients, analytics.value.totalPractitioners],
                backgroundColor: 'rgba(183, 21, 64, 0.7)'
              },
              {
                label: 'Active',
                data: [analytics.value.activePatients, analytics.value.activePractitioners],
                backgroundColor: 'rgba(76, 161, 163, 0.7)'
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  precision: 0
                }
              }
            }
          }
        });
      }
    };

    const getHealthIcon = (status) => {
      const icons = {
        'healthy': 'fas fa-check-circle',
        'degraded': 'fas fa-exclamation-triangle',
        'critical': 'fas fa-times-circle',
        'unknown': 'fas fa-question-circle'
      };

      return icons[status] || icons.unknown;
    };

    const formatHealthStatus = (status) => {
      const statusMap = {
        'healthy': 'Healthy',
        'degraded': 'Degraded',
        'critical': 'Critical',
        'unknown': 'Unknown'
      };

      return statusMap[status] || 'Unknown';
    };

    // Watch for date range changes
    watch(dateRange, () => {
      fetchAnalyticsData();
    });

    onMounted(() => {
      fetchAnalyticsData();
    });

    onUnmounted(() => {
      // Clean up chart instances
      if (readingsChartInstance) {
        readingsChartInstance.destroy();
      }

      if (userActivityChartInstance) {
        userActivityChartInstance.destroy();
      }
    });

    return {
      isLoading,
      error,
      dateRange,
      analytics,
      systemHealth,
      readingsChart,
      userActivityChart,
      patientsTrend,
      practitionersTrend,
      readingsTrend,
      activePatientsTrend,
      averageReadingsPerPatient,
      patientRetentionRate,
      activePatientRate,
      fetchAnalyticsData,
      getHealthIcon,
      formatHealthStatus
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.analytics-dashboard {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 1.5rem;
      color: $primary;
    }

    h2 {
      margin: 0;
      color: $dark-grey;
      font-size: 1.5rem;
    }
  }

  .date-range-selector {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      font-size: 0.9rem;
      color: $dark-grey;
    }

    select {
      padding: 8px 12px;
      border: 1px solid rgba($dark-grey, 0.2);
      border-radius: 4px;
      background-color: $white;
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: $primary;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba($primary, 0.2);
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    color: $dark-grey;
    font-size: 0.9rem;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  i {
    font-size: 2rem;
    color: $danger;
    margin-bottom: 16px;
  }

  p {
    color: $dark-grey;
    margin-bottom: 16px;
  }

  .btn-retry {
    padding: 8px 16px;
    background-color: $primary;
    color: $white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}

.analytics-content {
  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .summary-card {
      background-color: $white;
      border-radius: 8px;
      box-shadow: $shadow-sm;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;

      .card-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: rgba($primary-subtle, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 1.5rem;
          color: $primary;
        }
      }

      .card-content {
        flex: 1;

        h3 {
          margin: 0 0 8px 0;
          color: $dark-grey;
          font-size: 0.9rem;
          font-weight: 600;
        }

        .card-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: $dark-grey;
          margin-bottom: 4px;
        }

        .card-trend {
          font-size: 0.8rem;
          display: flex;
          align-items: center;
          gap: 4px;

          &.positive {
            color: $success;
          }

          &.negative {
            color: $danger;
          }
        }
      }
    }
  }

  .charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .chart-container {
      background-color: $white;
      border-radius: 8px;
      box-shadow: $shadow-sm;
      padding: 20px;

      h3 {
        margin: 0 0 16px 0;
        color: $dark-grey;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .chart-wrapper {
        height: 300px;
        position: relative;
      }
    }
  }

  .engagement-section {
    background-color: $white;
    border-radius: 8px;
    box-shadow: $shadow-sm;
    padding: 20px;
    margin-bottom: 30px;

    h3 {
      margin: 0 0 16px 0;
      color: $dark-grey;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .engagement-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;

      .metric-card {
        background-color: rgba($primary-subtle, 0.05);
        border-radius: 8px;
        padding: 16px;
        text-align: center;

        .metric-title {
          font-size: 0.9rem;
          color: $dark-grey;
          margin-bottom: 8px;
        }

        .metric-value {
          font-size: 1.5rem;
          font-weight: 700;
          color: $primary;
        }
      }
    }
  }

  .system-health-section {
    background-color: $white;
    border-radius: 8px;
    box-shadow: $shadow-sm;
    padding: 20px;

    h3 {
      margin: 0 0 16px 0;
      color: $dark-grey;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .health-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;

      .health-card {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        border-radius: 8px;

        &.healthy {
          background-color: rgba($success, 0.1);

          .health-icon i {
            color: $success;
          }

          .health-value {
            color: $success;
          }
        }

        &.degraded {
          background-color: rgba($warning, 0.1);

          .health-icon i {
            color: $warning;
          }

          .health-value {
            color: $warning;
          }
        }

        &.critical {
          background-color: rgba($danger, 0.1);

          .health-icon i {
            color: $danger;
          }

          .health-value {
            color: $danger;
          }
        }

        &.unknown {
          background-color: rgba($dark-grey, 0.1);

          .health-icon i {
            color: $dark-grey;
          }

          .health-value {
            color: $dark-grey;
          }
        }

        .health-icon {
          font-size: 1.5rem;
        }

        .health-content {
          flex: 1;

          .health-title {
            font-size: 0.8rem;
            color: $dark-grey;
            margin-bottom: 4px;
          }

          .health-value {
            font-size: 1rem;
            font-weight: 600;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }
}
</style>