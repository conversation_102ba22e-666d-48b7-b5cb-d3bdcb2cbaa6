<template>
  <section class="wrapper-login">
    <Card>
      <template v-slot:card-header>
        <Logo :path="logo" :className="'text-center'" :width="'180'"></Logo>
      </template>
      <template v-slot:card-body>
        <br>
        <h4 class="my-2 text-muted">Sign In</h4>
        <form class="login-form" @submit.prevent="handleSubmitLogin">
          <div v-if="hasError" className="auth__error">
            <p class="mb-1 subtitle">{{errorText}}</p>
          </div>
          <div class="form-group">
            <label for="email">Email Address</label>
            <input
              type="email"
              placeholder=""
              id="email"
              v-model="email"
            />
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input
              type="password"
              placeholder=""
              id="password"
              v-model="password"
              autocomplete="on"
            />
            <router-link to="#" class="ev-link forgot "
              >Forgot Password?</router-link
            >
          </div>
          <button
            class="btn btn-ck fill-danger w-50 m-auto"
            type="submit"
          >
            Sign in
          </button>
        </form>
      </template>
      <hr />
      <template v-slot:card-footer>
        <div className="auth__social-networks">
          <social-button
            @click="handleGoogleLogin"
            icon="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/24px-Google_%22G%22_logo.svg.png"
            text="Sign in with Google"
            className="btn btn-ck otl-danger"
            :disabled="loading"
          />
          <div v-if="hasError" class="error-message">
            {{ errorText }}
          </div>
        </div>
        <div class="text-center">
          <span class="ft-10">Not a member? </span>
          <router-link class="ev-link" to="/auth/signup">Sign up</router-link>
        </div>
      </template>
    </Card>
  </section>
</template>
<script>
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";
import { mapActions, mapState } from 'vuex';
import SocialButton from '../../components/auth/SocialButton.vue';
import { getRedirectResult } from 'firebase/auth';
import { auth, sendPasswordResetEmail } from '@/plugins/firebase/firebase';

export default {
  name: 'Login',
  data() {
    return {
      email: "",
      password: "",
      logo: require('@/assets/<EMAIL>'),
      loading: false,
      error: false,
      errorMessage: ""
    };
  },
  components: {
    Logo,
    Card,
    SocialButton,
  },
  computed: {
    ...mapState('authFirebase', {
      storeError: 'error',
      storeErrorMessage: 'errorMessage'
    }),
    hasError() {
      return this.error || this.storeError;
    },
    errorText() {
      return this.errorMessage || this.storeErrorMessage;
    }
  },
  methods: {
    ...mapActions({
      signIn: 'authFirebase/SignIn',
      loginWithGoogle: 'authFirebase/LogInWithGoogle'
    }),
    async handleSubmitLogin() {
      this.loading = true;
      this.error = false;

      try {
        const response = await this.signIn({
          email: this.email,
          password: this.password
        });

        console.log('SignIn response:', response);

        if (response?.isLogged) {
          // Get user role from response first, fallback to localStorage
          const userRole = response.role || localStorage.getItem('userRole');
          console.log('User role for routing:', userRole);

          // If no role is found, redirect to registration
          if (!userRole) {
            console.log('No role found, redirecting to registration');
            await this.$router.push('/patient/registration');
            return;
          }

          // Define dashboard routes
          const dashboardRoutes = {
            'admin': '/admin/dashboard',
            'superAdmin': '/admin/dashboard',
            'practitioner': '/practitioner/dashboard',
            'patient': '/patient/dashboard'
          };

          const dashboardPath = dashboardRoutes[userRole];

          if (!dashboardPath) {
            console.error('Invalid role:', userRole);
            this.error = true;
            this.errorMessage = "Invalid user role configuration";
            return;
          }

          console.log('Redirecting to dashboard:', dashboardPath);
          await this.$router.push(dashboardPath);

        } else if (response?.error) {
          console.error('Login error:', response);
          this.error = true;
          this.errorMessage = response.originalError || response.error;
        }
      } catch (error) {
        console.error('Login error:', error);
        this.error = true;
        this.errorMessage = error.message || "Failed to sign in. Please try again.";
      } finally {
        this.loading = false;
      }
    },
    async handleGoogleLogin() {
      this.loading = true;
      this.error = false;

      try {
        const response = await this.loginWithGoogle();
        console.log('Google login response:', response);

        if (response.error) {
          this.error = true;
          this.errorMessage = response.error;
          return;
        }

        if (response.needsRoleSelection) {
          // Redirect to role selection page for new Google users
          await this.$router.push('/auth/role-selection');
          return;
        }

        if (response.isNewUser) {
          await this.$router.push('/patient/registration');
          return;
        }

        // Regular login success - get role from response or localStorage
        const userRole = response.role || localStorage.getItem('userRole');
        if (!userRole) {
          this.error = true;
          this.errorMessage = "User role not found";
          return;
        }

        // Redirect to appropriate dashboard
        await this.$router.push(`/${userRole}/dashboard`);
      } catch (err) {
        console.error('Google login error:', err);
        this.error = true;
        this.errorMessage = err.message || "Failed to sign in with Google";
      } finally {
        this.loading = false;
      }
    },
    async handleResetPassword() {
      try {
        await sendPasswordResetEmail(auth, this.email);
        this.error = false;
        this.errorMessage = "Password reset email sent. Please check your inbox.";
      } catch (error) {
        this.error = true;
        this.errorMessage = "Failed to send password reset email.";
      }
    }
  },
  async mounted() {
    try {
      // Check if there's an error message in the URL query parameters
      if (this.$route.query.error) {
        this.error = true;
        this.errorMessage = this.$route.query.error;
      }

      // Check if we're returning from a redirect
      const result = await getRedirectResult(auth);
      if (result) {
        // Get user role from localStorage or fetch it
        const userRole = localStorage.getItem('userRole');
        if (userRole) {
          // Construct the dashboard route based on user role
          const dashboardRoute = `${userRole.charAt(0).toUpperCase() + userRole.slice(1)}Dashboard`;
          await this.$router.push({ name: dashboardRoute });
        }
      }
    } catch (error) {
      console.error('Redirect result error:', error);
      this.error = true;
      this.errorMessage = "Failed to complete Google sign in. Please try again.";
    }
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/base/settings";

.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  height: 100vh;
  background: $background-login;

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 2rem;

    .form-group {
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;

      label {
        text-transform: uppercase;
        text-align: start;
        margin-bottom: 3px;
        font-size: 10pt;
        color: #6c757d
      }
      input {
        margin-bottom: 0.5rem;
        font-size: 1em;
        border: 0;
        border-bottom: 1px solid #6c757d;
        -webkit-appearance: none;
        appearance: none;
        border-radius: 0;
        padding: 0;
        cursor: text;
      }:focus {
        outline: 0;
        border-bottom: 1px solid #B61440;
      }

      a {
        width: 100%;
        display: flex;
        justify-content: flex-end;

        &.forgot {
          font-size: 10pt;
          color: #6c757d
        }
      }
    }
  }

  .auth__social-networks {
    margin-bottom: .5rem;
    display: grid;
    gap: 10px;

    .subtitle {
      margin-top: 0;
      text-align: center;
    }
  }
  .auth__error {
    margin-bottom: .5rem;
    display: grid;
    gap: 10px;
    margin-top: 0;
    text-align: center;
    color: red;
  }
}

.error-message {
  color: #dc3545;
  margin-top: 10px;
  text-align: center;
  font-size: 0.9em;
}

.auth__social-networks {
  .btn {
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}
</style>
