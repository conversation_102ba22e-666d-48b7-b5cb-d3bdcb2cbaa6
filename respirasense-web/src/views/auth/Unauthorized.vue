<template>
  <section class="wrapper-login">
    <Card class="wider-card">
      <template #card-header>
        <div class="logo-container">
          <Logo :path="logo" class="text-center" width="180" />
        </div>
        <h1 class="registration-heading">Unauthorized Access</h1>
      </template>

      <template #card-body>
        <div class="unauthorized-container">
          <div class="unauthorized-icon">
            <div class="lock-circle">
              <i class="fas fa-lock"></i>
            </div>
          </div>
          <p>You don't have permission to access this resource.</p>
          <div class="submit-button-container">
            <button @click="goBack" class="submit-button outline">GO BACK</button>
            <button @click="goToLogin" class="submit-button">GO TO LOGIN</button>
          </div>
        </div>
      </template>
    </Card>
  </section>
</template>

<script>
import { useRouter } from 'vue-router';
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";

export default {
  name: 'Unauthorized',
  components: {
    Logo,
    Card
  },
  setup() {
    const router = useRouter();
    const logo = require('@/assets/<EMAIL>');

    const goBack = () => {
      router.go(-1);
    };

    const goToLogin = () => {
      router.push('/auth/login');
    };

    return {
      logo,
      goBack,
      goToLogin
    };
  }
}
</script>

<style lang="scss" scoped>
.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

.wider-card {
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 5px;
}

.registration-heading {
  text-align: center;
  color: #6b7280;
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.unauthorized-container {
  text-align: center;
  padding: 20px;
}

.unauthorized-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.lock-circle {
  width: 80px;
  height: 80px;
  background-color: #dc3545;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  i {
    font-size: 2.5rem;
    color: white;
  }
}

p {
  margin-bottom: 2rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  font-size: 0.95rem;
}

// Button styles now imported from global styles
</style>