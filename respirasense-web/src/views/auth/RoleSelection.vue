<template>
  <section class="wrapper-login">
    <Card class="wider-card">
      <template v-slot:card-header>
        <div class="logo-container">
          <Logo :path="logo" :className="'text-center'" :width="'180'"></Logo>
        </div>
        <h1 class="registration-heading">Complete Your<br>Registration</h1>
      </template>
      <template v-slot:card-body>
        <div v-if="error" class="auth__error">
          <p class="mb-1 subtitle">{{ error }}</p>
        </div>
        <form class="login-form" @submit.prevent="handleSubmit">
          <div class="form-group">
            <label for="name" class="form-label">Full Name</label>
            <input
              type="text"
              id="name"
              v-model="form.name"
              required
              placeholder="Enter your full name"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="phone" class="form-label">Phone Number (optional)</label>
            <input
              type="tel"
              id="phone"
              v-model="form.phone"
              placeholder="Enter your phone number"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="role" class="form-label">Select Account Type</label>
            <select
              id="role"
              v-model="form.role"
              required
              class="form-select"
            >
              <option value="">Select account type</option>
              <option value="patient">Patient</option>
              <option value="practitioner">Healthcare Professional</option>
            </select>
          </div>

          <!-- Practitioner-specific fields -->
          <div v-if="form.role === 'practitioner'" class="practitioner-fields">
            <div class="form-group">
              <label for="specialty" class="form-label">Specialty</label>
              <select
                id="specialty"
                v-model="form.specialty"
                required
                class="form-select"
              >
                <option value="">Select specialty</option>
                <option value="general">General Practitioner</option>
                <option value="respiratory">Respiratory Specialist</option>
                <option value="cardiology">Cardiologist</option>
                <option value="internal">Internal Medicine</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div v-if="form.specialty === 'other'" class="form-group">
              <label for="otherSpecialty" class="form-label">Please specify</label>
              <input
                type="text"
                id="otherSpecialty"
                v-model="form.otherSpecialty"
                required
                placeholder="Enter your specialty"
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label for="licenseNumber" class="form-label">License/Registration Number</label>
              <input
                type="text"
                id="licenseNumber"
                v-model="form.licenseNumber"
                required
                placeholder="Enter your professional license number"
                class="form-input"
              />
            </div>
          </div>

          <div class="info-container">
            <div class="info-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="info-text">
              Your account will need to be approved by an administrator before you can access the system.
            </div>
          </div>

          <div class="submit-button-container">
            <button
              class="submit-button"
              type="submit"
              :disabled="loading"
            >
              <span v-if="loading">Processing...</span>
              <span v-else>SUBMIT REQUEST</span>
            </button>
          </div>
        </form>
      </template>
    </Card>
  </section>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";
import { notify } from '@/utils/notifications';

export default {
  name: 'RoleSelection',
  components: {
    Logo,
    Card
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const loading = ref(false);
    const error = ref('');
    const logo = require('@/assets/<EMAIL>');

    const form = ref({
      name: '',
      phone: '',
      role: '',
      specialty: '',
      otherSpecialty: '',
      licenseNumber: ''
    });

    const handleSubmit = async () => {
      loading.value = true;
      error.value = '';

      try {
        // Prepare the data for submission
        const userData = {
          name: form.value.name,
          phone: form.value.phone || '',
          role: form.value.role,
          status: 'pending' // All new registrations start as pending
        };

        // Add practitioner-specific fields if applicable
        if (form.value.role === 'practitioner') {
          userData.specialty = form.value.specialty === 'other'
            ? form.value.otherSpecialty
            : form.value.specialty;
          userData.licenseNumber = form.value.licenseNumber;
        }

        // Submit the role selection request
        await store.dispatch('authFirebase/submitRoleSelection', userData);

        // Show success message
        notify({
          type: 'success',
          text: 'Your registration has been submitted for approval. You will be notified when your account is activated.'
        });

        // Redirect to a confirmation page
        router.push('/auth/registration-pending');
      } catch (err) {
        console.error('Error submitting role selection:', err);
        error.value = err.message || 'Failed to submit registration. Please try again.';
        notify({
          type: 'error',
          text: error.value
        });
      } finally {
        loading.value = false;
      }
    };

    return {
      form,
      error,
      loading,
      logo,
      handleSubmit
    };
  }
};
</script>

<style lang="scss" scoped>
.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

.wider-card {
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 5px;
}

.registration-heading {
  text-align: center;
  color: #6b7280;
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #4a5568;
  font-size: 0.95rem;
}

.form-input, .form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
  }

  &::placeholder {
    color: #a0aec0;
  }
}

.info-container {
  display: flex;
  margin-bottom: 1.5rem;
  padding: 12px;
  border-left: 3px solid #dc3545;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.info-icon {
  color: #dc3545;
  margin-right: 10px;
  font-size: 1.1rem;
  display: flex;
  align-items: flex-start;
}

.info-text {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
}

.practitioner-fields {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  border: 1px solid #edf2f7;
}

.auth__error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

// Button styles now imported from global styles
</style>
