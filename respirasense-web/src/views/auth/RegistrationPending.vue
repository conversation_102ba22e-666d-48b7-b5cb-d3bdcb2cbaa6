<template>
  <section class="wrapper-login">
    <Card class="wider-card">
      <template v-slot:card-header>
        <div class="logo-container">
          <Logo :path="logo" :className="'text-center'" :width="'180'"></Logo>
        </div>
        <h1 class="registration-heading">Registration Pending</h1>
      </template>
      <template v-slot:card-body>
        <div class="pending-container">
          <div class="pending-icon">
            <div class="clock-circle">
              <i class="fas fa-clock"></i>
            </div>
          </div>
          <h2>Thank You for Registering</h2>
          <p>
            Your registration has been submitted and is pending approval by an administrator.
            You will receive an email notification once your account has been activated.
          </p>
          <p>
            If you have any questions or need assistance, please contact our support team.
          </p>
          <div class="submit-button-container">
            <button @click="handleLogout" class="submit-button outline">
              SIGN OUT
            </button>
            <button @click="goToLogin" class="submit-button">
              RETURN TO LOGIN
            </button>
          </div>
        </div>
      </template>
    </Card>
  </section>
</template>

<script>
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";

export default {
  name: 'RegistrationPending',
  components: {
    Logo,
    Card
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const logo = require('@/assets/<EMAIL>');

    const handleLogout = async () => {
      try {
        await store.dispatch('authFirebase/SignOut');
        router.push('/auth/login');
      } catch (error) {
        console.error('Logout error:', error);
      }
    };

    const goToLogin = () => {
      router.push('/auth/login');
    };

    return {
      logo,
      handleLogout,
      goToLogin
    };
  }
};
</script>

<style lang="scss" scoped>
.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

.wider-card {
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 5px;
}

.registration-heading {
  text-align: center;
  color: #6b7280;
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.pending-container {
  text-align: center;
  padding: 20px;
}

.pending-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.clock-circle {
  width: 80px;
  height: 80px;
  background-color: #dc3545;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  i {
    font-size: 2.5rem;
    color: white;
  }
}

// Button styles now imported from global styles

h2 {
  margin-bottom: 1.5rem;
  color: #4a5568;
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  font-size: 0.95rem;
}
</style>
