<template>
  <section class="wrapper-login">
    <Card>
      <template v-slot:card-header>
        <Logo :path="logo" :className="'text-center'" :width="'180'"></Logo>
      </template>
      <template v-slot:card-body>
        <br>
        <h4 class="my-2 text-muted">Sign Up</h4>
        <form class="login-form" @submit.prevent="handleSubmitRegister">
          <div v-if="error" class="auth__error">
            <p class="mb-1 subtitle">{{ error }}</p>
          </div>
          <div class="form-group">
            <label for="email">Email Address</label>
            <input
              type="email"
              placeholder=""
              id="email"
              v-model="email"
              required
            />
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input
              type="password"
              placeholder=""
              id="password"
              v-model="password"
              required
              minlength="8"
            />
          </div>
          <div class="form-group">
            <label for="confirmPassword">Confirm Password</label>
            <input
              type="password"
              placeholder=""
              id="confirmPassword"
              v-model="confirmPassword"
              required
            />
          </div>

          <button 
            class="btn btn-ck fill-danger w-50 m-auto"
            type="submit"
          >
            Sign Up
          </button>
        </form>
      </template>
      <hr />
      <template v-slot:card-footer>
        <div class="auth__social-networks">
          <social-button 
            @click="handleGoogleSignUp"
            icon="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/24px-Google_%22G%22_logo.svg.png" 
            text="Sign up with Google"
            className="btn btn-ck otl-danger"
          />
        </div>
        <div class="text-center">
          <span class="ft-10">Already have an account? </span>
          <router-link class="ev-link" to="/auth/login">Sign in</router-link>
        </div>
      </template>
    </Card>
  </section>
</template>
<script>
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";
import SocialButton from '@/components/auth/SocialButton.vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { notify } from '@kyvg/vue3-notification';

export default {
  name: 'Signup',
  components: {
    Logo,
    Card,
    SocialButton
  },
  setup() {
    const email = ref('');
    const password = ref('');
    const confirmPassword = ref('');
    const error = ref('');
    const logo = require('@/assets/<EMAIL>');
    const router = useRouter();
    const store = useStore();

    const handleSubmitRegister = async () => {
      error.value = '';

      // Validate passwords match
      if (password.value !== confirmPassword.value) {
        error.value = 'Passwords do not match';
        return;
      }

      // Validate password length
      if (password.value.length < 8) {
        error.value = 'Password must be at least 8 characters long';
        return;
      }

      try {
        const response = await store.dispatch('authFirebase/SignUp', {
          email: email.value,
          password: password.value
        });

        if (response.error) {
          if (response.errorCode === 'auth/email-already-in-use') {
            error.value = 'This email is already registered';
          } else {
            error.value = response.error;
          }
          return;
        }

        // Successful signup - redirect to patient registration
        router.push('/patient/registration');
        
      } catch (err) {
        error.value = 'An error occurred during registration';
        console.error('Registration error:', err);
      }
    };

    const handleGoogleSignUp = async () => {
      try {
        const response = await store.dispatch('authFirebase/LogInWithGoogle');
        
        if (response.error) {
          error.value = response.error;
          return;
        }

        if (response.isNewUser) {
          // New Google user - redirect to patient registration
          router.push('/patient/registration');
        } else {
          // Existing user - redirect to dashboard
          router.push('/patient/dashboard');
        }
      } catch (err) {
        error.value = 'An error occurred during Google sign-up';
        console.error('Google signup error:', err);
      }
    };

    return {
      email,
      password,
      confirmPassword,
      error,
      logo,
      handleSubmitRegister,
      handleGoogleSignUp
    };
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/styles/base/settings";

.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  height: 100vh;
  background: $background-login;

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 2rem;

    .form-group {
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;

      label {
        text-transform: uppercase;
        text-align: start;
        margin-bottom: 3px;
        font-size: 10pt;
        color: #6c757d
      }
      
      input {
        margin-bottom: 0.5rem;
        font-size: 1em;
        border: 0;
        border-bottom: 1px solid #6c757d;
        -webkit-appearance: none;
        border-radius: 0;
        padding: 0;
        cursor: text;
        &:focus {
          outline: 0;
          border-bottom: 1px solid #B61440;
        }
      }

      button {
        margin-top: 0.5rem;
        transition: all 0.3s ease;
        
        i {
          margin-right: 0.5rem;
        }
      }
    }
  }
  
  .auth__social-networks {
    margin-bottom: .5rem;
    display: grid;
    gap: 10px;

    .subtitle {
      margin-top: 0;
      text-align: center;
    }
  }

  .auth__error {
    margin-bottom: .5rem;
    display: grid;
    gap: 10px;
    margin-top: 0;
    text-align: center;
    color: red;
  }
}

.error-message {
  color: #dc3545;
  margin-top: 10px;
  text-align: center;
  font-size: 0.9em;
}
</style>
