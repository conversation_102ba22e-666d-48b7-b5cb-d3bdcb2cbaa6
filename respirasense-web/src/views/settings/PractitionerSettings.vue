<template>
  <div class="settings-container">
    <h1>Practitioner Settings</h1>

    <div class="settings-sections">
      <!-- Professional Profile -->
      <section class="settings-section">
        <h2>Professional Profile</h2>
        <div class="form-group">
          <label>Full Name</label>
          <input v-model="settings.profile.name" type="text" />
        </div>
        <div class="form-group">
          <label>License Number</label>
          <input v-model="settings.profile.licenseNumber" type="text" />
        </div>
        <div class="form-group">
          <label>Specialization</label>
          <input v-model="settings.profile.specialization" type="text" />
        </div>
      </section>

      <!-- Practice Settings -->
      <section class="settings-section">
        <h2>Practice Settings</h2>
        <div class="form-group">
          <label>Practice Hours</label>
          <div class="hours-grid">
            <div v-for="day in weekDays" :key="day">
              <label>{{ day }}</label>
              <input v-model="settings.practice.hours[day].start" type="time" />
              <input v-model="settings.practice.hours[day].end" type="time" />
            </div>
          </div>
        </div>
      </section>

      <!-- Patient Management -->
      <section class="settings-section">
        <h2>Patient Management</h2>
        <div class="form-group">
          <label>
            <input v-model="settings.patients.autoAccept" type="checkbox" />
            Automatically accept new patient requests
          </label>
        </div>
        <div class="form-group">
          <label>Maximum active patients</label>
          <input v-model="settings.patients.maxPatients" type="number" min="1" />
        </div>
      </section>

      <!-- Notification Preferences -->
      <section class="settings-section">
        <h2>Notification Settings</h2>
        <div class="form-group">
          <label>
            <input v-model="settings.notifications.email" type="checkbox" />
            Email notifications for patient alerts
          </label>
        </div>
        <div class="form-group">
          <label>
            <input v-model="settings.notifications.sms" type="checkbox" />
            SMS notifications for urgent alerts
          </label>
        </div>
        <div class="form-group">
          <label>Alert threshold (hours)</label>
          <input v-model="settings.notifications.threshold" type="number" min="1" />
        </div>
      </section>
    </div>

    <div class="settings-actions">
      <button @click="saveSettings" class="btn-primary">Save Changes</button>
      <button @click="resetSettings" class="btn-secondary">Reset</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'PractitionerSettings',

  setup() {
    const store = useStore();
    const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    const settings = ref({
      profile: {
        name: '',
        licenseNumber: '',
        specialization: ''
      },
      practice: {
        hours: weekDays.reduce((acc, day) => ({
          ...acc,
          [day]: { start: '09:00', end: '17:00' }
        }), {})
      },
      patients: {
        autoAccept: false,
        maxPatients: 50
      },
      notifications: {
        email: true,
        sms: true,
        threshold: 24
      }
    });

    onMounted(async () => {
      try {
        const userSettings = await store.dispatch('practitioner/fetch_settings');
        if (userSettings) {
          settings.value = { ...settings.value, ...userSettings };
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    });

    const saveSettings = async () => {
      try {
        await store.dispatch('practitioner/update_settings', settings.value);
      } catch (error) {
        console.error('Failed to save settings:', error);
      }
    };

    const resetSettings = () => {
      onMounted();
    };

    return {
      settings,
      weekDays,
      saveSettings,
      resetSettings
    };
  }
};
</script>

<style scoped>
.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-sections {
  display: grid;
  gap: 2rem;
}

.settings-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hours-grid {
  display: grid;
  gap: 1rem;
}

.hours-grid > div {
  display: grid;
  grid-template-columns: 100px 1fr 1fr;
  gap: 1rem;
  align-items: center;
}

/* Rest of the styles same as PatientSettings.vue */
</style>