<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="patient-settings">
        <div class="dashboard-header">
          <div class="header-left">
            <router-link @click="goBack" class="ev-link">
              ← Back
            </router-link>
            <h1>Settings</h1>
          </div>
        </div>

        <div class="settings-section">
          <h3>Profile Information</h3>
          <form @submit.prevent="saveSettings">
            <div class="form-group">
              <label>Full Name</label>
              <input 
                v-model="settings.profile.name" 
                type="text" 
                placeholder="Enter your full name"
              >
            </div>

            <div class="form-group">
              <label>Date of Birth</label>
              <input 
                v-model="settings.profile.dob" 
                type="date"
              >
            </div>

            <div class="form-group">
              <label>Emergency Contact</label>
              <input 
                v-model="settings.profile.emergencyContact" 
                type="text" 
                placeholder="Emergency contact number"
              >
            </div>
          </form>
        </div>

        <div class="settings-section">
          <h3>Notifications</h3>
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="settings.notifications.email"
              >
              Receive email notifications
            </label>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="settings.notifications.sms"
              >
              Receive SMS notifications
            </label>
          </div>
        </div>

        <div class="settings-section">
          <h3>Data Sharing</h3>
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="settings.sharing.allowPractitioners"
              >
              Share data with my healthcare practitioners
            </label>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="settings.sharing.allowResearch"
              >
              Allow anonymous data use for research
            </label>
          </div>
        </div>

        <div class="settings-section">
          <h3>Device Settings</h3>
          <div class="form-group">
            <label>Device ID</label>
            <input 
              v-model="settings.device.id" 
              type="text" 
              placeholder="Enter device ID"
            >
          </div>
          <div class="form-group">
            <label>Measurement Units</label>
            <select v-model="settings.device.units">
              <option value="metric">Metric</option>
              <option value="imperial">Imperial</option>
            </select>
          </div>
        </div>

        <div class="form-actions">
          <button 
            @click="saveSettings" 
            class="action-button"
            :disabled="saving"
          >
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import AppHeader from '@/components/layout/Header.vue';

export default {
  name: 'PatientSettings',
  components: {
    AppHeader
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const saving = ref(false);
    
    const goBack = () => {
      const userRole = localStorage.getItem('userRole');
      const dashboardRoutes = {
        'admin': '/admin/dashboard',
        'superAdmin': '/admin/dashboard',
        'practitioner': '/practitioner/dashboard',
        'patient': '/patient/dashboard'
      };
      
      const dashboardPath = dashboardRoutes[userRole];
      if (dashboardPath) {
        router.push(dashboardPath);
      }
    };

    const settings = ref({
      profile: {
        name: '',
        dob: '',
        emergencyContact: ''
      },
      notifications: {
        email: true,
        sms: false
      },
      sharing: {
        allowPractitioners: true,
        allowResearch: false
      },
      device: {
        id: '',
        units: 'metric'
      }
    });

    const saveSettings = async () => {
      try {
        saving.value = true;
        await store.dispatch('patient/updateSettings', settings.value);
        alert('Settings saved successfully');
      } catch (error) {
        console.error('Failed to save settings:', error);
        alert('Failed to save settings');
      } finally {
        saving.value = false;
      }
    };

    onMounted(async () => {
      try {
        const userSettings = await store.dispatch('patient/fetchSettings');
        if (userSettings) {
          settings.value = { ...settings.value, ...userSettings };
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    });

    return {
      settings,
      saving,
      saveSettings,
      goBack
    };
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/base/settings";

.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .main-content {
    flex: 1;
    padding: 24px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.patient-settings {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    .header-left {
      display: flex;
      align-items: center;
      gap: 1rem;

      .ev-link {
        font-size: 1rem;
        color: $link;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }

      h1 {
        margin: 0;
      }
    }
  }
}

.settings-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;

  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
}

.form-group {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
  }

  input[type="text"],
  input[type="date"],
  select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background: white;

    &:focus {
      outline: none;
      border-color: #2978b5;
      box-shadow: 0 0 0 2px rgba(41, 120, 181, 0.1);
    }
  }
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 0;

  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
  }
}

.form-actions {
  margin-top: 24px;
  text-align: right;
}

.action-button {
  padding: 12px 24px;
  background: #2978b5;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s;

  &:hover {
    background: darken(#2978b5, 10%);
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #2978b5;
  border-radius: 8px;
  color: #2978b5;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;

  &:hover {
    background: #2978b5;
    color: white;
  }

  i {
    font-size: 16px;
  }
}
</style>
```

This implementation:

1. Adds a back button in the settings layout that returns to the appropriate dashboard based on user role
2. Creates a comprehensive patient settings component with sections for:
   - Profile information
   - Notification preferences
   - Data sharing preferences
   - Device settings
3. Includes proper styling and form handling
4. Implements save functionality (Note: you'll need to implement the corresponding Vuex actions `patient/fetchSettings` and `patient/updateSettings`)

Make sure you have the Font Awesome icons included in your project for the back button arrow icon to work. If you don't, you can replace `<i class="fas fa-arrow-left"></i>` with a simple text "←" or another icon from your icon set.
