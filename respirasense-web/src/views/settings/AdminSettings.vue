<template>
  <div class="settings-container">
    <h1>Admin Settings</h1>
    
    <div class="settings-sections">
      <!-- System Settings -->
      <section class="settings-section">
        <h2>System Settings</h2>
        <div class="form-group">
          <label>System Name</label>
          <input v-model="settings.system.name" type="text" />
        </div>
        <div class="form-group">
          <label>Default Timezone</label>
          <select v-model="settings.system.timezone">
            <option v-for="tz in timezones" :key="tz" :value="tz">{{ tz }}</option>
          </select>
        </div>
        <div class="form-group">
          <label>Session Timeout (minutes)</label>
          <input v-model="settings.system.sessionTimeout" type="number" min="5" />
        </div>
      </section>

      <!-- Security Settings -->
      <section class="settings-section">
        <h2>Security Settings</h2>
        <div class="form-group">
          <label>Minimum Password Length</label>
          <input v-model="settings.security.minPasswordLength" type="number" min="8" />
        </div>
        <div class="form-group">
          <label>
            <input v-model="settings.security.requireMFA" type="checkbox" />
            Require Two-Factor Authentication
          </label>
        </div>
        <div class="form-group">
          <label>
            <input v-model="settings.security.enforcePasswordHistory" type="checkbox" />
            Enforce Password History
          </label>
        </div>
      </section>

      <!-- User Management -->
      <section class="settings-section">
        <h2>User Management</h2>
        <div class="form-group">
          <label>
            <input v-model="settings.users.autoApprove" type="checkbox" />
            Auto-approve new practitioner accounts
          </label>
        </div>
        <div class="form-group">
          <label>Maximum failed login attempts</label>
          <input v-model="settings.users.maxLoginAttempts" type="number" min="1" />
        </div>
        <div class="form-group">
          <label>Account lockout duration (minutes)</label>
          <input v-model="settings.users.lockoutDuration" type="number" min="5" />
        </div>
      </section>

      <!-- Monitoring -->
      <section class="settings-section">
        <h2>System Monitoring</h2>
        <div class="form-group">
          <label>
            <input v-model="settings.monitoring.enableAuditLog" type="checkbox" />
            Enable Audit Logging
          </label>
        </div>
        <div class="form-group">
          <label>Log retention period (days)</label>
          <input v-model="settings.monitoring.logRetentionDays" type="number" min="30" />
        </div>
      </section>
    </div>

    <div class="settings-actions">
      <button @click="saveSettings" class="btn-primary">Save Changes</button>
      <button @click="resetSettings" class="btn-secondary">Reset</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'AdminSettings',
  
  setup() {
    const store = useStore();
    const timezones = [
      'UTC',
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Paris',
      // Add more timezones as needed
    ];
    
    const settings = ref({
      system: {
        name: 'RespiraSense',
        timezone: 'UTC',
        sessionTimeout: 30
      },
      security: {
        minPasswordLength: 12,
        requireMFA: true,
        enforcePasswordHistory: true
      },
      users: {
        autoApprove: false,
        maxLoginAttempts: 5,
        lockoutDuration: 30
      },
      monitoring: {
        enableAuditLog: true,
        logRetentionDays: 90
      }
    });

    onMounted(async () => {
      try {
        const adminSettings = await store.dispatch('admin/fetchSettings');
        if (adminSettings) {
          settings.value = { ...settings.value, ...adminSettings };
        }
      } catch (error) {
        console.error('Failed to load admin settings:', error);
      }
    });

    const saveSettings = async () => {
      try {
        await store.dispatch('admin/updateSettings', settings.value);
      } catch (error) {
        console.error('Failed to save admin settings:', error);
      }
    };

    const resetSettings = () => {
      onMounted();
    };

    return {
      settings,
      timezones,
      saveSettings,
      resetSettings
    };
  }
};
</script>

<style scoped>
/* Same base styles as other settings components */
</style>