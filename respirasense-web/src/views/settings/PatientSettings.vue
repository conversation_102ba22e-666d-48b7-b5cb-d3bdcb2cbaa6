<template>
  <div class="patient-settings">
    <h2>Patient Settings</h2>
    
    <div class="settings-section">
      <h3>Profile Information</h3>
      <form @submit.prevent="saveSettings">
        <div class="form-group">
          <label>Full Name</label>
          <input 
            v-model="settings.profile.name" 
            type="text" 
            placeholder="Enter your full name"
          >
        </div>

        <div class="form-group">
          <label>Date of Birth</label>
          <input 
            v-model="settings.profile.dob" 
            type="date"
          >
        </div>

        <div class="form-group">
          <label>Emergency Contact</label>
          <input 
            v-model="settings.profile.emergencyContact" 
            type="text" 
            placeholder="Emergency contact number"
          >
        </div>
      </form>
    </div>

    <div class="settings-section">
      <h3>Notifications</h3>
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            v-model="settings.notifications.email"
          >
          Receive email notifications
        </label>
      </div>
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            v-model="settings.notifications.sms"
          >
          Receive SMS notifications
        </label>
      </div>
    </div>

    <div class="settings-section">
      <h3>Data Sharing</h3>
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            v-model="settings.sharing.allowPractitioners"
          >
          Share data with my healthcare practitioners
        </label>
      </div>
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            v-model="settings.sharing.allowResearch"
          >
          Allow anonymous data use for research
        </label>
      </div>
    </div>

    <div class="settings-section">
      <h3>Device Settings</h3>
      <div class="form-group">
        <label>Device ID</label>
        <input 
          v-model="settings.device.id" 
          type="text" 
          placeholder="Enter device ID"
        >
      </div>
      <div class="form-group">
        <label>Measurement Units</label>
        <select v-model="settings.device.units">
          <option value="metric">Metric</option>
          <option value="imperial">Imperial</option>
        </select>
      </div>
    </div>

    <div class="form-actions">
      <button 
        @click="saveSettings" 
        class="btn-save"
        :disabled="saving"
      >
        {{ saving ? 'Saving...' : 'Save Changes' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'PatientSettings',
  setup() {
    const store = useStore();
    const saving = ref(false);
    const settings = ref({
      profile: {
        name: '',
        dob: '',
        emergencyContact: ''
      },
      notifications: {
        email: true,
        sms: false
      },
      sharing: {
        allowPractitioners: true,
        allowResearch: false
      },
      device: {
        id: '',
        units: 'metric'
      }
    });

    const saveSettings = async () => {
      try {
        saving.value = true;
        await store.dispatch('patient/updateSettings', settings.value);
        alert('Settings saved successfully');
      } catch (error) {
        console.error('Failed to save settings:', error);
        alert('Failed to save settings');
      } finally {
        saving.value = false;
      }
    };

    onMounted(async () => {
      try {
        const userSettings = await store.dispatch('patient/fetchSettings');
        if (userSettings) {
          settings.value = { ...settings.value, ...userSettings };
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    });

    return {
      settings,
      saving,
      saveSettings
    };
  }
}
</script>

<style lang="scss" scoped>
.patient-settings {
  max-width: 800px;
  margin: 0 auto;
}

.settings-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
  }
}

.form-group {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    color: #555;
  }

  input[type="text"],
  input[type="date"],
  select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #007bff;
    }
  }
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;

  input[type="checkbox"] {
    margin: 0;
  }
}

.form-actions {
  margin-top: 30px;
  text-align: right;
}

.btn-save {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #0056b3;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}
</style>