<template>
  <div class="home">
    <h1>Welcome to RespiraSense</h1>
    <div class="dashboard">
      <div class="stats-container">
        <div class="stat-card">
          <h3>Total Patients</h3>
          <p>{{ totalPatients }}</p>
        </div>
        <div class="stat-card">
          <h3>Active Monitors</h3>
          <p>{{ activeMonitors }}</p>
        </div>
        <div class="stat-card">
          <h3>Alerts Today</h3>
          <p>{{ alertsToday }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      totalPatients: 0,
      activeMonitors: 0,
      alertsToday: 0
    }
  },
  async created() {
    // TODO: Implement dashboard metrics
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.dashboard {
  margin-top: 20px;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
}

.stat-card p {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
</style>