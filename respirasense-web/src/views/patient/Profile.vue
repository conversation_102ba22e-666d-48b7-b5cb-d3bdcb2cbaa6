<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="profile-page">
        <div class="dashboard-header">
          <div class="header-left">
            <button @click="goBack" class="back-btn">
              <i class="fas fa-arrow-left"></i> Back to Dashboard
            </button>
            <h1>Manage Account</h1>
          </div>
        </div>

        <!-- Tabs Navigation -->
        <div class="profile-tabs">
          <div class="tab-header">
            <button
              @click="activeTab = 'profile'"
              class="tab-button"
              :class="{ active: activeTab === 'profile' }"
            >
              <i class="fas fa-user"></i> Profile
            </button>
            <button
              @click="activeTab = 'practitioners'"
              class="tab-button"
              :class="{ active: activeTab === 'practitioners' }"
            >
              <i class="fas fa-user-md"></i> My Practitioners
            </button>
          </div>

          <!-- Profile Tab -->
          <div v-if="activeTab === 'profile'" class="tab-content">
            <div class="profile-section">
              <div class="profile-form">
                <h3>Personal Information</h3>
                <form @submit.prevent="updateProfile">
                  <div class="profile-info">
                    <div class="form-group">
                      <label>Full Name</label>
                      <input
                        v-model="profile.name"
                        type="text"
                        placeholder="Enter your full name"
                        required
                      >
                    </div>

                    <div class="form-group">
                      <label>Email</label>
                      <input
                        v-model="profile.email"
                        type="email"
                        disabled
                      >
                    </div>

                    <div class="form-group">
                      <label>Age</label>
                      <input
                        v-model="profile.age"
                        type="number"
                        placeholder="Enter your age"
                        required
                      >
                    </div>

                    <div class="form-group">
                      <label>Height (cm)</label>
                      <input
                        v-model="profile.height"
                        type="number"
                        placeholder="Enter your height in cm"
                        required
                      >
                    </div>

                    <div class="form-group">
                      <label>Weight (kg)</label>
                      <input
                        v-model="profile.weight"
                        type="number"
                        placeholder="Enter your weight in kg"
                        required
                      >
                    </div>

                    <div class="form-actions">
                      <button type="submit" class="btn-primary">
                        Save Changes
                      </button>
                    </div>
                  </div>
                </form>
              </div>

              <div class="danger-zone">
                <h3>Danger Zone</h3>
                <button @click="confirmDeleteAccount" class="btn-delete">
                  Delete Account
                </button>
              </div>
            </div>
          </div>

          <!-- Practitioners Tab -->
          <div v-if="activeTab === 'practitioners'" class="tab-content">
            <PatientPractitionerContacts />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref } from 'vue';
import AppHeader from '@/components/layout/Header.vue';
import PatientPractitionerContacts from '@/components/patient/PatientPractitionerContacts.vue';
import { useProfile } from './composables/useProfile';

export default {
  name: 'Profile',
  components: {
    AppHeader,
    PatientPractitionerContacts
  },
  setup() {
    // Check if there's an activeTab in localStorage (set from Dashboard)
    const savedTab = localStorage.getItem('profileActiveTab');
    const activeTab = ref(savedTab || 'profile');

    // Clear the localStorage value after using it
    if (savedTab) {
      localStorage.removeItem('profileActiveTab');
    }
    const profileData = useProfile();

    return {
      ...profileData,
      activeTab
    };
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/views/patient/Profile.scss";
</style>
