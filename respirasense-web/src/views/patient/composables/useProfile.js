import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { auth } from '@/plugins/firebase/firebase';
import request from '@/Rest';
import { notify } from '@kyvg/vue3-notification';
import { deleteUser } from '@/utils/user-management';

/**
 * Composable for profile functionality
 * @returns {Object} Profile utility functions and state
 */
export function useProfile() {
  const router = useRouter();
  const store = useStore();

  // State
  const profile = ref({
    name: '',
    email: '',
    age: '',
    height: '',
    weight: ''
  });

  /**
   * Navigate back to the appropriate dashboard
   */
  const goBack = () => {
    const userRole = localStorage.getItem('userRole');
    const dashboardRoutes = {
      'admin': '/admin/dashboard',
      'superAdmin': '/admin/dashboard',
      'practitioner': '/practitioner/dashboard',
      'patient': '/patient/dashboard'
    };

    const dashboardPath = dashboardRoutes[userRole];
    if (dashboardPath) {
      router.push(dashboardPath);
    }
  };

  /**
   * Load user profile data
   */
  const loadProfile = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      const patientDoc = await request.GET(`patients/${user.uid}`).Execute();
      if (patientDoc.exists()) {
        const data = patientDoc.data();
        profile.value = {
          name: data.name || '',
          email: data.email || user.email,
          age: data.age || '',
          height: data.height || '',
          weight: data.weight || ''
        };
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      notify({
        type: 'error',
        text: 'Failed to load profile data'
      });
    }
  };

  /**
   * Update user profile
   */
  const updateProfile = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      await request.POST(`patients/${user.uid}`, {
        data: {
          ...profile.value,
          updatedAt: new Date().toISOString()
        }
      }).Execute();

      notify({
        type: 'success',
        text: 'Profile updated successfully'
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      notify({
        type: 'error',
        text: 'Failed to update profile'
      });
    }
  };

  /**
   * Confirm and delete user account
   */
  const confirmDeleteAccount = async () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        const user = auth.currentUser;
        if (!user) {
          notify({
            type: 'error',
            text: 'You must be logged in to delete your account'
          });
          return;
        }

        // Use the enhanced deleteUser utility function
        // Pass false for isAdmin parameter since this is a self-delete
        await deleteUser(user.uid, false);

        // Log out the user
        await store.dispatch('authFirebase/Logout');

        // Redirect to login page
        router.push('/auth/login');

        // Show success notification
        notify({
          type: 'success',
          text: 'Your account has been successfully deleted'
        });
      } catch (error) {
        console.error('Error deleting account:', error);

        // Check if this is a re-authentication error
        if (error.message && error.message.includes('re-authenticate')) {
          notify({
            type: 'error',
            text: 'You need to log out and log back in before deleting your account'
          });
        } else {
          notify({
            type: 'error',
            text: `Failed to delete account: ${error.message || 'Unknown error'}`
          });
        }
      }
    }
  };

  // Initialize data
  onMounted(loadProfile);

  return {
    profile,
    goBack,
    updateProfile,
    confirmDeleteAccount
  };
}
