import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';  // Import autoTable function directly
import { useFormatters } from '@/composables/useFormatters';
import { auth } from '@/plugins/firebase/firebase';
import request from '@/Rest';
import logoImg from '@/assets/LogoRespiraSense.png';

/**
 * Composable for detailed report functionality
 * @returns {Object} Detailed report utility functions and state
 */
export function useDetailedReport() {
  const store = useStore();
  const router = useRouter();
  const { formatDecimal, formatDate, formatDateTime } = useFormatters();

  // State
  const loading = ref(false);
  const error = ref(null);
  const fromDate = ref('');
  const toDate = ref('');
  const report = ref({
    readings: [],
    statistics: {
      averages: {},
      trends: {},
      anomalies: []
    }
  });

  // Filters
  const filters = ref({
    copdStatus: 'all',
    threshold: 'all'
  });

  /**
   * Fetch report data
   */
  const fetchReport = async () => {
    try {
      // Reset data and set loading state
      loading.value = true;
      error.value = null;

      // Reset report data to avoid displaying stale data
      report.value = {
        readings: [],
        statistics: {
          averages: {},
          trends: {},
          anomalies: []
        }
      };

      console.log(`Fetching report data from ${fromDate.value} to ${toDate.value}`);
      console.log('Store modules:', Object.keys(store._modules.root._children));
      console.log('Patient module children:', Object.keys(store._modules.root._children.patient._children));

      // Validate date range
      const startDate = new Date(fromDate.value);
      const endDate = new Date(toDate.value);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error('Invalid date range:', fromDate.value, toDate.value);
        error.value = 'Please select a valid date range';
        loading.value = false;
        return;
      }

      if (startDate > endDate) {
        console.error('Start date is after end date');
        error.value = 'Start date must be before end date';
        loading.value = false;
        return;
      }

      // Fetch data with a timeout to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 10000);
      });

      console.log('Dispatching patient/health/fetchDetailedReport action');
      const fetchPromise = store.dispatch('patient/health/fetchDetailedReport', {
        from: fromDate.value,
        to: toDate.value
      });

      const data = await Promise.race([fetchPromise, timeoutPromise])
        .catch(err => {
          console.error('Error or timeout in fetchDetailedReport:', err);
          error.value = 'Failed to load report data: ' + (err.message || 'Request timeout');
          return null;
        });

      console.log('Received data from fetchDetailedReport:', data);

      if (!data) {
        console.error('No data returned from fetchDetailedReport');
        loading.value = false;
        return;
      }

      if (!data.readings || !Array.isArray(data.readings)) {
        console.warn('Invalid data received from fetchDetailedReport:', data);
        error.value = 'No valid data available for the selected period';
        loading.value = false;
        return;
      }

      console.log(`Received ${data.readings.length} readings for the report`);
      console.log('Raw readings:', data.readings);

      // Log detailed timestamp information for debugging
      data.readings.forEach((r, i) => {
        console.log(`[${i}] timestamp:`, r.timestamp, 'lastUpdated:', r.lastUpdated,
          'respiratoryRate:', r.respiratoryRate, 'oxygenSaturation:', r.oxygenSaturation);
      });

      // Normalize timestamp function
      const normalizeTime = (timestamp) => {
        if (!timestamp) return null;

        if (timestamp instanceof Date) return timestamp.getTime();
        if (typeof timestamp === 'object' && timestamp.seconds) return timestamp.seconds * 1000;
        if (typeof timestamp === 'object' && timestamp._seconds) return timestamp._seconds * 1000;
        if (typeof timestamp === 'string') {
          const parsedDate = new Date(timestamp);
          return isNaN(parsedDate.getTime()) ? null : parsedDate.getTime();
        }
        return null;
      };

      // Validate readings with more lenient timestamp handling
      const validReadings = data.readings.filter(reading => {
        if (!reading) return false;

        // Check if reading has a valid timestamp
        const hasValidTimestamp = normalizeTime(reading.timestamp) || normalizeTime(reading.lastUpdated);

        // Log invalid readings for debugging
        if (!hasValidTimestamp) {
          console.warn('Invalid timestamp in reading:', reading);
        }

        return !!hasValidTimestamp;
      });

      console.log(`Found ${validReadings.length} valid readings out of ${data.readings.length}`);

      if (validReadings.length === 0) {
        console.warn('No valid readings in the data');
        error.value = 'No readings found for the selected period';
        loading.value = false;
        return;
      }

      // Update report with valid data
      report.value = {
        ...data,
        readings: validReadings
      };

      console.log('Report data updated successfully');
    } catch (err) {
      error.value = 'Failed to load report data';
      console.error('Error fetching report data:', err);
    } finally {
      loading.value = false;
    }
  };

  /**
   * Format metric name
   * @param {string} metric - The metric key
   * @returns {string} Formatted metric name
   */
  const formatMetricName = (metric) => {
    const names = {
      respiratoryRate: 'Respiratory Rate',
      oxygenSaturation: 'Oxygen Saturation',
      heartRate: 'Heart Rate',
      temperature: 'Temperature'
    };
    return names[metric] || metric;
  };

  /**
   * Format metric value with units
   * @param {string} metric - The metric key
   * @param {number} value - The metric value
   * @returns {string} Formatted metric value with units
   */
  const formatMetricValue = (metric, value) => {
    if (value === undefined || value === null) return '--';

    const units = {
      respiratoryRate: 'bpm',
      oxygenSaturation: '%',
      heartRate: 'bpm',
      temperature: '°C'
    };
    return `${formatDecimal(value)} ${units[metric] || ''}`;
  };

  /**
   * Format risk level
   * @param {string} riskLevel - The risk level
   * @returns {string} Formatted risk level
   */
  const formatRiskLevel = (riskLevel) => {
    if (!riskLevel) return 'Normal';

    switch (riskLevel.toLowerCase()) {
      case 'normal':
        return 'Normal';
      case 'warning':
        return 'Warning';
      case 'danger':
        return 'Danger';
      default:
        return riskLevel;
    }
  };

  /**
   * Get CSS class for metric value based on thresholds
   * @param {string} metric - The metric key
   * @param {number} value - The metric value
   * @returns {string} CSS class
   */
  const getMetricClass = (metric, value) => {
    if (value === undefined || value === null) return '';

    switch (metric) {
      case 'respiratoryRate':
        if (value > 20) return 'metric-high';
        if (value < 12) return 'metric-low';
        break;
      case 'oxygenSaturation':
        if (value < 95) return 'metric-low';
        break;
      case 'heartRate':
        if (value > 100) return 'metric-high';
        if (value < 60) return 'metric-low';
        break;
      case 'temperature':
        if (value > 37.5) return 'metric-high';
        break;
    }

    return '';
  };

  /**
   * Get latest readings
   * @param {number} count - Number of readings to return
   * @returns {Array} Latest readings
   */
  const getLatestReadings = (count) => {
    if (!filteredReadings.value || !filteredReadings.value.length) return [];

    // Normalize timestamp function (same as in fetchReport)
    const normalizeTime = (timestamp) => {
      if (!timestamp) return null;

      if (timestamp instanceof Date) return timestamp.getTime();
      if (typeof timestamp === 'object' && timestamp.seconds) return timestamp.seconds * 1000;
      if (typeof timestamp === 'object' && timestamp._seconds) return timestamp._seconds * 1000;
      if (typeof timestamp === 'string') {
        const parsedDate = new Date(timestamp);
        return isNaN(parsedDate.getTime()) ? null : parsedDate.getTime();
      }
      return null;
    };

    // Sort readings by timestamp (newest first)
    const sortedReadings = [...filteredReadings.value].sort((a, b) => {
      const timeA = normalizeTime(a.timestamp) || normalizeTime(a.lastUpdated) || 0;
      const timeB = normalizeTime(b.timestamp) || normalizeTime(b.lastUpdated) || 0;
      return timeB - timeA; // Descending order
    });

    // Return the specified number of readings
    return sortedReadings.slice(0, count);
  };

  /**
   * Filter readings based on selected filters
   */
  const filteredReadings = computed(() => {
    if (!report.value.readings) return [];

    return report.value.readings.filter(reading => {
      // Filter by COPD status
      if (filters.value.copdStatus !== 'all' && reading.riskLevel !== filters.value.copdStatus) {
        return false;
      }

      // Filter by threshold
      if (filters.value.threshold !== 'all') {
        switch (filters.value.threshold) {
          case 'respiratory':
            return reading.respiratoryRate > 20 || reading.respiratoryRate < 12;
          case 'oxygen':
            return reading.oxygenSaturation < 95;
          case 'heart':
            return reading.heartRate > 100 || reading.heartRate < 60;
          case 'temperature':
            return reading.temperature > 37.5 || reading.temperature < 36;
          default:
            return true;
        }
      }

      return true;
    });
  });

  /**
   * Apply filters
   */
  const applyFilters = () => {
    // This function is called when filters change
    // The filteredReadings computed property will update automatically
  };

  /**
   * Generate chart data
   * @param {string} metric - The metric key
   * @returns {Array} Chart data
   */
  const getChartData = (metric) => {
    // Return empty array if no readings
    if (!report.value.readings || !report.value.readings.length) {
      console.log(`No readings available for ${metric}`);
      return [];
    }

    try {
      // Create a copy of readings to avoid modifying the original
      const readings = [...report.value.readings];

      // Normalize timestamp function (same as in fetchReport)
      const normalizeTime = (timestamp) => {
        if (!timestamp) return null;

        if (timestamp instanceof Date) return timestamp.getTime();
        if (typeof timestamp === 'object' && timestamp.seconds) return timestamp.seconds * 1000;
        if (typeof timestamp === 'object' && timestamp._seconds) return timestamp._seconds * 1000;
        if (typeof timestamp === 'string') {
          const parsedDate = new Date(timestamp);
          return isNaN(parsedDate.getTime()) ? null : parsedDate.getTime();
        }
        return null;
      };

      // Process readings to extract valid data points
      const validReadings = readings.filter(reading => {
        // Check if reading has the metric
        if (reading[metric] === undefined || reading[metric] === null) {
          return false;
        }

        // Check if reading has a valid timestamp
        const hasValidTimestamp = normalizeTime(reading.timestamp) || normalizeTime(reading.lastUpdated);

        if (!hasValidTimestamp) {
          console.warn(`Invalid timestamp in reading for ${metric}:`, reading);
        }

        return !!hasValidTimestamp;
      });

      if (validReadings.length === 0) {
        console.log(`No valid readings found for ${metric}`);
        return [];
      }

      // Sort readings by timestamp
      validReadings.sort((a, b) => {
        const timeA = normalizeTime(a.timestamp) || normalizeTime(a.lastUpdated) || 0;
        const timeB = normalizeTime(b.timestamp) || normalizeTime(b.lastUpdated) || 0;
        return timeA - timeB;
      });

      // Map readings to chart data points
      const data = validReadings.map(reading => {
        // Get timestamp using normalizeTime
        const timestamp = reading.timestamp || reading.lastUpdated;
        const dateTime = normalizeTime(timestamp) || Date.now();

        // Get value and ensure it's a valid number
        let value = parseFloat(reading[metric]);

        // Check if value is a valid number
        if (isNaN(value)) {
          console.warn(`Invalid value for ${metric} in reading:`, reading);
          value = 0; // Default to 0 or some other sensible default
        }

        return {
          x: dateTime,
          y: value
        };
      });

      // Log data for debugging
      console.log(`Generated ${data.length} data points for ${metric}`);

      // Return data in the format expected by the chart component
      return [{
        name: formatMetricName(metric),
        data: data
      }];
    } catch (error) {
      console.error(`Error generating chart data for ${metric}:`, error);
      return [];
    }
  };

  /**
   * Generate risk distribution data for pie chart
   * @returns {Array} Risk distribution data
   */
  const getRiskDistribution = () => {
    // Return empty array if no readings
    if (!report.value.readings || !report.value.readings.length) {
      console.log('No readings available for risk distribution');
      return [];
    }

    try {
      // Count readings by risk level
      const riskCounts = {
        normal: 0,
        warning: 0,
        danger: 0
      };

      // Count readings by risk level
      report.value.readings.forEach(reading => {
        const riskLevel = reading.riskLevel || 'normal';
        if (riskCounts[riskLevel] !== undefined) {
          riskCounts[riskLevel]++;
        } else {
          riskCounts[riskLevel] = 1;
        }
      });

      // Convert to array format for pie chart
      const data = [
        { label: 'Normal', value: riskCounts.normal },
        { label: 'Warning', value: riskCounts.warning },
        { label: 'Danger', value: riskCounts.danger }
      ].filter(item => item.value > 0); // Remove empty categories

      // Log data for debugging
      console.log('Generated risk distribution data:', data);

      return data;
    } catch (error) {
      console.error('Error generating risk distribution data:', error);
      return [];
    }
  };

  /**
   * Get patient profile data
   * @returns {Promise<Object>} Patient profile data
   */
  const getPatientProfile = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return null;

      const patientDoc = await request.GET(`patients/${user.uid}`).Execute();
      if (patientDoc.exists()) {
        return patientDoc.data();
      }
      return null;
    } catch (error) {
      console.error('Error loading profile:', error);
      return null;
    }
  };

  /**
   * Helper function to load an image as base64
   * @param {string} url - Image URL
   * @returns {Promise<string>} Base64 encoded image
   */
  const loadImage = (url) => new Promise((resolve, reject) => {
    if (!url) {
      reject(new Error('No image URL provided'));
      return;
    }

    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      canvas.getContext('2d').drawImage(img, 0, 0);
      resolve(canvas.toDataURL('image/jpeg'));
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    img.src = url;
  });

  /**
   * Helper function to capture a chart as an image
   * @param {string} selector - Chart canvas selector
   * @returns {string|null} Base64 encoded image or null if chart not found
   */
  const captureChart = (selector) => {
    try {
      const chartCanvas = document.querySelector(selector);
      if (!chartCanvas) return null;
      return chartCanvas.toDataURL('image/png');
    } catch (error) {
      console.error('Error capturing chart:', error);
      return null;
    }
  };

  /**
   * Generate and download PDF report
   */
  const downloadPdf = async () => {
    // Create PDF document
    const doc = new jsPDF();

    // Get patient profile data
    const profileData = await getPatientProfile();

    // ===== PAGE 1: HEADER, SUMMARY, CHARTS =====
    let yPos = 10;

    // Add RespiraSense logo (left-aligned)
    try {
      doc.addImage(logoImg, 'PNG', 14, yPos, 40, 20);
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
    }

    // Add title and date range (center-aligned)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(20);
    doc.setTextColor(0, 0, 0);
    doc.text('Detailed Health Report', 105, yPos + 10, { align: 'center' });

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text(`Report Period: ${formatDate(fromDate.value)} to ${formatDate(toDate.value)}`, 105, yPos + 18, { align: 'center' });

    // Try to add patient profile image (right-aligned)
    if (profileData && profileData.photoURL) {
      try {
        const profileImage = await loadImage(profileData.photoURL);
        doc.addImage(profileImage, 'JPEG', 160, yPos, 35, 35);
      } catch (error) {
        console.error('Error adding profile image:', error);
      }
    }

    // Add patient details section
    yPos = 40;
    doc.setDrawColor(183, 21, 64);
    doc.setLineWidth(0.5);
    doc.line(14, yPos, 196, yPos);

    yPos += 10;
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Patient Information', 14, yPos);

    yPos += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    // Create two-column layout for patient info
    const leftCol = 20;
    const rightCol = 105;

    if (profileData) {
      doc.text(`Name: ${profileData.name || 'Not provided'}`, leftCol, yPos);
      doc.text(`Email: ${profileData.email || 'Not provided'}`, rightCol, yPos);
      yPos += 6;

      if (profileData.age) {
        doc.text(`Age: ${profileData.age}`, leftCol, yPos);
      }

      if (profileData.height) {
        doc.text(`Height: ${profileData.height} cm`, rightCol, yPos);
      }
      yPos += 6;

      if (profileData.weight) {
        doc.text(`Weight: ${profileData.weight} kg`, leftCol, yPos);
      }

      // Add BMI if height and weight are available
      if (profileData.height && profileData.weight) {
        const heightInMeters = profileData.height / 100;
        const bmi = (profileData.weight / (heightInMeters * heightInMeters)).toFixed(1);
        doc.text(`BMI: ${bmi}`, rightCol, yPos);
      }
    } else {
      doc.text('Patient details not available', leftCol, yPos);
    }

    // Add report generation date
    yPos += 10;
    doc.text(`Report Generated: ${format(new Date(), 'MMMM d, yyyy')}`, leftCol, yPos);

    // Add divider
    yPos += 5;
    doc.setDrawColor(200, 200, 200);
    doc.line(14, yPos, 196, yPos);

    // Add summary statistics
    yPos += 10;
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Summary Statistics', 14, yPos);

    yPos += 8;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    // Create two-column layout for statistics
    const metrics = Object.entries(report.value.statistics.averages);
    const midpoint = Math.ceil(metrics.length / 2);

    for (let i = 0; i < midpoint; i++) {
      const [metric, value] = metrics[i];
      doc.text(`Average ${formatMetricName(metric)}: ${formatMetricValue(metric, value)}`, leftCol, yPos);

      // Add right column if there's a corresponding item
      if (i + midpoint < metrics.length) {
        const [rightMetric, rightValue] = metrics[i + midpoint];
        doc.text(`Average ${formatMetricName(rightMetric)}: ${formatMetricValue(rightMetric, rightValue)}`, rightCol, yPos);
      }

      yPos += 6;
    }

    // Add divider
    yPos += 5;
    doc.setDrawColor(200, 200, 200);
    doc.line(14, yPos, 196, yPos);

    // Add charts section
    yPos += 10;
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Health Metrics Trends', 14, yPos);
    yPos += 10;

    // Try to capture charts from the DOM
    // Note: This will only work if the charts are rendered in the DOM
    // For each chart type, try to capture and add to PDF
    const chartSelectors = [
      { name: 'Respiratory Rate', selector: 'canvas[data-metric="respiratoryRate"]' },
      { name: 'Oxygen Saturation', selector: 'canvas[data-metric="oxygenSaturation"]' },
      { name: 'Heart Rate', selector: 'canvas[data-metric="heartRate"]' },
      { name: 'Temperature', selector: 'canvas[data-metric="temperature"]' },
      { name: 'Risk Distribution', selector: 'canvas[data-metric="riskDistribution"]' }
    ];

    // Add note about charts
    doc.setFont('helvetica', 'italic');
    doc.setFontSize(10);
    doc.text('Note: For detailed charts, please view the online report.', 105, yPos, { align: 'center' });
    yPos += 15;

    // ===== PAGE 2: READINGS TABLE =====
    // Start a new page for the readings table
    doc.addPage();

    // Add table header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.text('Detailed Readings Data', 14, 20);

    // Prepare table data
    const tableData = filteredReadings.value.map(reading => [
      formatDateTime(reading.timestamp || reading.lastUpdated),
      `${formatDecimal(reading.respiratoryRate)} bpm`,
      `${formatDecimal(reading.oxygenSaturation)}%`,
      `${formatDecimal(reading.heartRate)} bpm`,
      `${formatDecimal(reading.temperature)}°C`,
      reading.riskLevel.toUpperCase()
    ]);

    // Add the table
    autoTable(doc, {
      startY: 30,
      head: [['Date & Time', 'Respiratory Rate', 'Oxygen Saturation', 'Heart Rate', 'Temperature', 'COPD Risk']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: [183, 21, 64], textColor: [255, 255, 255] },
      styles: { fontSize: 9 },
      columnStyles: {
        0: { cellWidth: 40 },
        1: { cellWidth: 30 },
        2: { cellWidth: 30 },
        3: { cellWidth: 30 },
        4: { cellWidth: 30 },
        5: { cellWidth: 30 }
      }
    });

    // Add footer with RespiraSense branding on all pages
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text('RespiraSense Health Report - Confidential Medical Information', 105, doc.internal.pageSize.height - 10, { align: 'center' });
      doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 5, { align: 'center' });
    }

    // Save the PDF
    const fileName = profileData?.name
      ? `${profileData.name.replace(/\s+/g, '_')}_health_report_${format(new Date(), 'yyyy-MM-dd')}.pdf`
      : `health_report_${format(new Date(), 'yyyy-MM-dd')}.pdf`;

    doc.save(fileName);
  };

  /**
   * Navigate back to the patient dashboard
   */
  const goBack = () => {
    router.push('/patient/dashboard');
  };

  // Initialize data
  onMounted(() => {
    // Set default date range to last 30 days
    const today = new Date();
    toDate.value = format(today, 'yyyy-MM-dd');
    fromDate.value = format(new Date(today.setMonth(today.getMonth() - 1)), 'yyyy-MM-dd');
    fetchReport();
  });

  return {
    loading,
    error,
    fromDate,
    toDate,
    report,
    filters,
    filteredReadings,
    fetchReport,
    formatMetricName,
    formatMetricValue,
    formatRiskLevel,
    formatDate,
    formatDateTime,
    formatDecimal,
    applyFilters,
    getChartData,
    getRiskDistribution,
    getMetricClass,
    getLatestReadings,
    downloadPdf,
    goBack
  };
}
