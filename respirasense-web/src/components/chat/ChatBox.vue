<template>
  <div class="chat-box">
    <div class="chat-messages" ref="messagesContainer">
      <div v-if="loading" class="loading-container">
        <div class="spinner"></div>
        <p>Loading messages...</p>
      </div>

      <div v-else-if="messages.length === 0" class="empty-state">
        <i class="fas fa-comment-dots"></i>
        <p>No messages yet</p>
        <p class="empty-state-subtitle">Start the conversation by sending a message</p>
      </div>

      <template v-else>
        <div
          v-for="(message, index) in messages"
          :key="message.id"
          class="message"
          :class="{
            'sent': message.senderId === currentUserId,
            'received': message.senderId !== currentUserId,
            'first-in-group': isFirstInGroup(message, index),
            'last-in-group': isLastInGroup(message, index)
          }"
        >
          <div class="message-content">
            <p>{{ message.text }}</p>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
        </div>
      </template>
    </div>

    <div class="chat-input">
      <textarea
        v-model="newMessage"
        placeholder="Type your message..."
        @keydown.enter.prevent="sendMessage"
      ></textarea>
      <button @click="sendMessage" :disabled="!newMessage.trim() || sending" class="send-btn">
        <i class="fas fa-paper-plane"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { collection, query, orderBy, limit, onSnapshot } from 'firebase/firestore';
import { db, auth } from '@/plugins/firebase/firebase';
import { format } from 'date-fns';

export default {
  name: 'ChatBox',

  props: {
    practitionerId: {
      type: String,
      required: true
    },
    practitionerName: {
      type: String,
      required: true
    }
  },

  setup(props) {
    const store = useStore();
    const newMessage = ref('');
    const sending = ref(false);
    const messagesContainer = ref(null);
    const unsubscribe = ref(null);
    const currentUserId = computed(() => auth.currentUser?.uid);

    // Get state from store
    const loading = computed(() => store.getters['patient/isLoading']);
    const chatId = computed(() => {
      const ids = [currentUserId.value, props.practitionerId].sort();
      return ids.join('_');
    });
    const messages = computed(() => store.getters['patient/getChatMessages'](chatId.value) || []);

    // Format time
    const formatTime = (timestamp) => {
      if (!timestamp) return '';

      if (typeof timestamp === 'object' && timestamp.seconds) {
        return format(new Date(timestamp.seconds * 1000), 'h:mm a');
      }

      return format(new Date(timestamp), 'h:mm a');
    };

    // Check if message is first in a group
    const isFirstInGroup = (message, index) => {
      if (index === 0) return true;
      const prevMessage = messages.value[index - 1];
      return prevMessage.senderId !== message.senderId;
    };

    // Check if message is last in a group
    const isLastInGroup = (message, index) => {
      if (index === messages.value.length - 1) return true;
      const nextMessage = messages.value[index + 1];
      return nextMessage.senderId !== message.senderId;
    };

    // Scroll to bottom of messages
    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
      });
    };

    // Send a new message
    const sendMessage = async () => {
      if (!newMessage.value.trim() || sending.value) return;

      try {
        sending.value = true;

        // Send message using store action
        const result = await store.dispatch('patient/send_chat_message', {
          practitionerId: props.practitionerId,
          message: newMessage.value.trim()
        });

        if (result.success) {
          // Clear the input
          newMessage.value = '';
        }

      } catch (err) {
        console.error('Error sending message:', err);
      } finally {
        sending.value = false;
      }
    };

    // Subscribe to messages
    const subscribeToMessages = () => {
      // Fetch messages using store action
      store.dispatch('patient/fetch_chat_messages', props.practitionerId);

      // Set up real-time listener for new messages
      const ids = [currentUserId.value, props.practitionerId].sort();
      const chatRoomId = ids.join('_');

      // Subscribe to the query using onSnapshot
      const messagesRef = collection(db, `chats/${chatRoomId}/messages`);
      const messagesQuery = query(messagesRef, orderBy('timestamp', 'asc'), limit(100));

      unsubscribe.value = onSnapshot(
        messagesQuery,
        (snapshot) => {
          const newMessages = [];

          snapshot.forEach((doc) => {
            const data = doc.data();
            newMessages.push({
              id: doc.id,
              ...data,
              timestamp: data.timestamp?.toDate() || new Date()
            });
          });

          // Update store with new messages
          store.commit('patient/SET_CHAT_MESSAGES', {
            chatId: chatRoomId,
            messages: newMessages
          });

          scrollToBottom();
        },
        (error) => {
          console.error('Error subscribing to messages:', error);
          store.commit('patient/SET_ERROR', 'Failed to load messages');
        }
      );
    };

    // Initialize
    onMounted(() => {
      subscribeToMessages();
    });

    // Clean up
    onUnmounted(() => {
      if (unsubscribe.value) {
        unsubscribe.value();
      }
    });

    // Watch for new messages and scroll to bottom
    watch(messages, () => {
      scrollToBottom();
    });

    return {
      messages,
      newMessage,
      loading,
      sending,
      messagesContainer,
      currentUserId,
      formatTime,
      isFirstInGroup,
      isLastInGroup,
      sendMessage
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.chat-box {
  display: flex;
  flex-direction: column;
  height: 500px;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
  overflow: hidden;

  .chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .spinner {
        width: 36px;
        height: 36px;
        border: 3px solid rgba($primary, 0.2);
        border-radius: 50%;
        border-top-color: $primary;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 12px;
      }

      p {
        color: $dark-grey;
        margin: 0;
        font-size: 0.95rem;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: $dark-grey;

      i {
        font-size: 42px;
        margin-bottom: 16px;
        color: rgba($primary-light, 0.4);
      }

      p {
        margin: 0;
        font-size: 1rem;
        font-weight: 500;
      }

      .empty-state-subtitle {
        margin-top: 8px;
        font-size: 0.9rem;
        color: lighten($dark-grey, 15%);
      }
    }

    .message {
      max-width: 70%;
      margin-bottom: 4px;
      align-self: flex-start;

      &.sent {
        align-self: flex-end;

        .message-content {
          background-color: $primary;
          color: white;
          border-radius: 18px 18px 4px 18px;

          .message-time {
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }

      &.received {
        .message-content {
          background-color: white;
          color: $dark-grey;
          border-radius: 18px 18px 18px 4px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      &.first-in-group {
        margin-top: 12px;
      }

      &.last-in-group {
        margin-bottom: 12px;
      }

      .message-content {
        padding: 10px 14px;
        position: relative;

        p {
          margin: 0 0 16px 0;
          font-size: 0.95rem;
          line-height: 1.4;
          word-break: break-word;
        }

        .message-time {
          position: absolute;
          bottom: 4px;
          right: 10px;
          font-size: 0.7rem;
          color: rgba($dark-grey, 0.6);
        }
      }
    }
  }

  .chat-input {
    display: flex;
    padding: 12px;
    background-color: white;
    border-top: 1px solid #eee;

    textarea {
      flex: 1;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 24px;
      resize: none;
      height: 48px;
      font-size: 0.95rem;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: $primary-light;
      }
    }

    .send-btn {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: $primary;
      color: white;
      border: none;
      margin-left: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: darken($primary, 5%);
        transform: translateY(-2px);
      }

      &:disabled {
        background-color: lighten($primary, 20%);
        cursor: not-allowed;
      }

      i {
        font-size: 1rem;
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .chat-box {
    height: 400px;

    .message {
      max-width: 85%;
    }
  }
}
</style>
