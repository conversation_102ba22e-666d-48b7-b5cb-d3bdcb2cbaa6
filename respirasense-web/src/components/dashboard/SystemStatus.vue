<template>
  <div class="system-status">
    <h3 class="status-title">System Status</h3>
    <div class="status-grid">
      <div v-for="(status, key) in healthStatus" 
           :key="key" 
           class="status-item"
           :class="getStatusClass(status)">
        <div class="status-header">
          <span class="status-name">{{ formatServiceName(key) }}</span>
          <span class="status-indicator"></span>
        </div>
        <div class="status-details">
          <p v-if="status.message">{{ status.message }}</p>
          <p v-if="status.lastCheck">Last checked: {{ formatTime(status.lastCheck) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemStatus',
  props: {
    healthStatus: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatServiceName(name) {
      return name.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    getStatusClass(status) {
      return {
        'status-healthy': status.status === 'healthy',
        'status-warning': status.status === 'warning',
        'status-error': status.status === 'error'
      }
    },
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped lang="scss">
.system-status {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .status-title {
    margin: 0 0 20px 0;
    color: #333;
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .status-item {
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;

    &.status-healthy {
      background: #f0fdf4;
      .status-indicator { background: #22c55e; }
    }

    &.status-warning {
      background: #fefce8;
      .status-indicator { background: #eab308; }
    }

    &.status-error {
      background: #fef2f2;
      .status-indicator { background: #ef4444; }
    }
  }

  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .status-name {
    font-weight: 600;
    color: #374151;
  }

  .status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }

  .status-details {
    color: #6b7280;
    font-size: 0.9em;

    p {
      margin: 5px 0;
    }
  }
}
</style>