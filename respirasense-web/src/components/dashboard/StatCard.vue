<template>
  <div class="stat-card" :class="{ 'warning': isWarning }">
    <div class="stat-icon">
      <i :class="`fas fa-${icon}`"></i>
    </div>
    <div class="stat-content">
      <h3 class="stat-title">{{ title }}</h3>
      <div class="stat-value">{{ value }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    isWarning: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped lang="scss">
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;

  &.warning {
    background: #fff3e0;
    .stat-icon {
      color: #f57c00;
    }
  }
}

.stat-icon {
  font-size: 24px;
  color: #2196f3;
}

.stat-content {
  .stat-title {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}
</style>