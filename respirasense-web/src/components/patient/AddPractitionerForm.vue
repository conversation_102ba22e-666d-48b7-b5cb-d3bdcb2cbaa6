<template>
  <div class="add-practitioner-form-container">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-user-md"></i>
        <h3>Find Your Practitioner</h3>
      </div>
    </div>

    <div v-if="error" class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{{ error }}</p>
    </div>

    <div v-if="successMessage" class="success-message">
      <i class="fas fa-check-circle"></i>
      <p>{{ successMessage }}</p>
    </div>

    <form @submit.prevent="sendRequest" class="add-practitioner-form">
      <div class="form-group">
        <label for="practitionerEmail">Practitioner Email</label>
        <div class="input-wrapper">
          <i class="fas fa-envelope"></i>
          <input
            type="email"
            id="practitionerEmail"
            v-model="practitionerEmail"
            placeholder="Enter practitioner email address"
            required
          >
        </div>
        <small class="form-hint">Enter the email address your healthcare practitioner uses on RespiraSense</small>
      </div>

      <div class="form-actions">
        <button
          type="submit"
          class="action-button"
          :disabled="isSubmitting"
        >
          <i class="fas fa-paper-plane"></i>
          {{ isSubmitting ? 'Sending...' : 'Send Request' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'AddPractitionerForm',

  setup() {
    const store = useStore();
    const practitionerEmail = ref('');
    const isSubmitting = ref(false);
    const error = ref('');
    const successMessage = ref('');

    const sendRequest = async () => {
      try {
        // Reset messages
        error.value = '';
        successMessage.value = '';
        isSubmitting.value = true;

        // Validate email
        const cleanedEmail = practitionerEmail.value.trim().toLowerCase();
        if (!cleanedEmail) {
          error.value = 'Please enter a valid email address';
          return;
        }

        // Send the request
        const result = await store.dispatch('patient/request_practitioner_linking', cleanedEmail);

        if (result && result.success) {
          if (result.message) {
            successMessage.value = result.message;
          } else if (result.practitionerExists) {
            successMessage.value = 'Request sent successfully! The practitioner will be notified.';
          } else {
            successMessage.value = 'Invitation sent! We\'ll notify you when the practitioner joins the platform.';
          }
          practitionerEmail.value = ''; // Clear the form
        } else {
          error.value = (result && result.error) || 'Failed to send request. Please try again.';
        }
      } catch (err) {
        console.error('Error sending practitioner request:', err);
        error.value = err.message || 'An error occurred while sending the request';
      } finally {
        isSubmitting.value = false;
      }
    };

    return {
      practitionerEmail,
      isSubmitting,
      error,
      successMessage,
      sendRequest
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

// Define variables that might not be in the imported settings
$text-dark: #333333;
$text-muted: #777777;
$border-color: #e0e0e0;

.add-practitioner-form-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 24px;
  border-top: 3px solid $primary;

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }
  }

  .add-practitioner-form {
    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: $text-dark;
      }

      .input-wrapper {
        position: relative;

        i {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: $primary;
        }

        input {
          width: 100%;
          padding: 12px 12px 12px 40px;
          border: 1px solid $border-color;
          border-radius: 6px;
          font-size: 1rem;
          transition: border-color 0.2s ease;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 2px rgba($primary, 0.2);
          }
        }
      }

      .form-hint {
        display: block;
        margin-top: 6px;
        font-size: 0.85rem;
        color: $text-muted;
      }
    }

    .form-actions {
      .action-button {
        width: 100%;
        padding: 12px 16px;
        background-color: $primary;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          background-color: darken($primary, 5%);
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        &:disabled {
          background-color: lighten($primary, 20%);
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }

  .error-message, .success-message {
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      font-size: 1.2rem;
    }

    p {
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .error-message {
    background-color: rgba(#ff4757, 0.1);
    color: #ff4757;
    border: 1px solid rgba(#ff4757, 0.2);
  }

  .success-message {
    background-color: rgba(#2ed573, 0.1);
    color: #2ed573;
    border: 1px solid rgba(#2ed573, 0.2);
  }
}

@media (max-width: 768px) {
  .add-practitioner-form-container {
    padding: 16px;
  }
}
</style>
