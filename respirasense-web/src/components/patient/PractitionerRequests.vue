<template>
  <div class="practitioner-requests">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-user-md"></i>
        <h3>Practitioner Requests</h3>
      </div>
    </div>

    <div v-if="loading && loadingLocally" class="loading-container">
      <div class="spinner"></div>
      <p>Loading requests...</p>
    </div>

    <div v-else-if="!requests || requests.length === 0" class="empty-state">
      <i class="fas fa-check-circle"></i>
      <p>No pending practitioner requests</p>
    </div>

    <div v-else class="requests-list">
      <!-- Incoming requests (practitioner-initiated) -->
      <div v-for="request in incomingRequests" :key="request.id" class="request-card incoming">
        <div class="request-info">
          <div class="request-header">
            <h4>
              <i class="fas fa-user-md"></i>
              {{ request.practitionerName }}
            </h4>
            <span class="request-badge incoming">Incoming Request</span>
          </div>
          <div class="request-details">
            <p class="request-message">
              <i class="fas fa-comment-medical"></i>
              This practitioner has requested to link with your account
            </p>
            <p class="request-date">
              <i class="fas fa-calendar-alt"></i>
              Requested on {{ formatDate(request.createdAt) }}
            </p>
          </div>
        </div>
        <div class="request-actions">
          <button @click="approveRequest(request.id)" class="approve-btn">
            <i class="fas fa-check"></i> Approve
          </button>
          <button @click="rejectRequest(request.id)" class="reject-btn">
            <i class="fas fa-times"></i> Decline
          </button>
        </div>
      </div>

      <!-- Outgoing requests (patient-initiated) -->
      <div v-for="request in outgoingRequests" :key="request.id" class="request-card outgoing">
        <div class="request-info">
          <div class="request-header">
            <h4>
              <i class="fas fa-user-md"></i>
              {{ request.practitionerEmail }}
            </h4>
            <span class="request-badge outgoing">Outgoing Request</span>
          </div>
          <div class="request-details">
            <p class="request-message">
              <i class="fas fa-comment-medical"></i>
              You have requested to link with this practitioner
            </p>
            <p class="request-date">
              <i class="fas fa-calendar-alt"></i>
              Requested on {{ formatDate(request.createdAt) }}
            </p>
          </div>
        </div>
        <div class="request-actions">
          <button @click="cancelRequest(request.id)" class="cancel-btn">
            <i class="fas fa-times"></i> Cancel Request
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { format } from 'date-fns';

export default {
  name: 'PractitionerRequests',

  setup() {
    const store = useStore();
    const loading = computed(() => store.getters['patient/isLoading']);
    const loadingLocally = ref(false);
    const error = ref(null);

    // Get requests from store
    const requests = computed(() => {
      return store.getters['patient/getPendingRequests'] || [];
    });

    // Filter incoming requests (practitioner-initiated)
    const incomingRequests = computed(() => {
      return requests.value.filter(req => req.direction === 'incoming');
    });

    // Filter outgoing requests (patient-initiated)
    const outgoingRequests = computed(() => {
      return requests.value.filter(req => req.direction === 'outgoing');
    });

    // Format date
    const formatDate = (date) => {
      if (!date) return 'N/A';
      return format(new Date(date), 'MMM d, yyyy h:mm a');
    };

    // Fetch practitioner requests
    const fetchRequests = async () => {
      try {
        loading.value = true;
        error.value = null;
        await store.dispatch('patient/fetch_pending_requests');
      } catch (err) {
        console.error('Error fetching practitioner requests:', err);
        error.value = 'Failed to load practitioner requests';
      } finally {
        loading.value = false;
      }
    };

    // Approve request
    const approveRequest = async (requestId) => {
      try {
        loading.value = true;
        error.value = null;

        // Use the store action to approve the request
        const result = await store.dispatch('patient/approve_linking_request', requestId);

        if (!result.success) {
          error.value = result.error || 'Failed to approve request';
        }

        // Refresh the requests
        await fetchRequests();
      } catch (err) {
        console.error('Error approving request:', err);
        error.value = 'Failed to approve request';
      } finally {
        loading.value = false;
      }
    };

    // Reject request
    const rejectRequest = async (requestId) => {
      try {
        loading.value = true;
        error.value = null;

        // Use the store action to reject the request
        const result = await store.dispatch('patient/reject_linking_request', requestId);

        if (!result.success) {
          error.value = result.error || 'Failed to reject request';
        }

        // Refresh the requests
        await fetchRequests();
      } catch (err) {
        console.error('Error rejecting request:', err);
        error.value = 'Failed to reject request';
      } finally {
        loading.value = false;
      }
    };

    // Cancel request
    const cancelRequest = async (requestId) => {
      try {
        loading.value = true;
        error.value = null;

        // Use the store action to cancel the request
        const result = await store.dispatch('patient/cancel_linking_request', requestId);

        if (!result.success) {
          error.value = result.error || 'Failed to cancel request';
        }

        // Refresh the requests
        await fetchRequests();
      } catch (err) {
        console.error('Error cancelling request:', err);
        error.value = 'Failed to cancel request';
      } finally {
        loading.value = false;
      }
    };

    // Fetch requests on mount
    onMounted(fetchRequests);

    return {
      requests,
      incomingRequests,
      outgoingRequests,
      loading,
      error,
      formatDate,
      approveRequest,
      rejectRequest,
      cancelRequest
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.practitioner-requests {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 24px;
  border-top: 3px solid $primary;

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;

    .spinner {
      width: 36px;
      height: 36px;
      border: 3px solid rgba($primary, 0.2);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 12px;
    }

    p {
      color: $dark-grey;
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;
    color: $dark-grey;
    background-color: rgba($primary-light, 0.05);
    border-radius: 8px;
    border: 1px dashed rgba($primary-light, 0.2);

    i {
      font-size: 42px;
      margin-bottom: 16px;
      color: #28a745;
    }

    p {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .requests-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    .request-card {
      border-radius: 8px;
      padding: 16px;
      box-shadow: $shadow-sm;
      background-color: rgba($primary-light, 0.02);
      border-left: 4px solid $primary;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &.incoming {
        border-left-color: #28a745;

        .request-badge.incoming {
          background-color: rgba(#28a745, 0.1);
          color: #28a745;
        }
      }

      &.outgoing {
        border-left-color: $primary;

        .request-badge.outgoing {
          background-color: rgba($primary, 0.1);
          color: $primary;
        }
      }

      .request-info {
        margin-bottom: 16px;

        .request-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px dashed rgba($primary-light, 0.2);

          h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: $primary;
            display: flex;
            align-items: center;
            gap: 8px;

            i {
              color: $primary-light;
              font-size: 0.9rem;
            }
          }

          .request-badge {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 12px;
            background-color: rgba($primary, 0.1);
            color: $primary;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        .request-details {
          background-color: rgba($primary-light, 0.05);
          padding: 10px 12px;
          border-radius: 6px;

          p {
            margin: 0 0 6px 0;
            font-size: 0.9rem;
            color: $dark-grey;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              width: 16px;
              color: $primary-light;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }

          .request-message {
            margin-bottom: 10px;
            font-weight: 500;
          }
        }
      }

      .request-actions {
        display: flex;
        gap: 12px;

        button {
          flex: 1;
          padding: 10px 16px;
          border: none;
          border-radius: 6px;
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
          }
        }

        .approve-btn {
          background-color: #28a745;
          color: white;

          &:hover {
            background-color: darken(#28a745, 5%);
          }
        }

        .reject-btn {
          background-color: $primary;
          color: white;

          &:hover {
            background-color: darken($primary, 5%);
          }
        }

        .cancel-btn {
          background-color: #6c757d;
          color: white;

          &:hover {
            background-color: darken(#6c757d, 5%);
          }
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .practitioner-requests {
    .requests-list {
      grid-template-columns: 1fr;
    }

    .request-actions {
      flex-direction: column;

      button {
        width: 100%;
      }
    }
  }
}
</style>
