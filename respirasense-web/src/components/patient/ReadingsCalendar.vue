<template>
  <div class="readings-calendar">
    <!-- Loading indicator -->
    <div v-if="isLoadingReadings" class="calendar-loading">
      <div class="shimmer-container">
        <div class="spinner"></div>
        <span class="loading-text">Loading calendar data<span class="dot">.</span><span class="dot">.</span><span class="dot">.</span></span>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="loadingError" class="calendar-error">
      {{ loadingError }}
      <button @click="fetchCalendarReadings" class="retry-button">
        <i class="fas fa-sync"></i> Retry
      </button>
    </div>

    <div class="calendar-controls">
      <div class="view-toggle">
        <button
          @click="setView('year')"
          :class="{ active: currentView === 'year' }"
          class="view-button"
        >
          Year
        </button>
        <button
          @click="setView('month')"
          :class="{ active: currentView === 'month' }"
          class="view-button"
        >
          Month
        </button>
        <button
          @click="setView('week')"
          :class="{ active: currentView === 'week' }"
          class="view-button"
        >
          Week
        </button>
        <button
          @click="setView('day')"
          :class="{ active: currentView === 'day' }"
          class="view-button"
        >
          Day
        </button>
      </div>
      <div class="navigation">
        <button @click="navigatePrevious" class="nav-button">
          <i class="fas fa-chevron-left"></i>
        </button>
        <span class="current-period">{{ currentPeriodLabel }}</span>
        <button @click="navigateNext" class="nav-button">
          <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="goToToday" class="nav-button" title="Go to today">
          <i class="fas fa-calendar-day"></i>
        </button>
      </div>
    </div>

    <!-- Year View -->
    <div v-if="currentView === 'year'" class="year-view">
      <div
        v-for="month in 12"
        :key="month"
        class="month-mini"
        @click="selectMonth(month - 1)"
      >
        <h4>{{ getMonthName(month - 1) }}</h4>
        <div class="mini-days">
          <div
            v-for="day in getDaysInMonth(currentDate.getFullYear(), month - 1)"
            :key="`${month}-${day}`"
            :class="{
              'has-reading': hasReadingOnDate(new Date(currentDate.getFullYear(), month - 1, day)),
              'current-day': isCurrentDay(new Date(currentDate.getFullYear(), month - 1, day))
            }"
            class="mini-day"
          >
            {{ day }}
            <!-- Add a dot indicator for days with readings -->
            <span v-if="hasReadingOnDate(new Date(currentDate.getFullYear(), month - 1, day))" class="reading-indicator"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Month View -->
    <div v-if="currentView === 'month'" class="month-view">
      <div class="weekdays">
        <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
      </div>
      <div class="days">
        <div
          v-for="{ day, date, inMonth } in getMonthDays()"
          :key="date.toISOString()"
          :class="{
            'other-month': !inMonth,
            'has-reading': hasReadingOnDate(date),
            'current-day': isCurrentDay(date)
          }"
          class="day"
          @click="selectDay(date)"
        >
          {{ day }}
          <!-- Add a dot indicator for days with readings -->
          <span v-if="hasReadingOnDate(date)" class="reading-indicator"></span>
        </div>
      </div>
    </div>

    <!-- Week View -->
    <div v-if="currentView === 'week'" class="week-view">
      <div class="weekdays">
        <div v-for="(date, index) in getWeekDays()" :key="index" class="weekday">
          {{ weekdays[date.getDay()] }} {{ date.getDate() }}
        </div>
      </div>
      <div class="week-days">
        <div
          v-for="(date, index) in getWeekDays()"
          :key="index"
          :class="{
            'has-reading': hasReadingOnDate(date),
            'current-day': isCurrentDay(date)
          }"
          class="week-day"
          @click="selectDay(date)"
        >
          <!-- Add a dot indicator for days with readings -->
          <span v-if="hasReadingOnDate(date)" class="reading-indicator-week"></span>
          <div v-if="getReadingsForDate(date).length > 0" class="day-readings">
            <div
              v-for="reading in getReadingsForDate(date)"
              :key="reading.id"
              :class="reading.riskLevel"
              class="day-reading"
            >
              {{ formatTime(reading.timestamp) }}
            </div>
          </div>
          <div v-else class="no-readings">No readings</div>
        </div>
      </div>
    </div>

    <!-- Day View -->
    <div v-if="currentView === 'day'" class="day-view">
      <h3>{{ formatDate(selectedDate) }}</h3>
      <div v-if="getReadingsForDate(selectedDate).length > 0" class="day-readings-list">
        <div
          v-for="reading in getReadingsForDate(selectedDate)"
          :key="reading.id"
          :class="reading.riskLevel"
          class="day-reading-item"
        >
          <div class="reading-time">{{ formatTime(reading.timestamp) }}</div>
          <div class="reading-metrics">
            <span>RR: {{ formatDecimal(reading.respiratoryRate) }} bpm</span>
            <span>O₂: {{ formatDecimal(reading.oxygenSaturation) }}%</span>
            <span>HR: {{ formatDecimal(reading.heartRate) }} bpm</span>
            <span>Temp: {{ formatDecimal(reading.temperature) }}°C</span>
          </div>
          <div class="reading-risk">
            COPD Risk: <span :class="reading.riskLevel">{{ reading.riskLevel.toUpperCase() }}</span>
          </div>
          <p v-if="reading.notes" class="reading-notes">{{ reading.notes }}</p>
        </div>
      </div>
      <div v-else class="no-readings-day">
        No readings for this day
      </div>
    </div>
  </div>
</template>

<script>
import { useReadingsCalendar } from './composables/useReadingsCalendar';

export default {
  name: 'ReadingsCalendar',
  props: {
    readings: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  setup(props) {
    // Use our composable which now handles fetching readings independently
    const {
      // State
      currentView,
      currentDate,
      selectedDate,
      weekdays,
      currentPeriodLabel,
      isLoadingReadings,
      loadingError,

      // Navigation functions
      navigatePrevious,
      navigateNext,
      goToToday,

      // View management
      setView,
      selectMonth,
      selectDay,

      // Helper functions
      getMonthName,
      getDaysInMonth,
      getMonthDays,
      getWeekDays,

      // Reading-related functions
      hasReadingOnDate,
      getReadingsForDate,
      isCurrentDay,
      fetchCalendarReadings,

      // Formatting functions
      formatDate,
      formatTime,
      formatDecimal
    } = useReadingsCalendar(props);

    // Expose the fetchCalendarReadings method to parent components
    // This allows the parent to trigger a refresh when needed
    const refreshCalendarData = () => {
      console.log('Calendar refresh triggered by parent component');
      fetchCalendarReadings();
    };

    return {
      // State
      currentView,
      currentDate,
      selectedDate,
      weekdays,
      currentPeriodLabel,
      isLoadingReadings,
      loadingError,

      // Navigation functions
      navigatePrevious,
      navigateNext,
      goToToday,

      // View management
      setView,
      selectMonth,
      selectDay,

      // Helper functions
      getMonthName,
      getDaysInMonth,
      getMonthDays,
      getWeekDays,

      // Reading-related functions
      hasReadingOnDate,
      getReadingsForDate,
      isCurrentDay,
      fetchCalendarReadings,
      refreshCalendarData,

      // Formatting functions
      formatDate,
      formatTime,
      formatDecimal
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/components/patient/ReadingsCalendar.scss';

/* Loading indicator styles */
.calendar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  .shimmer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 50%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      animation: shimmer 2s infinite;
      z-index: 1;
    }
  }

  .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #b71540;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
    z-index: 2;
  }

  .loading-text {
    font-size: 1.1em;
    color: #333;
    font-weight: 500;
    z-index: 2;

    .dot {
      opacity: 0;
      animation: dot-fade 1.5s infinite;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.5s;
      }

      &:nth-child(3) {
        animation-delay: 1s;
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @keyframes dot-fade {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }
}

/* Error message styles */
.calendar-error {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff3f3;
  border: 1px solid #ffcccb;
  border-radius: 8px;
  color: #d63031;
  margin-bottom: 15px;

  .retry-button {
    background-color: #b71540;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 0.9em;

    &:hover {
      background-color: #900c3f;
    }

    i {
      margin-right: 5px;
    }
  }
}
</style>
