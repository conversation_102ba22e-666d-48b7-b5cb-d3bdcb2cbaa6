import { ref, computed, watch, onMounted } from 'vue';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addDays, subDays, addMonths, subMonths, addYears, subYears } from 'date-fns';
import { useStore } from 'vuex';
import { auth } from '@/plugins/firebase/firebase';

/**
 * Composable for readings calendar functionality
 * @param {Object} props - Component props
 * @param {Array} [props.readings] - Optional readings data from parent component
 * @returns {Object} Calendar utility functions and state
 */
export function useReadingsCalendar(props = {}) {
  const store = useStore();

  // State for calendar readings (independent from list view)
  const calendarReadings = ref([]);
  const isLoadingReadings = ref(false);
  const loadingError = ref(null);

  // State for calendar view
  const currentView = ref('month');
  const currentDate = ref(new Date());
  const selectedDate = ref(new Date());

  // Create a map of dates with readings for faster lookup
  const datesWithReadings = new Map();

  // Constants
  const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Helper function to get week days (needed for fetchCalendarReadings)
  const getWeekDays = () => {
    const start = startOfWeek(currentDate.value);
    const end = endOfWeek(currentDate.value);
    return eachDayOfInterval({ start, end });
  };

  // Get the readings to use (either from props or from our own state)
  const readings = computed(() => {
    // If props.readings exists and has items, use it
    if (props.readings && props.readings.length > 0) {
      return props.readings;
    }
    // Otherwise use our independently loaded readings
    return calendarReadings.value;
  });

  // Process all readings once and create a lookup map
  const processReadings = () => {
    datesWithReadings.clear();

    // Use the computed readings value which could come from props or our own state
    const readingsToProcess = readings.value;

    if (readingsToProcess && readingsToProcess.length > 0) {
      readingsToProcess.forEach((reading) => {
        if (!reading) return;

        // Get timestamp from reading
        let timestamp = null;

        // First try timestamp field
        if (reading.timestamp) {
          timestamp = reading.timestamp;
        }
        // Then try lastUpdated field
        else if (reading.lastUpdated) {
          timestamp = reading.lastUpdated;
        }
        // If neither exists, skip this reading
        else {
          return;
        }

        // Convert timestamp to Date object
        let readingDate;

        try {
          // If timestamp is a Firestore Timestamp object, convert it
          if (timestamp && typeof timestamp === 'object' && typeof timestamp.toDate === 'function') {
            readingDate = timestamp.toDate();
          } else if (timestamp && typeof timestamp === 'object' && timestamp.seconds) {
            // Handle Firestore timestamp that's been serialized
            readingDate = new Date(timestamp.seconds * 1000);
          } else if (timestamp && typeof timestamp === 'object' && timestamp._seconds) {
            // Handle Firestore timestamp with _seconds field
            readingDate = new Date(timestamp._seconds * 1000);
          } else if (timestamp && typeof timestamp === 'string') {
            // Handle string timestamp
            readingDate = new Date(timestamp);
          } else if (timestamp && typeof timestamp === 'number') {
            // Handle numeric timestamp (milliseconds since epoch)
            readingDate = new Date(timestamp);
          } else {
            // If we can't determine the type, try to create a Date object
            readingDate = new Date(timestamp);
          }

          // Check if the date is valid
          if (isNaN(readingDate.getTime())) {
            return;
          }

          // Create a date key in format YYYY-MM-DD
          const dateKey = `${readingDate.getFullYear()}-${readingDate.getMonth()}-${readingDate.getDate()}`;

          // Add to map
          if (!datesWithReadings.has(dateKey)) {
            datesWithReadings.set(dateKey, []);
          }

          // Add reading to the array for this date
          datesWithReadings.get(dateKey).push(reading);
        } catch (error) {
          console.error('Error processing reading date:', error);
        }
      });
    }
  };

  /**
   * Fetch readings directly from Firestore for the calendar
   * This allows the calendar to load data independently of the list view
   * @param {Object} options - Optional parameters for fetching
   * @param {Date} options.fromDate - Start date for fetching readings
   * @param {Date} options.toDate - End date for fetching readings
   */
  const fetchCalendarReadings = async (options = {}) => {
    try {
      isLoadingReadings.value = true;
      loadingError.value = null;

      // Get the current user
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Determine date range based on current view if not provided
      let fromDate = options.fromDate;
      let toDate = options.toDate;

      if (!fromDate || !toDate) {
        // Use current date as reference point for date calculations
        if (currentView.value === 'day') {
          // For day view, just get readings for the selected day
          fromDate = new Date(selectedDate.value);
          fromDate.setHours(0, 0, 0, 0);

          toDate = new Date(selectedDate.value);
          toDate.setHours(23, 59, 59, 999);
        }
        else if (currentView.value === 'week') {
          // For week view, get readings for the current week
          const weekDays = getWeekDays();
          fromDate = weekDays[0];
          toDate = weekDays[6];
          toDate.setHours(23, 59, 59, 999);
        }
        else if (currentView.value === 'month') {
          // For month view, get readings for the current month
          fromDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1);
          toDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 0, 23, 59, 59, 999);
        }
        else if (currentView.value === 'year') {
          // For year view, get readings for the current year
          fromDate = new Date(currentDate.value.getFullYear(), 0, 1);
          toDate = new Date(currentDate.value.getFullYear(), 11, 31, 23, 59, 59, 999);
        }
      }

      console.log(`Fetching calendar readings from ${fromDate} to ${toDate}`);

      // Fetch readings for the calendar view
      // We want to show dots for many days, so we need more readings
      let fetchedReadings;

      if (fromDate && toDate) {
        // If we have a date range, use it to fetch readings
        fetchedReadings = await store.dispatch('patient/health/fetchReadingsInDateRange', {
          fromDate,
          toDate,
          limit: 100
        });
      } else {
        // Fallback to fetching a fixed number of readings
        fetchedReadings = await store.dispatch('patient/health/fetchMoreReadings', {
          limit: 100 // Fetch more readings for the calendar to show a good history
        });
      }

      // Update our local state
      calendarReadings.value = fetchedReadings || [];

      // Process the readings to update the calendar dots
      processReadings();
    } catch (error) {
      console.error('Error fetching calendar readings:', error);
      loadingError.value = 'Failed to load calendar data';
    } finally {
      isLoadingReadings.value = false;
    }
  };

  // Initialize readings on mount
  onMounted(() => {
    // If no readings are provided via props, fetch them directly
    if (!props.readings || props.readings.length === 0) {
      fetchCalendarReadings();
    } else {
      // Otherwise, process the provided readings
      processReadings();
    }
  });

  // Watch for changes in readings from props
  watch(() => props.readings, () => {
    if (props.readings && props.readings.length > 0) {
      processReadings();
    }
  });

  // Watch for changes in view to reload data
  watch(currentView, (newView, oldView) => {
    if (newView !== oldView) {
      console.log(`View changed from ${oldView} to ${newView}, reloading data`);
      fetchCalendarReadings();
    }
  });

  // Watch for changes in current date to reload data
  watch(currentDate, (newDate, oldDate) => {
    // Only reload if the year, month, or day has changed
    if (
      newDate.getFullYear() !== oldDate.getFullYear() ||
      newDate.getMonth() !== oldDate.getMonth() ||
      newDate.getDate() !== oldDate.getDate()
    ) {
      console.log(`Date changed from ${oldDate} to ${newDate}, reloading data`);
      fetchCalendarReadings();
    }
  });

  // Watch for changes in selected date (for day view)
  watch(selectedDate, (newDate, oldDate) => {
    if (
      currentView.value === 'day' &&
      (newDate.getFullYear() !== oldDate.getFullYear() ||
       newDate.getMonth() !== oldDate.getMonth() ||
       newDate.getDate() !== oldDate.getDate())
    ) {
      console.log(`Selected date changed from ${oldDate} to ${newDate}, reloading data`);
      fetchCalendarReadings();
    }
  });

  // Format helpers
  const formatDecimal = (value) => {
    if (!value || isNaN(value)) return '--';
    return Number(value).toFixed(2);
  };

  const formatDate = (date) => {
    try {
      if (!date) return '';
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return 'Invalid date';
      }
      return format(dateObj, 'MMMM d, yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  const formatTime = (reading) => {
    if (!reading) return '';
    try {
      const timestamp = reading.timestamp || reading.lastUpdated;
      if (!timestamp) return '';

      const dateObj = new Date(timestamp);
      if (isNaN(dateObj.getTime())) {
        return 'Invalid time';
      }
      return format(dateObj, 'h:mm a');
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Invalid time';
    }
  };

  // Navigation functions
  const navigatePrevious = () => {
    if (currentView.value === 'day') {
      selectedDate.value = subDays(selectedDate.value, 1);
      currentDate.value = selectedDate.value;
    } else if (currentView.value === 'week') {
      currentDate.value = subDays(currentDate.value, 7);
      selectedDate.value = currentDate.value;
    } else if (currentView.value === 'month') {
      currentDate.value = subMonths(currentDate.value, 1);
    } else if (currentView.value === 'year') {
      currentDate.value = subYears(currentDate.value, 1);
    }
  };

  const navigateNext = () => {
    if (currentView.value === 'day') {
      selectedDate.value = addDays(selectedDate.value, 1);
      currentDate.value = selectedDate.value;
    } else if (currentView.value === 'week') {
      currentDate.value = addDays(currentDate.value, 7);
      selectedDate.value = currentDate.value;
    } else if (currentView.value === 'month') {
      currentDate.value = addMonths(currentDate.value, 1);
    } else if (currentView.value === 'year') {
      currentDate.value = addYears(currentDate.value, 1);
    }
  };

  const goToToday = () => {
    const today = new Date();
    currentDate.value = today;
    selectedDate.value = today;
  };

  // View management
  const setView = (view) => {
    currentView.value = view;
  };

  const selectMonth = (month) => {
    currentDate.value = new Date(currentDate.value.getFullYear(), month, 1);
    currentView.value = 'month';
  };

  const selectDay = (date) => {
    selectedDate.value = date;
    currentDate.value = date;
    currentView.value = 'day';
  };

  // Helper functions
  const getMonthName = (month) => {
    return monthNames[month];
  };

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getMonthDays = () => {
    const year = currentDate.value.getFullYear();
    const month = currentDate.value.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const daysInMonth = lastDay.getDate();
    const firstDayOfWeek = firstDay.getDay();

    const days = [];

    // Add days from previous month
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevMonthYear = month === 0 ? year - 1 : year;
    const daysInPrevMonth = getDaysInMonth(prevMonthYear, prevMonth);

    for (let i = 0; i < firstDayOfWeek; i++) {
      const day = daysInPrevMonth - firstDayOfWeek + i + 1;
      days.push({
        day,
        date: new Date(prevMonthYear, prevMonth, day),
        inMonth: false
      });
    }

    // Add days from current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        day: i,
        date: new Date(year, month, i),
        inMonth: true
      });
    }

    // Add days from next month
    const remainingDays = 42 - days.length; // 6 rows of 7 days
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextMonthYear = month === 11 ? year + 1 : year;

    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        day: i,
        date: new Date(nextMonthYear, nextMonth, i),
        inMonth: false
      });
    }

    return days;
  };

  // Reading-related functions
  const hasReadingOnDate = (date) => {
    if (!date || !readings.value || readings.value.length === 0) {
      return false;
    }

    try {
      // Create a date key in the same format as our map
      const dateKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;

      // Check if we have readings for this date
      const hasReading = datesWithReadings.has(dateKey);

      return hasReading;
    } catch (error) {
      console.error('Error in hasReadingOnDate:', error);
      return false;
    }
  };

  const getReadingsForDate = (date) => {
    if (!date || !readings.value || readings.value.length === 0) {
      return [];
    }

    try {
      // Create a date key in the same format as our map
      const dateKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;

      // Get readings for this date from our map
      const readings = datesWithReadings.has(dateKey) ? datesWithReadings.get(dateKey) : [];

      return readings;
    } catch (error) {
      console.error('Error in getReadingsForDate:', error);
      return [];
    }
  };

  const isCurrentDay = (date) => {
    if (!date) return false;

    try {
      const today = new Date();
      return (
        date.getFullYear() === today.getFullYear() &&
        date.getMonth() === today.getMonth() &&
        date.getDate() === today.getDate()
      );
    } catch (error) {
      console.error('Error in isCurrentDay:', error);
      return false;
    }
  };

  // Map risk levels from different formats
  const mapRiskLevel = (riskLevel) => {
    if (!riskLevel) return 'normal';

    const riskMap = {
      'low': 'normal',
      'medium': 'warning',
      'high': 'danger',
      'normal': 'normal',
      'warning': 'warning',
      'danger': 'danger'
    };

    return riskMap[riskLevel.toLowerCase()] || 'normal';
  };

  // Computed properties
  const isCurrentPeriod = computed(() => {
    const today = new Date();

    if (currentView.value === 'day') {
      return isCurrentDay(selectedDate.value);
    } else if (currentView.value === 'month') {
      return (
        currentDate.value.getMonth() === today.getMonth() &&
        currentDate.value.getFullYear() === today.getFullYear()
      );
    } else if (currentView.value === 'year') {
      return currentDate.value.getFullYear() === today.getFullYear();
    }

    return false;
  });

  const currentPeriodLabel = computed(() => {
    try {
      if (currentView.value === 'year') {
        return currentDate.value.getFullYear();
      } else if (currentView.value === 'month') {
        return `${getMonthName(currentDate.value.getMonth())} ${currentDate.value.getFullYear()}`;
      } else if (currentView.value === 'week') {
        const weekDays = getWeekDays();
        if (!weekDays || weekDays.length < 7) return 'Current Week';

        try {
          return `${format(weekDays[0], 'MMM d')} - ${format(weekDays[6], 'MMM d, yyyy')}`;
        } catch (error) {
          console.error('Error formatting week days:', error);
          return 'Current Week';
        }
      } else if (currentView.value === 'day') {
        return formatDate(selectedDate.value);
      }
      return '';
    } catch (error) {
      console.error('Error computing period label:', error);
      return 'Current Period';
    }
  });

  return {
    // State
    currentView,
    currentDate,
    selectedDate,
    weekdays,
    currentPeriodLabel,
    isLoadingReadings,
    loadingError,

    // Navigation functions
    navigatePrevious,
    navigateNext,
    goToToday,
    isCurrentPeriod,

    // View management
    mapRiskLevel,
    setView,
    selectMonth,
    selectDay,

    // Helper functions
    getMonthName,
    getDaysInMonth,
    getMonthDays,
    getWeekDays,

    // Reading-related functions
    hasReadingOnDate,
    getReadingsForDate,
    isCurrentDay,
    fetchCalendarReadings,

    // Formatting functions
    formatDate,
    formatTime,
    formatDecimal
  };
}
