<template>
  <div class="practitioner-contacts">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-user-md"></i>
        <h3>My Healthcare Practitioners</h3>
      </div>
      <button @click="showAddPractitionerForm = !showAddPractitionerForm" class="add-btn">
        <i class="fas" :class="showAddPractitionerForm ? 'fa-minus' : 'fa-plus'"></i>
        {{ showAddPractitionerForm ? 'Hide Form' : 'Add Practitioner' }}
      </button>
    </div>

    <!-- Add Practitioner Form (shown when button is clicked) -->
    <div v-if="showAddPractitionerForm" class="add-practitioner-section">
      <AddPractitionerForm />
    </div>

    <!-- Loading state for practitioners only -->
    <div v-if="loading && !showPractitionerRequests" class="loading-container">
      <div class="spinner"></div>
      <p>Loading practitioners...</p>
    </div>

    <!-- Empty state with requests section always visible -->
    <div v-else-if="!practitioners || practitioners.length === 0" class="empty-state">
      <i class="fas fa-user-md"></i>
      <p>No practitioners assigned yet</p>
      <p class="empty-state-subtitle">You will see your practitioners here once they are assigned to you</p>
      <p class="empty-state-action">Click the "Add Practitioner" button above to link with a practitioner</p>
    </div>

    <!-- Practitioner Requests Component - Always visible -->
    <div v-if="!loading || showPractitionerRequests" class="requests-section">
      <PractitionerRequests />
    </div>

    <div v-else class="practitioners-list">
      <div v-for="practitioner in practitioners" :key="practitioner.id" class="practitioner-card">
        <div class="practitioner-info">
          <div class="practitioner-header">
            <h4>
              <i class="fas fa-user-md"></i>
              {{ practitioner.name }}
            </h4>
            <span class="practitioner-status" :class="practitioner.status">
              {{ formatStatus(practitioner.status) }}
            </span>
          </div>
          <div class="practitioner-details">
            <p v-if="practitioner.email">
              <i class="fas fa-envelope"></i>
              {{ practitioner.email }}
            </p>
            <p v-if="practitioner.specialty">
              <i class="fas fa-stethoscope"></i>
              {{ practitioner.specialty }}
            </p>
            <p v-if="practitioner.assignedDate">
              <i class="fas fa-calendar-alt"></i>
              Assigned since {{ formatDate(practitioner.assignedDate) }}
            </p>
          </div>
        </div>
        <div class="practitioner-actions">
          <button @click="startChat(practitioner)" class="chat-btn">
            <i class="fas fa-comment-medical"></i> Contact
          </button>
          <button @click="viewHistory(practitioner)" class="history-btn">
            <i class="fas fa-history"></i> History
          </button>
          <button @click="viewReport(practitioner)" class="report-btn">
            <i class="fas fa-file-medical"></i> Report
          </button>
        </div>
      </div>
    </div>

    <!-- Chat Modal -->
    <div v-if="showChatModal" class="chat-modal">
      <div class="chat-modal-content">
        <div class="chat-modal-header">
          <h4>Chat with {{ selectedPractitioner?.name }}</h4>
          <button @click="closeChatModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <ChatBox
          v-if="selectedPractitioner"
          :practitioner-id="selectedPractitioner.id"
          :practitioner-name="selectedPractitioner.name"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { format } from 'date-fns';
import PractitionerRequests from '@/components/patient/PractitionerRequests.vue';
import AddPractitionerForm from '@/components/patient/AddPractitionerForm.vue';
import ChatBox from '@/components/chat/ChatBox.vue';

export default {
  name: 'PatientPractitionerContacts',

  components: {
    PractitionerRequests,
    AddPractitionerForm,
    ChatBox
  },

  setup() {
    const store = useStore();
    const showChatModal = ref(false);
    const selectedPractitioner = ref(null);
    const showAddPractitionerForm = ref(false);

    // Get state from store
    const practitioners = computed(() => store.getters['patient/getLinkedPractitioners']);
    const loading = computed(() => store.getters['patient/isLoading']);
    const error = computed(() => store.getters['patient/getError']);

    // Check if there are pending requests to show
    const showPractitionerRequests = computed(() => {
      const pendingRequests = store.getters['patient/getPendingRequests'] || [];
      return pendingRequests.length > 0;
    });

    // Format date
    const formatDate = (date) => {
      if (!date) return 'N/A';

      if (typeof date === 'object' && date.seconds) {
        return format(new Date(date.seconds * 1000), 'MMM d, yyyy');
      }

      return format(new Date(date), 'MMM d, yyyy');
    };

    // Format status
    const formatStatus = (status) => {
      if (!status) return 'Unknown';

      const statusMap = {
        'active': 'Active',
        'pending': 'Pending',
        'inactive': 'Inactive'
      };

      return statusMap[status] || status;
    };

    // Start chat with practitioner
    const startChat = async (practitioner) => {
      selectedPractitioner.value = practitioner;
      showChatModal.value = true;

      // Fetch chat messages
      await store.dispatch('patient/fetch_chat_messages', practitioner.id);
    };

    // Close chat modal
    const closeChatModal = () => {
      showChatModal.value = false;
      selectedPractitioner.value = null;
    };

    // View practitioner history
    const viewHistory = (practitioner) => {
      // This will be implemented later
      console.log('View history for practitioner:', practitioner.id);
    };

    // View detailed report
    const viewReport = (practitioner) => {
      // This will be implemented later
      console.log('View report for practitioner:', practitioner.id);
    };

    // Fetch practitioners and pending requests on mount
    onMounted(async () => {
      await store.dispatch('patient/fetch_linked_practitioners');
      await store.dispatch('patient/fetch_pending_requests');
    });

    return {
      practitioners,
      loading,
      error,
      showChatModal,
      selectedPractitioner,
      showAddPractitionerForm,
      showPractitionerRequests,
      formatDate,
      formatStatus,
      startChat,
      closeChatModal,
      viewHistory,
      viewReport
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.practitioner-contacts {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }

    .add-btn {
      background-color: $primary;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: darken($primary, 5%);
        transform: translateY(-1px);
      }

      i {
        font-size: 0.8rem;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;

    .spinner {
      width: 36px;
      height: 36px;
      border: 3px solid rgba($primary, 0.2);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 12px;
    }

    p {
      color: $dark-grey;
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;
    color: $dark-grey;

    i {
      font-size: 42px;
      margin-bottom: 16px;
      color: rgba($primary-light, 0.4);
    }

    p {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }

    .empty-state-subtitle {
      margin-top: 8px;
      font-size: 0.9rem;
      color: lighten($dark-grey, 15%);
    }

    .empty-state-action {
      margin-top: 16px;
      font-size: 0.95rem;
      color: $primary;
      font-weight: 500;
    }

    .add-practitioner-section,
    .requests-section {
      width: 100%;
      max-width: 600px;
      margin-top: 30px;
    }
  }

  .practitioners-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    .practitioner-card {
      border-radius: 8px;
      padding: 16px;
      box-shadow: $shadow-sm;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background-color: rgba($primary-light, 0.02);
      border-left: 4px solid $primary-light;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .practitioner-info {
        margin-bottom: 16px;

        .practitioner-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px dashed rgba($primary-light, 0.2);

          h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: $primary;
            display: flex;
            align-items: center;
            gap: 8px;

            i {
              color: $primary-light;
              font-size: 0.9rem;
            }
          }

          .practitioner-status {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.active {
              background-color: rgba(40, 167, 69, 0.2);
              color: #155724;
            }

            &.pending {
              background-color: rgba(255, 193, 7, 0.2);
              color: #856404;
            }

            &.inactive {
              background-color: rgba(108, 117, 125, 0.2);
              color: #383d41;
            }
          }
        }

        .practitioner-details {
          background-color: rgba($primary-light, 0.05);
          padding: 10px 12px;
          border-radius: 6px;

          p {
            margin: 0 0 6px 0;
            font-size: 0.9rem;
            color: $dark-grey;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              width: 16px;
              color: $primary-light;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .practitioner-actions {
        display: flex;
        gap: 8px;

        button {
          flex: 1;
          padding: 8px 12px;
          border: none;
          border-radius: 6px;
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          transition: all 0.2s ease;

          i {
            font-size: 0.85rem;
          }

          &:hover {
            transform: translateY(-1px);
          }
        }

        .chat-btn {
          background-color: $primary;
          color: white;

          &:hover {
            background-color: darken($primary, 5%);
          }
        }

        .history-btn, .report-btn {
          background-color: rgba($primary, 0.1);
          color: $primary;

          &:hover {
            background-color: rgba($primary, 0.2);
          }
        }
      }
    }
  }

  .chat-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .chat-modal-content {
      background-color: white;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .chat-modal-header {
        padding: 16px 20px;
        background-color: $primary;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 1.2rem;
          font-weight: 600;
        }

        .close-btn {
          background: none;
          border: none;
          color: white;
          font-size: 1.2rem;
          cursor: pointer;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .practitioner-contacts {
    .practitioners-list {
      grid-template-columns: 1fr;
    }

    .practitioner-actions {
      flex-direction: column;
    }
  }
}
</style>
