<template>
  <div class="alert-list">
    <div v-if="alerts.length === 0" class="no-alerts">
      No recent alerts
    </div>
    <div v-else class="alert-items">
      <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="alert.severity">
        <div class="alert-icon">
          <i :class="getAlertIcon(alert.severity)"></i>
        </div>
        <div class="alert-content">
          <div class="alert-header">
            <span class="alert-title">{{ alert.title }}</span>
            <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
          </div>
          <p class="alert-message">{{ alert.message }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlertList',
  props: {
    alerts: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getAlertIcon(severity) {
      const icons = {
        critical: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[severity] || icons.info
    },
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped lang="scss">
.alert-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .no-alerts {
    text-align: center;
    color: #666;
    padding: 20px;
  }

  .alert-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #eee;
    gap: 15px;

    &:last-child {
      border-bottom: none;
    }

    &.critical {
      background: #fef2f2;
      .alert-icon { color: #dc2626; }
    }

    &.warning {
      background: #fffbeb;
      .alert-icon { color: #d97706; }
    }

    &.info {
      background: #f0f9ff;
      .alert-icon { color: #0ea5e9; }
    }
  }

  .alert-icon {
    font-size: 20px;
  }

  .alert-content {
    flex: 1;
  }

  .alert-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .alert-title {
    font-weight: bold;
    color: #333;
  }

  .alert-time {
    color: #666;
    font-size: 0.9em;
  }

  .alert-message {
    margin: 0;
    color: #4b5563;
  }
}
</style>