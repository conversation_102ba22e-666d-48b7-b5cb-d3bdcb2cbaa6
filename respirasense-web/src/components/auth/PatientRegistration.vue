<template>
  <section class="wrapper-login">
    <Card>
      <template v-slot:card-header>
        <Logo :path="logo" :className="'text-center'" :width="'180'"></Logo>
      </template>
      <template v-slot:card-body>
        <br>
        <h4 class="my-2 text-muted">Complete Your Profile</h4>
        <form class="login-form" @submit.prevent="submitProfile">
          <div v-if="error" class="auth__error">
            <p class="mb-1 subtitle">{{ error }}</p>
          </div>
          <div class="form-group">
            <label for="name">Full Name</label>
            <input
              type="text"
              id="name"
              v-model="profile.name"
              required
              placeholder=""
            />
          </div>

          <div class="form-group">
            <label for="age">Age</label>
            <input
              type="number"
              id="age"
              v-model="profile.age"
              required
              placeholder=""
              min="0"
              max="120"
            />
          </div>

          <div class="form-group">
            <label for="gender">Gender</label>
            <select
              id="gender"
              v-model="profile.gender"
              required
              class="form-control"
            >
              <option value="">Select gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label for="weight">Weight (kg)</label>
            <input
              type="number"
              id="weight"
              v-model="profile.weight"
              required
              placeholder=""
              step="0.1"
              min="0"
            />
          </div>

          <div class="form-group">
            <label for="height">Height (cm)</label>
            <input
              type="number"
              id="height"
              v-model="profile.height"
              required
              placeholder=""
              step="0.1"
              min="0"
            />
          </div>

          <button 
            class="btn btn-ck fill-danger w-50 m-auto"
            type="submit"
          >
            Complete Registration
          </button>
        </form>
      </template>
    </Card>
  </section>
</template>

<script>
import Logo from "@/components/auth/Logo";
import Card from "@/components/auth/Card";
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { notify } from '@kyvg/vue3-notification';

export default {
  name: 'PatientRegistration',
  components: {
    Logo,
    Card
  },
  setup() {
    const profile = ref({
      name: '',
      age: '',
      gender: '',
      weight: '',
      height: ''
    });
    const error = ref('');
    const logo = require('@/assets/<EMAIL>');
    const router = useRouter();
    const store = useStore();

    const submitProfile = async () => {
      try {
        console.log('Submitting profile:', profile.value);
        await store.dispatch('patient/saveProfile', profile.value);
        
        notify({
          type: 'success',
          text: 'Profile completed successfully!'
        });

        // Redirect to dashboard after successful profile completion
        router.push('/patient/dashboard');
      } catch (err) {
        console.error('Error saving profile:', err);
        error.value = 'Failed to save profile. Please try again.';
        notify({
          type: 'error',
          text: error.value
        });
      }
    };

    return {
      profile,
      error,
      logo,
      submitProfile
    };
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/base/settings";

.wrapper-login {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  height: 100vh;
  background: $background-login;

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 2rem;

    .form-group {
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;

      label {
        text-transform: uppercase;
        text-align: start;
        margin-bottom: 3px;
        font-size: 10pt;
        color: #6c757d
      }
      
      input, select {
        margin-bottom: 0.5rem;
        font-size: 1em;
        border: 0;
        border-bottom: 1px solid #6c757d;
        -webkit-appearance: none;
        border-radius: 0;
        padding: 0;
        cursor: text;
        background: transparent;

        &:focus {
          outline: 0;
          border-bottom: 1px solid #B61440;
        }
      }

      select {
        cursor: pointer;
        padding-bottom: 0.5rem;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right center;
        background-size: 1em;
      }
    }
  }

  .auth__error {
    margin-bottom: .5rem;
    display: grid;
    gap: 10px;
    margin-top: 0;
    text-align: center;
    color: red;
  }
}

.btn {
  margin-top: 1.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}
</style>
