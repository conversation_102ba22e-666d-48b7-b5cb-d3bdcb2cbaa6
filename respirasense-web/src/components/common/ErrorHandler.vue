<template>
  <div v-if="error" class="error-container" :class="{ 'is-dismissible': dismissible }">
    <div class="error-content">
      <i class="fas fa-exclamation-circle error-icon"></i>
      <div class="error-message">
        <h4 v-if="title">{{ title }}</h4>
        <p>{{ error }}</p>
        <p v-if="suggestion" class="error-suggestion">{{ suggestion }}</p>
      </div>
      <button v-if="dismissible" @click="dismiss" class="dismiss-button">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div v-if="retry" class="error-actions">
      <button @click="$emit('retry')" class="retry-button">
        <i class="fas fa-redo"></i> Try Again
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorHand<PERSON>',
  
  props: {
    error: {
      type: [String, Error],
      default: null
    },
    title: {
      type: String,
      default: 'Error'
    },
    suggestion: {
      type: String,
      default: null
    },
    dismissible: {
      type: Boolean,
      default: true
    },
    retry: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    dismiss() {
      this.$emit('dismiss');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.error-container {
  background-color: rgba($primary, 0.05);
  border-left: 4px solid $primary;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  .error-content {
    display: flex;
    align-items: flex-start;
    
    .error-icon {
      color: $primary;
      font-size: 24px;
      margin-right: 16px;
      flex-shrink: 0;
    }
    
    .error-message {
      flex-grow: 1;
      
      h4 {
        margin: 0 0 8px;
        color: $primary;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: $text-dark;
        line-height: 1.5;
      }
      
      .error-suggestion {
        margin-top: 8px;
        font-style: italic;
        color: $text-muted;
      }
    }
    
    .dismiss-button {
      background: none;
      border: none;
      color: $text-muted;
      cursor: pointer;
      padding: 4px;
      font-size: 16px;
      transition: color 0.2s ease;
      
      &:hover {
        color: $primary;
      }
    }
  }
  
  .error-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    
    .retry-button {
      background-color: $primary;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: darken($primary, 10%);
      }
    }
  }
}
</style>
