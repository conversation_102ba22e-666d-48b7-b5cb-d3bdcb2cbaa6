<template>
  <div v-if="loading" class="loading-container" :class="{ overlay, fullscreen }">
    <div class="loading-content">
      <div class="spinner" :class="size"></div>
      <p v-if="message" class="loading-message">{{ message }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingState',
  
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: 'Loading...'
    },
    overlay: {
      type: Boolean,
      default: false
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  
  &.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 100;
  }
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 1000;
  }
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .spinner {
      border: 3px solid rgba($primary, 0.1);
      border-top: 3px solid $primary;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
      
      &.small {
        width: 20px;
        height: 20px;
        border-width: 2px;
      }
      
      &.medium {
        width: 30px;
        height: 30px;
        border-width: 3px;
      }
      
      &.large {
        width: 50px;
        height: 50px;
        border-width: 4px;
      }
    }
    
    .loading-message {
      margin-top: 16px;
      color: $text-dark;
      font-weight: 500;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
