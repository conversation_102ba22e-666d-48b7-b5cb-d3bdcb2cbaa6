<template>
  <div
    class="user-avatar"
    :class="[
      `size-${size}`,
      { 'with-status': showStatus, 'clickable': clickable }
    ]"
    @click="handleClick"
  >
    <img
      :src="photoUrl"
      :alt="alt || name || 'User'"
      class="avatar-image"
      @error="handleImageError"
      :style="{ 'border-color': borderColor }"
    />
    <span
      v-if="showStatus"
      class="status-indicator"
      :class="status"
      :title="formatStatus(status)"
    ></span>
    <div v-if="showName" class="avatar-name">
      {{ name || 'User' }}
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import { getProfilePhotoUrl, handleImageError as imageErrorHandler } from '@/utils/profile-utils';
import defaultUserIcon from '@/assets/DefaultUserIcon.webp';

export default {
  name: 'UserAvatar',
  props: {
    // User data
    userId: {
      type: String,
      default: null
    },
    photoURL: {
      type: String,
      default: null
    },
    name: {
      type: String,
      default: null
    },
    status: {
      type: String,
      default: 'active'
    },

    // Display options
    size: {
      type: String,
      default: 'medium', // 'small', 'medium', 'large'
      validator: (value) => ['small', 'medium', 'large', 'xlarge'].includes(value)
    },
    showStatus: {
      type: Boolean,
      default: false
    },
    showName: {
      type: Boolean,
      default: false
    },
    borderColor: {
      type: String,
      default: 'transparent'
    },
    alt: {
      type: String,
      default: null
    },
    clickable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const photoUrl = ref(props.photoURL || defaultUserIcon);

    const loadProfilePhoto = async () => {
      try {
        if (props.userId) {
          // If userId is provided, fetch the photo URL
          photoUrl.value = await getProfilePhotoUrl(null, props.userId);
        } else if (props.photoURL) {
          // If photoURL is provided directly, use it
          // For Google photos, ensure we get a reliable URL with higher resolution
          if (props.photoURL.includes('googleusercontent.com')) {
            // First, try to extract the base URL without size parameters
            const baseUrl = props.photoURL.split('=')[0];
            if (baseUrl) {
              // Add a specific size and ensure we get a fresh version with a cache buster
              photoUrl.value = `${baseUrl}=s400-c-k-no?${Date.now()}`;
            } else {
              // If we can't extract the base URL, try the standard replacement
              photoUrl.value = props.photoURL.replace(/=s\d+-c/, `=s400-c-k-no?${Date.now()}`);
            }
          } else {
            // For other providers, use the URL as is
            photoUrl.value = props.photoURL;
          }
        } else {
          // Fallback to default icon
          photoUrl.value = defaultUserIcon;
        }
      } catch (error) {
        console.error('Error loading profile photo:', error);
        photoUrl.value = defaultUserIcon;
      }
    };

    // Format status for display
    const formatStatus = (status) => {
      if (!status) return 'Unknown';

      const statusMap = {
        'active': 'Active',
        'pending': 'Pending',
        'inactive': 'Inactive',
        'rejected': 'Rejected',
        'suspended': 'Suspended'
      };

      return statusMap[status.toLowerCase()] || status;
    };

    // Handle image loading errors
    const handleImageError = (e) => {
      imageErrorHandler(e);
    };

    // Handle click events
    const handleClick = () => {
      if (props.clickable) {
        emit('click');
      }
    };

    // Load profile photo on mount
    onMounted(() => {
      loadProfilePhoto();
    });

    // Watch for changes to userId or photoURL
    watch(() => props.userId, () => {
      loadProfilePhoto();
    });

    watch(() => props.photoURL, () => {
      if (props.photoURL) {
        photoUrl.value = props.photoURL;
      } else {
        loadProfilePhoto();
      }
    });

    return {
      photoUrl,
      formatStatus,
      handleImageError,
      handleClick
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.user-avatar {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;

  &.clickable {
    cursor: pointer;

    &:hover .avatar-image {
      transform: scale(1.05);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .avatar-image {
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid transparent;
    transition: all 0.2s ease;
  }

  .status-indicator {
    position: absolute;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &.active {
      background-color: #28a745;
    }

    &.pending {
      background-color: #ffc107;
    }

    &.inactive {
      background-color: #6c757d;
    }

    &.rejected {
      background-color: #dc3545;
    }

    &.suspended {
      background-color: #dc3545;
    }
  }

  .avatar-name {
    margin-top: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    color: $dark-grey;
    text-align: center;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // Size variants
  &.size-small {
    .avatar-image {
      width: 32px;
      height: 32px;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      bottom: 0;
      right: 0;
    }

    .avatar-name {
      font-size: 0.8rem;
    }
  }

  &.size-medium {
    .avatar-image {
      width: 48px;
      height: 48px;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      bottom: 2px;
      right: 2px;
    }
  }

  &.size-large {
    .avatar-image {
      width: 64px;
      height: 64px;
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      bottom: 3px;
      right: 3px;
    }

    .avatar-name {
      font-size: 1rem;
    }
  }

  &.size-xlarge {
    .avatar-image {
      width: 96px;
      height: 96px;
      border-width: 3px;
    }

    .status-indicator {
      width: 16px;
      height: 16px;
      bottom: 5px;
      right: 5px;
      border-width: 3px;
    }

    .avatar-name {
      font-size: 1.1rem;
      margin-top: 8px;
    }
  }
}
</style>
