<template>
  <div class="patient-activity-timeline">
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading patient activity...</p>
    </div>

    <div v-else-if="!activities || activities.length === 0" class="empty-state">
      <i class="fas fa-calendar-day"></i>
      <p>No recent patient activity</p>
      <p class="empty-state-subtitle">Patient activities will appear here as they occur</p>
    </div>

    <div v-else class="timeline-container">
      <div class="timeline-header">
        <div class="timeline-filters">
          <div class="filter-group">
            <label for="activityType">Filter by:</label>
            <select id="activityType" v-model="selectedActivityType">
              <option value="all">All Activities</option>
              <option value="reading">Health Readings</option>
              <option value="login">Login Activity</option>
              <option value="report">Report Generation</option>
            </select>
          </div>
          <div class="filter-group">
            <label for="timeRange">Time range:</label>
            <select id="timeRange" v-model="selectedTimeRange">
              <option value="day">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
        </div>
      </div>

      <div class="timeline">
        <div 
          v-for="(activity, index) in filteredActivities" 
          :key="index" 
          class="timeline-item"
          :class="getActivityClass(activity.type)"
        >
          <div class="timeline-icon">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="timeline-content">
            <div class="activity-header">
              <h4>{{ activity.patientName }}</h4>
              <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
            </div>
            <p class="activity-description">{{ activity.description }}</p>
            <div v-if="activity.details" class="activity-details">
              <div v-if="activity.type === 'reading'" class="reading-details">
                <div v-if="activity.details.respiratoryRate" class="reading-item">
                  <i class="fas fa-lungs"></i>
                  <span>{{ activity.details.respiratoryRate }} bpm</span>
                </div>
                <div v-if="activity.details.oxygenSaturation" class="reading-item">
                  <i class="fas fa-heartbeat"></i>
                  <span>{{ activity.details.oxygenSaturation }}%</span>
                </div>
                <div v-if="activity.details.heartRate" class="reading-item">
                  <i class="fas fa-heart"></i>
                  <span>{{ activity.details.heartRate }} bpm</span>
                </div>
                <div v-if="activity.details.temperature" class="reading-item">
                  <i class="fas fa-thermometer-half"></i>
                  <span>{{ activity.details.temperature }}°C</span>
                </div>
              </div>
              <div v-else-if="activity.type === 'report'" class="report-details">
                <span class="report-type">{{ activity.details.reportType }}</span>
                <button @click="viewReport(activity.details.reportId)" class="view-report-btn">
                  <i class="fas fa-file-pdf"></i> View Report
                </button>
              </div>
            </div>
            <div class="activity-actions">
              <button @click="viewPatientDetails(activity.patientId)" class="view-patient-btn">
                <i class="fas fa-user"></i> View Patient
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="hasMoreActivities" class="load-more-container">
        <button @click="loadMoreActivities" class="load-more-btn" :disabled="loadingMore">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingMore }"></i>
          {{ loadingMore ? 'Loading...' : 'Load More' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useFormatters } from '@/composables/useFormatters';

export default {
  name: 'PatientActivityTimeline',
  props: {
    activities: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const store = useStore();
    const router = useRouter();
    const { formatDate } = useFormatters();
    
    const selectedActivityType = ref('all');
    const selectedTimeRange = ref('week');
    const loadingMore = ref(false);
    const hasMoreActivities = ref(true);
    
    // Filter activities based on selected type and time range
    const filteredActivities = computed(() => {
      if (!props.activities || props.activities.length === 0) {
        return [];
      }
      
      let filtered = [...props.activities];
      
      // Filter by activity type
      if (selectedActivityType.value !== 'all') {
        filtered = filtered.filter(activity => activity.type === selectedActivityType.value);
      }
      
      // Filter by time range
      const now = new Date();
      let cutoffDate;
      
      if (selectedTimeRange.value === 'day') {
        cutoffDate = new Date(now.setHours(0, 0, 0, 0));
      } else if (selectedTimeRange.value === 'week') {
        cutoffDate = new Date(now);
        cutoffDate.setDate(now.getDate() - 7);
      } else if (selectedTimeRange.value === 'month') {
        cutoffDate = new Date(now);
        cutoffDate.setMonth(now.getMonth() - 1);
      }
      
      filtered = filtered.filter(activity => {
        const activityDate = new Date(activity.timestamp);
        return activityDate >= cutoffDate;
      });
      
      // Sort by timestamp (newest first)
      return filtered.sort((a, b) => {
        return new Date(b.timestamp) - new Date(a.timestamp);
      });
    });
    
    // Format timestamp to relative time (e.g., "2 hours ago")
    const formatTime = (timestamp) => {
      const now = new Date();
      const activityTime = new Date(timestamp);
      const diffMs = now - activityTime;
      const diffSec = Math.round(diffMs / 1000);
      const diffMin = Math.round(diffSec / 60);
      const diffHour = Math.round(diffMin / 60);
      const diffDay = Math.round(diffHour / 24);
      
      if (diffSec < 60) {
        return 'Just now';
      } else if (diffMin < 60) {
        return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
      } else if (diffHour < 24) {
        return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
      } else if (diffDay < 7) {
        return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
      } else {
        return formatDate(timestamp);
      }
    };
    
    // Get appropriate icon for activity type
    const getActivityIcon = (type) => {
      switch (type) {
        case 'reading':
          return 'fas fa-heartbeat';
        case 'login':
          return 'fas fa-sign-in-alt';
        case 'report':
          return 'fas fa-file-medical-alt';
        default:
          return 'fas fa-bell';
      }
    };
    
    // Get CSS class for activity type
    const getActivityClass = (type) => {
      return `activity-${type}`;
    };
    
    // View patient details
    const viewPatientDetails = (patientId) => {
      router.push(`/practitioner/patient/${patientId}`);
    };
    
    // View report
    const viewReport = (reportId) => {
      // Implement report viewing logic
      console.log('Viewing report:', reportId);
    };
    
    // Load more activities
    const loadMoreActivities = async () => {
      if (loadingMore.value) return;
      
      loadingMore.value = true;
      
      try {
        // Get the oldest activity timestamp as the cursor
        const oldestActivity = [...props.activities].sort((a, b) => {
          return new Date(a.timestamp) - new Date(b.timestamp);
        })[0];
        
        const cursor = oldestActivity ? oldestActivity.timestamp : null;
        
        // Dispatch action to load more activities
        const moreActivities = await store.dispatch('practitioner/fetch_more_activities', { cursor });
        
        if (!moreActivities || moreActivities.length === 0) {
          hasMoreActivities.value = false;
        }
        
        // Emit event to parent component to add more activities
        emit('load-more', moreActivities);
      } catch (error) {
        console.error('Error loading more activities:', error);
      } finally {
        loadingMore.value = false;
      }
    };
    
    return {
      selectedActivityType,
      selectedTimeRange,
      filteredActivities,
      loadingMore,
      hasMoreActivities,
      formatTime,
      getActivityIcon,
      getActivityClass,
      viewPatientDetails,
      viewReport,
      loadMoreActivities
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.patient-activity-timeline {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .loading-container, .empty-state {
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary-light, 0.1);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }
    
    p {
      margin: 0;
      color: $dark-grey;
      font-size: 1rem;
    }
    
    .empty-state-subtitle {
      margin-top: 5px;
      font-size: 0.9rem;
      color: lighten($dark-grey, 20%);
    }
    
    i {
      font-size: 2.5rem;
      color: lighten($dark-grey, 30%);
      margin-bottom: 15px;
    }
  }
  
  .timeline-container {
    .timeline-header {
      padding: 15px 20px;
      border-bottom: 1px solid rgba($primary-light, 0.1);
      
      .timeline-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        
        .filter-group {
          display: flex;
          align-items: center;
          gap: 8px;
          
          label {
            font-size: 0.9rem;
            color: $dark-grey;
            font-weight: 500;
          }
          
          select {
            padding: 6px 10px;
            border: 1px solid rgba($primary-light, 0.3);
            border-radius: 4px;
            background-color: white;
            font-size: 0.9rem;
            color: $dark-grey;
            
            &:focus {
              outline: none;
              border-color: $primary;
              box-shadow: 0 0 0 2px rgba($primary-light, 0.1);
            }
          }
        }
      }
    }
    
    .timeline {
      position: relative;
      padding: 20px;
      
      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 30px;
        height: 100%;
        width: 2px;
        background-color: rgba($primary-light, 0.2);
      }
      
      .timeline-item {
        position: relative;
        margin-bottom: 25px;
        padding-left: 45px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .timeline-icon {
          position: absolute;
          left: 0;
          top: 0;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: $primary-light;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;
          
          i {
            color: white;
            font-size: 0.9rem;
          }
        }
        
        .timeline-content {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 15px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          
          .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            h4 {
              margin: 0;
              font-size: 1rem;
              font-weight: 600;
              color: $dark-grey;
            }
            
            .activity-time {
              font-size: 0.85rem;
              color: lighten($dark-grey, 20%);
            }
          }
          
          .activity-description {
            margin: 0 0 10px 0;
            font-size: 0.95rem;
            color: $dark-grey;
          }
          
          .activity-details {
            margin-bottom: 15px;
            
            .reading-details {
              display: flex;
              flex-wrap: wrap;
              gap: 15px;
              
              .reading-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 6px 12px;
                background-color: rgba($primary-light, 0.05);
                border-radius: 4px;
                
                i {
                  color: $primary;
                  font-size: 0.9rem;
                }
                
                span {
                  font-size: 0.9rem;
                  font-weight: 500;
                  color: $dark-grey;
                }
              }
            }
            
            .report-details {
              display: flex;
              align-items: center;
              justify-content: space-between;
              
              .report-type {
                font-size: 0.9rem;
                color: $dark-grey;
                font-weight: 500;
              }
              
              .view-report-btn {
                padding: 6px 12px;
                background-color: rgba($primary-light, 0.1);
                color: $primary;
                border: none;
                border-radius: 4px;
                font-size: 0.85rem;
                font-weight: 500;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 6px;
                transition: all 0.2s ease;
                
                &:hover {
                  background-color: rgba($primary-light, 0.2);
                }
                
                i {
                  font-size: 0.85rem;
                }
              }
            }
          }
          
          .activity-actions {
            display: flex;
            justify-content: flex-end;
            
            .view-patient-btn {
              padding: 6px 12px;
              background-color: $primary-light;
              color: white;
              border: none;
              border-radius: 4px;
              font-size: 0.85rem;
              font-weight: 500;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 6px;
              transition: all 0.2s ease;
              
              &:hover {
                background-color: $primary;
              }
              
              i {
                font-size: 0.85rem;
              }
            }
          }
        }
        
        // Activity type specific styling
        &.activity-reading {
          .timeline-icon {
            background-color: #28a745;
          }
        }
        
        &.activity-login {
          .timeline-icon {
            background-color: #17a2b8;
          }
        }
        
        &.activity-report {
          .timeline-icon {
            background-color: #fd7e14;
          }
        }
      }
    }
    
    .load-more-container {
      padding: 20px;
      display: flex;
      justify-content: center;
      
      .load-more-btn {
        padding: 8px 20px;
        background-color: #f1f1f1;
        color: $dark-grey;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: #e5e5e5;
        }
        
        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }
        
        i {
          font-size: 0.9rem;
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .patient-activity-timeline {
    .timeline-container {
      .timeline-header {
        .timeline-filters {
          flex-direction: column;
          align-items: flex-start;
        }
      }
    }
  }
}
</style>
