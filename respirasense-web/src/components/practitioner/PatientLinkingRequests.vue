<template>
  <div class="patient-linking-requests">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-paper-plane"></i>
        <h3>Patient Linking Requests</h3>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading requests...</p>
    </div>

    <div v-else-if="!requests || requests.length === 0" class="empty-state">
      <i class="fas fa-inbox"></i>
      <p>No linking requests found</p>
      <p class="empty-state-subtitle">Requests you send to patients will appear here</p>
    </div>

    <div v-else class="requests-list">
      <div v-for="request in requests" :key="request.id" class="request-card" :class="getStatusClass(request.status)">
        <div class="request-info">
          <div class="request-header">
            <h4>
              <i class="fas fa-envelope"></i>
              {{ request.patientEmail }}
            </h4>
            <span class="request-status" :class="request.status">
              {{ formatStatus(request.status) }}
            </span>
          </div>

          <div class="request-details">
            <p><i class="fas fa-calendar-alt"></i> Sent: {{ formatDate(request.createdAt) }}</p>
            <p v-if="request.expiresAt"><i class="fas fa-hourglass-end"></i> Expires: {{ formatDate(request.expiresAt) }}</p>
            <p v-if="request.updatedAt"><i class="fas fa-clock"></i> Updated: {{ formatDate(request.updatedAt) }}</p>
          </div>
        </div>

        <div class="request-actions" v-if="request.status === 'pending'">
          <button @click="cancelRequest(request.id)" class="cancel-button">
            <i class="fas fa-times"></i> Cancel Request
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { format } from 'date-fns';

export default {
  name: 'PatientLinkingRequests',

  setup() {
    const store = useStore();
    const loading = ref(false);
    const error = ref(null);

    // Get requests from store
    const requests = computed(() => {
      return store.getters['practitioner/getPatientLinkingRequests'];
    });

    // Format date
    const formatDate = (date) => {
      if (!date) return 'N/A';
      return format(new Date(date), 'MMM d, yyyy h:mm a');
    };

    // Format status
    const formatStatus = (status) => {
      switch (status) {
        case 'pending': return 'Pending';
        case 'approved': return 'Approved';
        case 'rejected': return 'Rejected';
        case 'cancelled': return 'Cancelled';
        case 'expired': return 'Expired';
        default: return status;
      }
    };

    // Get status class
    const getStatusClass = (status) => {
      return `status-${status}`;
    };

    // Cancel request
    const cancelRequest = async (requestId) => {
      try {
        loading.value = true;
        await store.dispatch('practitioner/cancel_patient_linking_request', requestId);
      } catch (err) {
        error.value = err.message || 'Failed to cancel request';
        console.error('Error cancelling request:', err);
      } finally {
        loading.value = false;
      }
    };

    // Fetch requests on mount
    onMounted(async () => {
      try {
        loading.value = true;
        await store.dispatch('practitioner/fetch_patient_linking_requests');
      } catch (err) {
        error.value = err.message || 'Failed to load requests';
        console.error('Error loading requests:', err);
      } finally {
        loading.value = false;
      }
    });

    return {
      requests,
      loading,
      error,
      formatDate,
      formatStatus,
      getStatusClass,
      cancelRequest
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.patient-linking-requests {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 24px;
  border-top: 3px solid $primary;

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;

    .spinner {
      width: 36px;
      height: 36px;
      border: 3px solid rgba($primary, 0.2);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 12px;
    }

    p {
      color: $dark-grey;
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;
    color: $dark-grey;
    background-color: rgba($primary-light, 0.05);
    border-radius: 8px;
    border: 1px dashed rgba($primary-light, 0.2);

    i {
      font-size: 42px;
      margin-bottom: 16px;
      color: rgba($primary-light, 0.4);
    }

    p {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }

    .empty-state-subtitle {
      margin-top: 8px;
      font-size: 0.9rem;
      color: lighten($dark-grey, 15%);
    }
  }

  .requests-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    .request-card {
      border-radius: 8px;
      padding: 16px;
      box-shadow: $shadow-sm;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background-color: rgba($primary-light, 0.02);
      border-left: 4px solid $primary-light;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &.status-pending {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
      }

      &.status-approved {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
      }

      &.status-rejected {
        border-left-color: $primary;
        background-color: rgba($primary, 0.05);
      }

      &.status-cancelled {
        border-left-color: #6c757d;
        background-color: rgba(108, 117, 125, 0.05);
      }

      &.status-expired {
        border-left-color: #6c757d;
        background-color: rgba(108, 117, 125, 0.05);
      }

      .request-info {
        margin-bottom: 16px;

        .request-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px dashed rgba($primary-light, 0.2);

          h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: $primary;
            display: flex;
            align-items: center;
            gap: 8px;

            i {
              color: $primary-light;
              font-size: 0.9rem;
            }
          }

          .request-status {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.pending {
              background-color: rgba(255, 193, 7, 0.2);
              color: #856404;
            }

            &.approved {
              background-color: rgba(40, 167, 69, 0.2);
              color: #155724;
            }

            &.rejected {
              background-color: rgba($primary, 0.2);
              color: $primary;
            }

            &.cancelled {
              background-color: rgba(108, 117, 125, 0.2);
              color: #383d41;
            }

            &.expired {
              background-color: rgba(108, 117, 125, 0.2);
              color: #383d41;
            }
          }
        }

        .request-details {
          background-color: rgba($primary-light, 0.05);
          padding: 10px 12px;
          border-radius: 6px;

          p {
            margin: 0 0 6px 0;
            font-size: 0.9rem;
            color: $dark-grey;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              width: 16px;
              color: $primary-light;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .request-actions {
        .cancel-button {
          width: 100%;
          padding: 10px 16px;
          background-color: $primary;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            background-color: darken($primary, 5%);
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .patient-linking-requests {
    .requests-list {
      grid-template-columns: 1fr;
    }
  }
}
</style>
