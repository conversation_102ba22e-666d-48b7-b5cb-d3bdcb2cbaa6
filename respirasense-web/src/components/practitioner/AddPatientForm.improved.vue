<template>
  <div class="add-patient-form-container">
    <div class="form-header">
      <div class="section-title">
        <i class="fas fa-user-plus"></i>
        <h3>Link with Patient</h3>
      </div>
    </div>

    <!-- Loading State -->
    <LoadingState 
      :loading="isSubmitting" 
      message="Sending request..." 
      overlay
    />

    <!-- <PERSON><PERSON><PERSON> -->
    <ErrorHandler 
      :error="error" 
      title="Request Error" 
      suggestion="Please check the email and try again."
      @dismiss="error = ''"
      :retry="!!patientEmail"
      @retry="sendRequest"
    />

    <form @submit.prevent="sendRequest" class="add-patient-form">
      <div class="form-group">
        <label for="patientEmail">Patient Email</label>
        <div class="input-wrapper">
          <i class="fas fa-envelope"></i>
          <input
            type="email"
            id="patientEmail"
            v-model="patientEmail"
            placeholder="Enter patient email address"
            required
            :disabled="isSubmitting"
          >
        </div>
        <small class="form-hint">Enter the email address the patient used to register</small>
      </div>

      <div class="form-actions">
        <button
          type="submit"
          class="action-button"
          :disabled="isSubmitting || !patientEmail"
        >
          <i class="fas fa-paper-plane"></i>
          {{ isSubmitting ? 'Sending...' : 'Send Request' }}
        </button>
      </div>
    </form>

    <div v-if="successMessage" class="success-message">
      <i class="fas fa-check-circle"></i>
      <p>{{ successMessage }}</p>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useStore } from 'vuex';
import ErrorHandler from '@/components/common/ErrorHandler.vue';
import LoadingState from '@/components/common/LoadingState.vue';

export default {
  name: 'AddPatientForm',
  
  components: {
    ErrorHandler,
    LoadingState
  },

  setup() {
    const store = useStore();
    const patientEmail = ref('');
    const isSubmitting = ref(false);
    const error = ref('');
    const successMessage = ref('');

    const sendRequest = async () => {
      // Clear previous messages
      error.value = '';
      successMessage.value = '';

      // Validate email exists
      if (!patientEmail.value || patientEmail.value.trim() === '') {
        error.value = 'Please enter a valid email address';
        return;
      }

      // Clean the email
      const cleanedEmail = patientEmail.value.trim().toLowerCase();
      console.log('Form sending email:', cleanedEmail);

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(cleanedEmail)) {
        error.value = 'Please enter a valid email address';
        return;
      }

      isSubmitting.value = true;

      try {
        // Send the cleaned email to the store action
        console.log('About to dispatch with email:', cleanedEmail);
        const result = await store.dispatch('practitioner/request_patient_linking', { patientEmail: cleanedEmail });

        if (result && result.success) {
          if (result.message) {
            successMessage.value = result.message;
          } else if (result.patientExists) {
            successMessage.value = 'Request sent successfully! The patient will be notified.';
          } else {
            successMessage.value = 'Invitation sent! The patient will receive an email to join the platform.';
          }
          patientEmail.value = ''; // Clear the form
        } else {
          error.value = (result && result.error) || 'Failed to send request. Please try again.';
        }
      } catch (err) {
        console.error('Error sending request:', err);
        error.value = err.message || 'An unexpected error occurred. Please try again.';
      } finally {
        isSubmitting.value = false;
      }
    };

    return {
      patientEmail,
      isSubmitting,
      error,
      successMessage,
      sendRequest
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.add-patient-form-container {
  position: relative;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 20px;

  .form-header {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: $text-dark;
      }
    }
  }

  .add-patient-form {
    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: $text-dark;
      }

      .input-wrapper {
        position: relative;

        i {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: $primary;
        }

        input {
          width: 100%;
          padding: 12px 12px 12px 40px;
          border: 1px solid $border-color;
          border-radius: 6px;
          font-size: 1rem;
          transition: border-color 0.2s ease;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 2px rgba($primary, 0.2);
          }

          &:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
          }
        }
      }

      .form-hint {
        display: block;
        margin-top: 6px;
        font-size: 0.85rem;
        color: $text-muted;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;

      .action-button {
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: $primary;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease, transform 0.1s ease;

        &:hover:not(:disabled) {
          background-color: darken($primary, 5%);
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          background-color: lighten($primary, 20%);
          cursor: not-allowed;
        }

        i {
          font-size: 0.9rem;
        }
      }
    }
  }

  .success-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    background-color: rgba(#28a745, 0.1);
    border-left: 4px solid #28a745;
    border-radius: 4px;
    padding: 16px;
    margin-top: 20px;

    i {
      color: #28a745;
      font-size: 20px;
    }

    p {
      margin: 0;
      color: $text-dark;
      line-height: 1.5;
    }
  }
}
</style>
