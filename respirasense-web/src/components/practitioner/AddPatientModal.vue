<template>
  <div class="modal-overlay" v-if="isOpen" @click.self="closeModal">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Add New Patient</h3>
        <button class="close-button" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="sendRequest" class="add-patient-form">
          <div class="form-group">
            <label for="patientEmail">Patient Email</label>
            <div class="input-wrapper">
              <i class="fas fa-envelope"></i>
              <input
                type="email"
                id="patientEmail"
                v-model="patientEmail"
                placeholder="Enter patient email address"
                required
              >
            </div>
            <small class="form-hint">Enter the email address the patient used to register</small>
          </div>

          <div class="form-actions">
            <button
              type="button"
              class="cancel-button"
              @click="closeModal"
            >
              <i class="fas fa-times"></i>
              Cancel
            </button>
            <button
              type="submit"
              class="action-button"
              :disabled="isSubmitting"
            >
              <i class="fas fa-paper-plane"></i>
              {{ isSubmitting ? 'Sending...' : 'Send Request' }}
            </button>
          </div>
        </form>

        <div v-if="successMessage" class="success-message">
          <i class="fas fa-check-circle"></i>
          <p>{{ successMessage }}</p>
        </div>

        <div v-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'AddPatientModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'patient-added'],
  setup(props, { emit }) {
    const store = useStore();
    const patientEmail = ref('');
    const isSubmitting = ref(false);
    const successMessage = ref('');
    const error = ref('');

    // Reset form when modal opens
    watch(() => props.isOpen, (newVal) => {
      if (newVal) {
        resetForm();
      }
    });

    const resetForm = () => {
      patientEmail.value = '';
      successMessage.value = '';
      error.value = '';
      isSubmitting.value = false;
    };

    const closeModal = () => {
      emit('close');
    };

    const sendRequest = async () => {
      // Clear previous messages
      error.value = '';
      successMessage.value = '';

      // Validate email exists
      if (!patientEmail.value || patientEmail.value.trim() === '') {
        error.value = 'Please enter a valid email address';
        return;
      }

      // Clean the email
      const cleanedEmail = patientEmail.value.trim().toLowerCase();

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(cleanedEmail)) {
        error.value = 'Please enter a valid email address';
        return;
      }

      isSubmitting.value = true;

      try {
        // Send the cleaned email to the store action
        const result = await store.dispatch('practitioner/request_patient_linking', { patientEmail: cleanedEmail });

        if (result && result.success) {
          if (result.message) {
            successMessage.value = result.message;
          } else if (result.patientExists) {
            successMessage.value = 'Request sent successfully! The patient will be notified.';
          } else {
            successMessage.value = 'Invitation sent! The patient will receive an email to join the platform.';
          }
          
          // Emit event that patient was added
          emit('patient-added');
          
          // Close modal after a delay
          setTimeout(() => {
            closeModal();
          }, 2000);
        } else {
          error.value = (result && result.error) || 'Failed to send request. Please try again.';
        }
      } catch (err) {
        console.error('Error sending patient request:', err);
        error.value = err.message || 'Failed to send request. Please try again.';
      } finally {
        isSubmitting.value = false;
      }
    };

    return {
      patientEmail,
      isSubmitting,
      successMessage,
      error,
      sendRequest,
      closeModal
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba($primary-light, 0.1);

  h3 {
    margin: 0;
    color: $primary;
    font-size: 1.3rem;
    font-weight: 600;
  }

  .close-button {
    background: none;
    border: none;
    color: $dark-grey;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    transition: color 0.2s ease;

    &:hover {
      color: $primary;
    }
  }
}

.modal-body {
  padding: 20px;
}

.add-patient-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: $dark-grey;
      font-size: 0.95rem;
    }

    .input-wrapper {
      position: relative;

      i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: $primary-light;
        font-size: 1rem;
      }

      input {
        width: 100%;
        padding: 12px 12px 12px 40px;
        border: 1px solid rgba($primary-light, 0.3);
        border-radius: 6px;
        font-size: 0.95rem;
        background-color: rgba($primary-light, 0.02);
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: $primary;
          box-shadow: 0 0 0 3px rgba($primary-light, 0.1);
          background-color: white;
        }

        &::placeholder {
          color: lighten($dark-grey, 30%);
        }
      }
    }

    .form-hint {
      display: block;
      margin-top: 8px;
      font-size: 0.85rem;
      color: lighten($dark-grey, 15%);
      font-style: italic;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 24px;
  }
}

.action-button, .cancel-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  i {
    font-size: 0.9rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }
}

.action-button {
  background-color: $primary-light;
  color: white;

  &:hover {
    background-color: $primary;
  }

  &:disabled {
    background-color: lighten($primary-light, 15%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.cancel-button {
  background-color: #f1f1f1;
  color: $dark-grey;

  &:hover {
    background-color: #e5e5e5;
  }
}

.success-message, .error-message {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;

  i {
    margin-right: 12px;
    font-size: 1.2rem;
  }

  p {
    margin: 0;
    font-size: 0.95rem;
  }
}

.success-message {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border-left: 3px solid #28a745;
}

.error-message {
  background-color: rgba($primary, 0.1);
  color: $primary;
  border-left: 3px solid $primary;
}
</style>
