<template>
  <div class="patient-management">
    <div class="section-header">
      <div class="section-title">
        <i class="fas fa-users"></i>
        <h3>My Patients</h3>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading patients...</p>
    </div>

    <div v-else-if="!patients || patients.length === 0" class="empty-state">
      <i class="fas fa-user-plus"></i>
      <p>No patients assigned yet</p>
      <p class="empty-state-subtitle">Use the Link with Patient form to connect with patients</p>
    </div>

    <div v-else class="patients-list">
      <div v-for="patient in patients" :key="patient.id" class="patient-card">
        <div class="patient-info">
          <div class="patient-header">
            <h4>
              <i class="fas fa-user"></i>
              {{ patient.name || 'Unknown Patient' }}
            </h4>
            <span class="patient-status" :class="patient.status || 'active'">
              {{ formatStatus(patient.status || 'active') }}
            </span>
          </div>

          <div class="patient-details">
            <p v-if="patient.email">
              <i class="fas fa-envelope"></i>
              {{ patient.email }}
            </p>
            <p v-if="patient.lastUpdate">
              <i class="fas fa-clock"></i>
              Last Update: {{ formatDate(patient.lastUpdate) }}
            </p>
          </div>
        </div>

        <div class="patient-actions">
          <button @click="viewPatientDetails(patient.id)" class="view-btn">
            <i class="fas fa-eye"></i> View Details
          </button>
          <button @click="confirmUnlink(patient)" class="unlink-btn">
            <i class="fas fa-unlink"></i> Unlink
          </button>
        </div>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmation" class="confirmation-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h4>Confirm Unlink</h4>
          <button @click="cancelUnlink" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to unlink from <strong>{{ selectedPatient?.name || 'this patient' }}</strong>?</p>
          <p class="warning">This action cannot be undone. The patient will need to approve a new request to link again.</p>
        </div>
        <div class="modal-footer">
          <button @click="cancelUnlink" class="cancel-btn">
            <i class="fas fa-times"></i> Cancel
          </button>
          <button @click="unlinkPatient" class="confirm-btn">
            <i class="fas fa-unlink"></i> Unlink
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format } from 'date-fns';
import { removePatientFromPractitioner } from '@/utils/firestore-helpers';
import { auth } from '@/plugins/firebase/firebase';

export default {
  name: 'PatientManagement',

  setup() {
    const store = useStore();
    const router = useRouter();
    const loading = ref(false);
    const error = ref(null);
    const showConfirmation = ref(false);
    const selectedPatient = ref(null);

    // Get patients from store
    const patients = computed(() => {
      return store.getters['practitioner/getPatients'] || [];
    });

    // Format date
    const formatDate = (date) => {
      if (!date) return 'N/A';
      return format(new Date(date), 'MMM d, yyyy h:mm a');
    };

    // Format status
    const formatStatus = (status) => {
      switch (status.toLowerCase()) {
        case 'active': return 'Active';
        case 'pending': return 'Pending';
        case 'inactive': return 'Inactive';
        default: return status;
      }
    };

    // View patient details
    const viewPatientDetails = (patientId) => {
      // Store the patient in Vuex before navigating
      const patient = patients.value.find(p => p.id === patientId);
      if (patient) {
        store.commit('practitioner/SET_SELECTED_PATIENT', patient);
      }
      router.push(`/practitioner/patient/${patientId}`);
    };

    // Confirm unlink
    const confirmUnlink = (patient) => {
      selectedPatient.value = patient;
      showConfirmation.value = true;
    };

    // Cancel unlink
    const cancelUnlink = () => {
      selectedPatient.value = null;
      showConfirmation.value = false;
    };

    // Unlink patient
    const unlinkPatient = async () => {
      if (!selectedPatient.value) return;

      try {
        loading.value = true;
        const user = auth.currentUser;

        if (!user) {
          throw new Error('No authenticated user found');
        }

        await removePatientFromPractitioner(selectedPatient.value.id, user.uid);

        // Refresh patient list
        await store.dispatch('practitioner/fetch_patients');

        // Close modal
        showConfirmation.value = false;
        selectedPatient.value = null;
      } catch (err) {
        console.error('Error unlinking patient:', err);
        error.value = 'Failed to unlink patient';
      } finally {
        loading.value = false;
      }
    };

    // Fetch patients on mount
    onMounted(async () => {
      try {
        loading.value = true;
        await store.dispatch('practitioner/fetch_patients');
      } catch (err) {
        console.error('Error loading patients:', err);
        error.value = 'Failed to load patients';
      } finally {
        loading.value = false;
      }
    });

    return {
      patients,
      loading,
      error,
      showConfirmation,
      selectedPatient,
      formatDate,
      formatStatus,
      viewPatientDetails,
      confirmUnlink,
      cancelUnlink,
      unlinkPatient
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.patient-management {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 24px;
  border-top: 3px solid $primary;

  .section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;

    .spinner {
      width: 36px;
      height: 36px;
      border: 3px solid rgba($primary, 0.2);
      border-radius: 50%;
      border-top-color: $primary;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 12px;
    }

    p {
      color: $dark-grey;
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 0;
    color: $dark-grey;
    background-color: rgba($primary-light, 0.05);
    border-radius: 8px;
    border: 1px dashed rgba($primary-light, 0.2);

    i {
      font-size: 42px;
      margin-bottom: 16px;
      color: rgba($primary-light, 0.4);
    }

    p {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }

    .empty-state-subtitle {
      margin-top: 8px;
      font-size: 0.9rem;
      color: lighten($dark-grey, 15%);
    }
  }

  .patients-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .patient-card {
      border-radius: 8px;
      padding: 16px;
      box-shadow: $shadow-sm;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background-color: rgba($primary-light, 0.02);
      border-left: 4px solid $primary-light;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .patient-info {
        margin-bottom: 16px;

        .patient-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px dashed rgba($primary-light, 0.2);

          h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: $primary;
            display: flex;
            align-items: center;
            gap: 8px;

            i {
              color: $primary-light;
              font-size: 0.9rem;
            }
          }

          .patient-status {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.active {
              background-color: rgba(40, 167, 69, 0.2);
              color: #155724;
            }

            &.pending {
              background-color: rgba(255, 193, 7, 0.2);
              color: #856404;
            }

            &.inactive {
              background-color: rgba(108, 117, 125, 0.2);
              color: #383d41;
            }
          }
        }

        .patient-details {
          background-color: rgba($primary-light, 0.05);
          padding: 10px 12px;
          border-radius: 6px;

          p {
            margin: 0 0 6px 0;
            font-size: 0.9rem;
            color: $dark-grey;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              width: 16px;
              color: $primary-light;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .patient-actions {
        display: flex;
        gap: 8px;

        button {
          flex: 1;
          padding: 8px 12px;
          border: none;
          border-radius: 6px;
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          transition: all 0.2s ease;

          i {
            font-size: 0.85rem;
          }

          &:hover {
            transform: translateY(-1px);
          }
        }

        .view-btn {
          background-color: $primary;
          color: white;

          &:hover {
            background-color: darken($primary, 5%);
          }
        }

        .unlink-btn {
          background-color: rgba($primary, 0.1);
          color: $primary;

          &:hover {
            background-color: rgba($primary, 0.2);
          }
        }
      }
    }
  }

  .confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: white;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      overflow: hidden;

      .modal-header {
        padding: 16px 20px;
        background-color: $primary;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 1.2rem;
          font-weight: 600;
        }

        .close-btn {
          background: none;
          border: none;
          color: white;
          font-size: 1.2rem;
          cursor: pointer;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .modal-body {
        padding: 20px;

        p {
          margin: 0 0 12px 0;
          font-size: 1rem;
          color: $dark-grey;

          &.warning {
            color: $primary;
            font-size: 0.9rem;
            font-style: italic;
          }

          strong {
            font-weight: 600;
          }
        }
      }

      .modal-footer {
        padding: 16px 20px;
        background-color: #f8f9fa;
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        button {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 0.95rem;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 6px;

          i {
            font-size: 0.9rem;
          }
        }

        .cancel-btn {
          background-color: #e9ecef;
          color: #495057;

          &:hover {
            background-color: darken(#e9ecef, 5%);
          }
        }

        .confirm-btn {
          background-color: $primary;
          color: white;

          &:hover {
            background-color: darken($primary, 5%);
          }
        }
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .patient-management {
    .patients-list {
      grid-template-columns: 1fr;
    }

    .patient-actions {
      flex-direction: column;
    }
  }
}
</style>
