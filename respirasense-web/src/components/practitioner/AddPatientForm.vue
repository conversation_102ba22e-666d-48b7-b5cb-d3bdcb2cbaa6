<template>
  <div class="add-patient-form-container">
    <div class="form-header">
      <div class="section-title">
        <i class="fas fa-user-plus"></i>
        <h3>Link with Patient</h3>
      </div>
    </div>

    <form @submit.prevent="sendRequest" class="add-patient-form">
      <div class="form-group">
        <label for="patientEmail">Patient Email</label>
        <div class="input-wrapper">
          <i class="fas fa-envelope"></i>
          <input
            type="email"
            id="patientEmail"
            v-model="patientEmail"
            placeholder="Enter patient email address"
            required
          >
        </div>
        <small class="form-hint">Enter the email address the patient used to register</small>
      </div>

      <div class="form-actions">
        <button
          type="submit"
          class="action-button"
          :disabled="isSubmitting"
        >
          <i class="fas fa-paper-plane"></i>
          {{ isSubmitting ? 'Sending...' : 'Send Request' }}
        </button>
      </div>
    </form>

    <div v-if="successMessage" class="success-message">
      <i class="fas fa-check-circle"></i>
      <p>{{ successMessage }}</p>
    </div>

    <div v-if="error" class="error-message">
      <i class="fas fa-exclamation-circle"></i>
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'AddPatientForm',

  setup() {
    const store = useStore();
    const patientEmail = ref('');
    const isSubmitting = ref(false);
    const successMessage = ref('');
    const error = ref('');

    const sendRequest = async () => {
      // Clear previous messages
      error.value = '';
      successMessage.value = '';

      // Validate email exists
      if (!patientEmail.value || patientEmail.value.trim() === '') {
        error.value = 'Please enter a valid email address';
        return;
      }

      // Clean the email
      const cleanedEmail = patientEmail.value.trim().toLowerCase();
      console.log('Form sending email:', cleanedEmail);

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(cleanedEmail)) {
        error.value = 'Please enter a valid email address';
        return;
      }

      isSubmitting.value = true;

      try {
        // Send the cleaned email to the store action
        console.log('About to dispatch with email:', cleanedEmail);
        const result = await store.dispatch('practitioner/request_patient_linking', { patientEmail: cleanedEmail });

        if (result && result.success) {
          if (result.message) {
            successMessage.value = result.message;
          } else if (result.patientExists) {
            successMessage.value = 'Request sent successfully! The patient will be notified.';
          } else {
            successMessage.value = 'Invitation sent! The patient will receive an email to join the platform.';
          }
          patientEmail.value = ''; // Clear the form
        } else {
          error.value = (result && result.error) || 'Failed to send request. Please try again.';
        }
      } catch (err) {
        console.error('Error sending patient request:', err);
        error.value = err.message || 'Failed to send request. Please try again.';
      } finally {
        isSubmitting.value = false;
      }
    };

    return {
      patientEmail,
      isSubmitting,
      successMessage,
      error,
      sendRequest
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.add-patient-form-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: $shadow-sm;
  margin-bottom: 24px;
  border-top: 3px solid $primary;

  .form-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba($primary-light, 0.1);

    .section-title {
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: $primary;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        color: $primary;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }
  }

  .add-patient-form {
    .form-group {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: $dark-grey;
        font-size: 0.95rem;
      }

      .input-wrapper {
        position: relative;

        i {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: $primary-light;
          font-size: 1rem;
        }

        input {
          width: 100%;
          padding: 12px 12px 12px 40px;
          border: 1px solid rgba($primary-light, 0.3);
          border-radius: 6px;
          font-size: 0.95rem;
          background-color: rgba($primary-light, 0.02);
          transition: all 0.2s ease;

          &:focus {
            outline: none;
            border-color: $primary;
            box-shadow: 0 0 0 3px rgba($primary-light, 0.1);
            background-color: white;
          }

          &::placeholder {
            color: lighten($dark-grey, 30%);
          }
        }
      }

      .form-hint {
        display: block;
        margin-top: 8px;
        font-size: 0.85rem;
        color: lighten($dark-grey, 15%);
        font-style: italic;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
    }
  }

  .action-button {
    padding: 10px 20px;
    background-color: $primary-light;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    i {
      font-size: 0.9rem;
    }

    &:hover {
      background-color: $primary;
      transform: translateY(-1px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      background-color: lighten($primary-light, 15%);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  .success-message, .error-message {
    margin-top: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    display: flex;
    align-items: center;

    i {
      margin-right: 12px;
      font-size: 1.2rem;
    }

    p {
      margin: 0;
      font-size: 0.95rem;
    }
  }

  .success-message {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border-left: 3px solid #28a745;
  }

  .error-message {
    background-color: rgba($primary, 0.1);
    color: $primary;
    border-left: 3px solid $primary;
  }
}
</style>
