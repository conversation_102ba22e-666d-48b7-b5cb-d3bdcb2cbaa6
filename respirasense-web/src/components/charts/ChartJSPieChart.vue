<template>
  <div class="chart-container">
    <h3 v-if="title" class="chart-title">{{ title }}</h3>
    <div v-if="loading" class="chart-loading">
      <div class="spinner"></div>
      <span>Loading chart data...</span>
    </div>
    <div v-else-if="!hasData" class="no-data-message">
      No data available for this period
    </div>
    <div v-else class="chart-wrapper">
      <Pie
        :data="chartData"
        :options="chartOptions"
        :height="height"
        data-metric="riskDistribution"
      />
    </div>
  </div>
</template>

<script>
import { Pie } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale
);

export default {
  name: 'ChartJSPieChart',
  components: {
    Pie
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    }
  },
  computed: {
    hasData() {
      return this.data && this.data.length > 0;
    },
    chartData() {
      if (!this.hasData) {
        return {
          labels: [],
          datasets: []
        };
      }

      // Extract labels and values from data
      const labels = this.data.map(item => item.label);
      const values = this.data.map(item => item.value);

      // Define colors for different risk levels
      const backgroundColors = this.data.map(item => {
        if (item.label.toLowerCase().includes('normal') || item.label.toLowerCase().includes('low')) {
          return 'rgba(75, 192, 192, 0.7)'; // Green for normal/low
        } else if (item.label.toLowerCase().includes('warning') || item.label.toLowerCase().includes('medium')) {
          return 'rgba(255, 159, 64, 0.7)'; // Orange for warning/medium
        } else if (item.label.toLowerCase().includes('danger') || item.label.toLowerCase().includes('high')) {
          return 'rgba(255, 99, 132, 0.7)'; // Red for danger/high
        } else {
          return 'rgba(54, 162, 235, 0.7)'; // Blue for other
        }
      });

      const borderColors = backgroundColors.map(color => color.replace('0.7', '1'));

      return {
        labels,
        datasets: [
          {
            data: values,
            backgroundColor: backgroundColors,
            borderColor: borderColors,
            borderWidth: 1
          }
        ]
      };
    },
    chartOptions() {
      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              boxWidth: 15,
              padding: 15
            }
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const label = context.label || '';
                const value = context.raw || 0;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      };
    }
  }
};
</script>

<style scoped lang="scss">
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .chart-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
  }

  .chart-wrapper {
    position: relative;
    height: 100%;
  }

  .no-data-message {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed #ddd;
  }

  .chart-loading {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(183, 21, 64, 0.3);
      border-radius: 50%;
      border-top-color: #B71540;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  }
}
</style>
