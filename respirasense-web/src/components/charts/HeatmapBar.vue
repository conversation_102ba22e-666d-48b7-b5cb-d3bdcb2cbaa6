<template>
  <div class="heatmap-container">
    <div v-if="loading" class="heatmap-loading">
      <div class="spinner"></div>
      <span>Loading data...</span>
    </div>
    <div v-else-if="!hasData" class="heatmap-no-data">
      <div class="no-data-icon">
        <i class="fas fa-calendar-times"></i>
      </div>
      <p>No data available for this time period</p>
    </div>
    <div v-else class="heatmap-wrapper">
      <div class="heatmap-header">
        <div class="date-range">
          <span>{{ formatDate(startDate) }}</span>
          <span class="date-separator">to</span>
          <span>{{ formatDate(endDate) }}</span>
        </div>
      </div>
      <div class="heatmap-bar">
        <div
          v-for="(point, index) in dataPoints"
          :key="index"
          class="heatmap-cell"
          :class="{ 'has-reading': point.value === 1 }"
          :title="formatTooltip(point)"
          @click="handleCellClick(point)"
        ></div>
      </div>
      <div class="heatmap-legend">
        <div class="legend-item">
          <span class="legend-marker has-reading"></span>
          <span>Reading Taken</span>
        </div>
        <div class="legend-item">
          <span class="legend-marker no-reading"></span>
          <span>No Reading</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import { format } from 'date-fns';

export default {
  name: 'HeatmapBar',
  props: {
    series: {
      type: [Array, Promise],
      required: true
    },
    timeRange: {
      type: String,
      default: '6m' // Default to 6 months
    },
    loading: {
      type: Boolean,
      default: false
    },
    fromDate: {
      type: Date,
      default: null
    },
    toDate: {
      type: Date,
      default: null
    }
  },
  emits: ['cell-click'],
  setup(props, { emit }) {
    // Track resolved series data
    const resolvedSeries = ref(null);
    const dataPoints = ref([]);

    // Watch for changes in the series prop, especially if it's a Promise
    watch(() => props.series, async (newSeries) => {
      try {
        // If the series is a Promise, resolve it
        if (newSeries && typeof newSeries.then === 'function') {
          try {
            resolvedSeries.value = await newSeries;
            processData();
          } catch (promiseError) {
            console.error('Error resolving Promise:', promiseError);
            resolvedSeries.value = null;
          }
        } else {
          // Otherwise, just use the value directly
          resolvedSeries.value = newSeries;
          processData();
        }
      } catch (error) {
        console.error('Error handling series data:', error);
        resolvedSeries.value = null;
      }
    }, { immediate: true });

    // Watch for changes in the timeRange prop
    watch(() => props.timeRange, () => {
      processData();
    });

    // Process the data for the heatmap
    const processData = () => {
      if (!hasData.value) {
        dataPoints.value = [];
        return;
      }

      const series = resolvedSeries.value || props.series;
      if (!series || !series[0] || !series[0].data) {
        dataPoints.value = [];
        return;
      }

      // Extract data points from the series
      dataPoints.value = series[0].data.map(point => ({
        date: new Date(point.x),
        value: point.y
      }));
    };

    // Check if we have valid data
    const hasData = computed(() => {
      const series = resolvedSeries.value || props.series;

      // If series is a Promise, we don't have data yet
      if (series && typeof series.then === 'function') {
        return false;
      }

      return series &&
             Array.isArray(series) &&
             series.length > 0 &&
             series[0]?.data &&
             Array.isArray(series[0].data) &&
             series[0].data.length > 0;
    });

    // Get the date range
    const { startDate, endDate } = computed(() => {
      // If fromDate and toDate props are provided, use them
      if (props.fromDate && props.toDate) {
        return {
          startDate: props.fromDate,
          endDate: props.toDate
        };
      }

      // Otherwise, calculate from data points
      if (!hasData.value || !dataPoints.value.length) {
        return {
          startDate: new Date(),
          endDate: new Date()
        };
      }

      // Sort data points by date
      const sortedPoints = [...dataPoints.value].sort((a, b) => a.date - b.date);

      return {
        startDate: sortedPoints[0].date,
        endDate: sortedPoints[sortedPoints.length - 1].date
      };
    }).value;

    // Format date for display
    const formatDate = (date) => {
      return format(date, 'MMM d, yyyy');
    };

    // Format tooltip for heatmap cell
    const formatTooltip = (point) => {
      const dateStr = format(point.date, 'MMM d, yyyy');
      return point.value === 1
        ? `Reading taken on ${dateStr}`
        : `No reading on ${dateStr}`;
    };

    // Handle cell click
    const handleCellClick = (point) => {
      emit('cell-click', point);
    };

    // Initialize
    onMounted(() => {
      processData();
    });

    return {
      dataPoints,
      hasData,
      startDate,
      endDate,
      formatDate,
      formatTooltip,
      handleCellClick
    };
  }
};
</script>

<style lang="scss" scoped>
.heatmap-container {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .heatmap-loading {
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;

    .spinner {
      width: 24px;
      height: 24px;
      border: 3px solid rgba(183, 21, 64, 0.3);
      border-radius: 50%;
      border-top-color: #B71540;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 10px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  }

  .heatmap-no-data {
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;

    .no-data-icon {
      font-size: 24px;
      color: #aaa;
      margin-bottom: 10px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .heatmap-wrapper {
    .heatmap-header {
      margin-bottom: 10px;

      .date-range {
        font-size: 14px;
        color: #555;
        text-align: center;

        .date-separator {
          margin: 0 5px;
          color: #999;
        }
      }
    }

    .heatmap-bar {
      display: flex;
      height: 30px;
      border-radius: 4px;
      overflow: hidden;
      background-color: #f5f5f5;

      .heatmap-cell {
        flex: 1;
        height: 100%;
        background-color: #e0e0e0;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: transform 0.2s ease;

        &:last-child {
          border-right: none;
        }

        &.has-reading {
          background-color: #B71540;
        }

        &:hover {
          transform: scaleY(1.1);
        }
      }
    }

    .heatmap-legend {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      gap: 15px;

      .legend-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #666;

        .legend-marker {
          width: 12px;
          height: 12px;
          margin-right: 5px;
          border-radius: 2px;

          &.has-reading {
            background-color: #B71540;
          }

          &.no-reading {
            background-color: #e0e0e0;
          }
        }
      }
    }
  }
}
</style>
