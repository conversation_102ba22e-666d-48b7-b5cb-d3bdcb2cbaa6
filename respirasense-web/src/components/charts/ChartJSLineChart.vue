<template>
  <div class="chart-container">
    <h3 v-if="title" class="chart-title">{{ title }}</h3>
    <div v-if="loading" class="chart-loading">
      <div class="spinner"></div>
      <span>Loading chart data...</span>
    </div>
    <div v-else-if="!hasData" class="no-data-message">
      No data available for this period
    </div>
    <div v-else class="chart-wrapper">
      <Line
        :data="chartData"
        :options="chartOptions"
        :height="height"
        :data-metric="metricType"
      />
    </div>
  </div>
</template>

<script>
import { Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  CategoryScale,
  TimeScale
} from 'chart.js';
import 'chartjs-adapter-date-fns';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  CategoryScale,
  TimeScale
);

export default {
  name: 'ChartJSLineChart',
  components: {
    Line
  },
  props: {
    series: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    },
    thresholds: {
      type: Object,
      default: () => ({})
    },
    yAxisLabel: {
      type: String,
      default: ''
    },
    metricType: {
      type: String,
      default: ''
    }
  },
  computed: {
    hasData() {
      return this.series &&
             this.series.length > 0 &&
             this.series[0].data &&
             this.series[0].data.length > 0;
    },
    chartData() {
      if (!this.hasData) {
        return {
          labels: [],
          datasets: []
        };
      }

      // Get the data from the series
      const datasets = this.series.map(serie => {
        return {
          label: serie.name,
          data: serie.data.map(point => ({
            x: new Date(point.x),
            y: point.y
          })),
          borderColor: '#B71540',
          backgroundColor: 'rgba(183, 21, 64, 0.1)',
          borderWidth: 2,
          pointBackgroundColor: '#B71540',
          pointBorderColor: '#fff',
          pointRadius: 4,
          pointHoverRadius: 6,
          fill: false,
          tension: 0.4
        };
      });

      // Add threshold lines if provided
      if (this.thresholds) {
        // Add upper threshold if it exists
        if (this.thresholds.upper !== undefined) {
          datasets.push(this.createThresholdDataset(
            `Upper ${this.yAxisLabel} Threshold`,
            this.thresholds.upper,
            'rgba(255, 0, 0, 0.5)'
          ));
        }

        // Add lower threshold if it exists
        if (this.thresholds.lower !== undefined) {
          datasets.push(this.createThresholdDataset(
            `Lower ${this.yAxisLabel} Threshold`,
            this.thresholds.lower,
            'rgba(255, 0, 0, 0.5)'
          ));
        }
      }

      return {
        datasets
      };
    },
    chartOptions() {
      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              title: (tooltipItems) => {
                const date = new Date(tooltipItems[0].parsed.x);
                return date.toLocaleString();
              },
              label: (context) => {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }

                // Format the value based on metric type
                let value = context.parsed.y;
                if (this.metricType === 'respiratoryRate') {
                  label += `${value} bpm`;
                } else if (this.metricType === 'oxygenSaturation') {
                  label += `${value}%`;
                } else if (this.metricType === 'heartRate') {
                  label += `${value} bpm`;
                } else if (this.metricType === 'temperature') {
                  label += `${value}°C`;
                } else {
                  label += value;
                }

                return label;
              }
            }
          },
          legend: {
            display: true,
            position: 'top'
          }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              unit: 'day',
              displayFormats: {
                day: 'MMM d'
              }
            },
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: !!this.yAxisLabel,
              text: this.yAxisLabel
            },
            beginAtZero: false
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        },
        animations: {
          tension: {
            duration: 1000,
            easing: 'linear'
          }
        }
      };
    }
  },
  methods: {
    createThresholdDataset(label, value, color) {
      // Create a dataset for a threshold line
      if (!this.hasData || !this.series[0].data.length) {
        return {
          label,
          data: [],
          borderColor: color,
          borderWidth: 2,
          borderDash: [5, 5],
          pointRadius: 0,
          fill: false
        };
      }

      // Get the x values from the main dataset
      const xValues = this.series[0].data.map(point => new Date(point.x));

      // Create a flat line at the threshold value
      return {
        label,
        data: xValues.map(x => ({ x, y: value })),
        borderColor: color,
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      };
    }
  }
};
</script>

<style scoped lang="scss">
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .chart-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
  }

  .chart-wrapper {
    position: relative;
    height: 100%;
  }

  .no-data-message {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed #ddd;
  }

  .chart-loading {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(183, 21, 64, 0.3);
      border-radius: 50%;
      border-top-color: #B71540;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  }
}
</style>
