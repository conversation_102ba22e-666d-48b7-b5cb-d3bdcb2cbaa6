<template>
  <div class="trend-chart-container">
    <div v-if="!hasData" class="no-data">
      --
    </div>
    <div v-else class="chart-wrapper">
      <Line
        :data="chartData"
        :options="chartOptions"
        :height="height"
      />
    </div>
  </div>
</template>

<script>
import { Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip
);

export default {
  name: 'ChartJSTrendChart',
  components: {
    Line
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    height: {
      type: Number,
      default: 80
    }
  },
  computed: {
    hasData() {
      return this.data && this.data.length > 1;
    },
    chartData() {
      if (!this.hasData) {
        return {
          labels: [],
          datasets: []
        };
      }

      // Extract values and labels
      const values = this.data.map(point => point.value);
      const labels = this.data.map((_, index) => '');  // Empty labels for cleaner look

      return {
        labels,
        datasets: [
          {
            data: values,
            borderColor: '#B71540',
            backgroundColor: 'rgba(183, 21, 64, 0.1)',
            borderWidth: 2,
            pointRadius: 0,
            pointHoverRadius: 3,
            tension: 0.4,
            fill: true
          }
        ]
      };
    },
    chartOptions() {
      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: true,
            mode: 'index',
            intersect: false,
            displayColors: false,
            callbacks: {
              title: () => '',
              label: (context) => `Value: ${context.raw}`
            }
          }
        },
        scales: {
          x: {
            display: false
          },
          y: {
            display: false,
            beginAtZero: false
          }
        },
        elements: {
          line: {
            tension: 0.4
          }
        },
        animation: {
          duration: 500
        },
        interaction: {
          mode: 'nearest',
          intersect: false
        }
      };
    }
  }
};
</script>

<style scoped lang="scss">
.trend-chart-container {
  width: 100%;
  height: 100%;
  min-height: 40px;
  position: relative;
  
  .chart-wrapper {
    width: 100%;
    height: 100%;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 1.2rem;
  }
}
</style>
