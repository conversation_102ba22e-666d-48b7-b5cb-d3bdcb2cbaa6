<template>
  <footer class="app-footer">
    <div class="footer-content">
      <div class="footer-section">
        <img :src="logo" alt="RespiraSense Logo" class="footer-logo" />
      </div>

      <div class="footer-section">
        <h4>Quick Links</h4>
        <router-link to="/help">Help Center</router-link>
        <router-link to="/privacy">Privacy Policy</router-link>
        <router-link to="/terms">Terms of Service</router-link>
      </div>

      <div class="footer-section">
        <h4>Contact</h4>
        <p>Support: <EMAIL></p>
        <a href="https://github.com/kimeudom"><p>Github</p> </a>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; {{ currentYear }} RespiraSense. All rights reserved.</p>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter',
  data() {
    return {
      logo: require('@/assets/<EMAIL>'),
      currentYear: new Date().getFullYear()
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';
.app-footer {
  background: $footer-bg;
  color: $white;
  padding: 2rem 0;
  margin-top: auto;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 0 2rem;

    .footer-section {
      .footer-logo {
        height: 30px;
        margin-bottom: 1rem;
      }

      h4 {
        margin-bottom: 1rem;
        color: $white;
      }

      a {
        display: block;
        color: $light-grey;
        text-decoration: none;
        margin-bottom: 0.5rem;

        &:hover {
          color: $white;
        }
      }

      p {
        color: $light-grey;
        margin-bottom: 0.5rem;
      }
    }
  }

  .footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: $light-grey;
  }
}
</style>