<template>
  <header class="app-header">
    <div class="header-left">
      <img :src="logo" alt="RespiraSense Logo" class="logo" @click="navigateToDashboard" style="cursor: pointer" />
    </div>

    <div class="header-right">
      <div class="profile-menu">
        <div class="profile-trigger" @click="toggleProfileMenu">
          <img :src="userPhotoUrl" alt="Profile" class="profile-photo" @error="handleImageError" />
          <span class="user-name">{{ userName }}</span>
          <i class="fas fa-chevron-down"></i>
        </div>

        <div v-if="showProfileMenu" class="profile-dropdown">
          <div class="profile-header">
            <img :src="userPhotoUrl" alt="Profile" class="profile-photo-large" @error="handleImageError" />
            <div class="profile-info">
              <span class="user-name">{{ userName }}</span>
              <span class="user-email">{{ userEmail }}</span>
            </div>
          </div>
          <div class="profile-actions">
            <router-link to="/profile" class="menu-item">
              <i class="fas fa-user"></i> Manage Account
            </router-link>
            <router-link to="/settings" class="menu-item">
              <i class="fas fa-cog"></i> Settings
            </router-link>
            <button @click="handleLogout" class="menu-item logout">
              <i class="fas fa-sign-out-alt"></i> Sign Out
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { auth } from '@/plugins/firebase/firebase';
import { getProfilePhotoUrl, handleImageError as imageErrorHandler } from '@/utils/profile-utils';
import defaultUserIcon from '@/assets/DefaultUserIcon.webp';

export default {
  name: 'AppHeader',
  setup() {
    const store = useStore();
    const router = useRouter();
    const showProfileMenu = ref(false);
    const logo = require('@/assets/<EMAIL>');
    const userPhotoUrl = ref('');
    const userName = ref('');
    const userEmail = ref('');

    const navigateToDashboard = () => {
      const userRole = localStorage.getItem('userRole');
      const dashboardRoutes = {
        'admin': '/admin/dashboard',
        'superAdmin': '/admin/dashboard',
        'practitioner': '/practitioner/dashboard',
        'patient': '/patient/dashboard'
      };

      const dashboardPath = dashboardRoutes[userRole];
      if (dashboardPath) {
        router.push(dashboardPath);
      }
    };

    const updateUserProfile = async () => {
      const user = auth.currentUser;
      if (user) {
        // Get profile photo URL using our utility function
        userPhotoUrl.value = await getProfilePhotoUrl(user);

        // Set user name and email
        userName.value = user.displayName || localStorage.getItem('userName') || 'User';
        userEmail.value = user.email;
      }
    };

    onMounted(() => {
      updateUserProfile();
      // Listen for auth state changes
      auth.onAuthStateChanged(updateUserProfile);
      document.addEventListener('click', handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });

    const toggleProfileMenu = () => {
      showProfileMenu.value = !showProfileMenu.value;
    };

    const handleLogout = async () => {
      try {
        // Use the correct action name with proper capitalization
        await store.dispatch('authFirebase/Logout');
        router.push('/auth/login');
      } catch (error) {
        console.error('Logout error:', error);
      }
    };

    const handleClickOutside = (event) => {
      const menu = document.querySelector('.profile-menu');
      if (menu && !menu.contains(event.target)) {
        showProfileMenu.value = false;
      }
    };

    const handleImageError = (e) => {
      // Use our utility function to handle image errors
      imageErrorHandler(e);
    };

    return {
      logo,
      showProfileMenu,
      userPhotoUrl,
      userName,
      userEmail,
      toggleProfileMenu,
      handleLogout,
      navigateToDashboard,
      handleImageError
    };
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';
@import '@/assets/styles/base/mixins';

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 2rem;
  background: $header-bg;
  color: $white;
  box-shadow: $shadow-sm;
  height: 60px;
}

.header-left {
  .logo {
    height: 40px;
    width: auto;
  }
}

.profile-menu {
  position: relative;

  .profile-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 24px;
    transition: background-color 0.2s;
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .profile-photo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
  }

  .profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    width: 280px;
    z-index: 1000;
  }

  .profile-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 1rem;

    .profile-photo-large {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover;
    }

    .profile-info {
      display: flex;
      flex-direction: column;

      .user-email {
        font-size: 0.875rem;
        color: #666;
      }
    }
  }

  .profile-actions {
    padding: 0.5rem;

    .menu-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      color: #333;
      text-decoration: none;
      cursor: pointer;
      border-radius: 4px;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      font-size: 0.9375rem;

      &:hover {
        background-color: #f5f5f5;
      }

      &.logout {
        color: $danger;
      }

      i {
        width: 20px;
      }
    }
  }
}
</style>
