<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-container">
      <div class="modal-header">
        <h2>Edit Practitioner</h2>
        <button class="close-button" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-section">
            <h3>Account Information</h3>
            
            <div class="form-group">
              <label for="email">Email Address</label>
              <input 
                id="email"
                v-model="form.email"
                type="email"
                disabled
                class="disabled"
              />
              <small class="helper-text">Email cannot be changed</small>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name <span class="required">*</span></label>
                <input 
                  id="firstName"
                  v-model="form.firstName"
                  type="text"
                  required
                  placeholder="First name"
                  :class="{ 'error': errors.firstName }"
                />
                <span v-if="errors.firstName" class="error-message">{{ errors.firstName }}</span>
              </div>
              
              <div class="form-group">
                <label for="lastName">Last Name <span class="required">*</span></label>
                <input 
                  id="lastName"
                  v-model="form.lastName"
                  type="text"
                  required
                  placeholder="Last name"
                  :class="{ 'error': errors.lastName }"
                />
                <span v-if="errors.lastName" class="error-message">{{ errors.lastName }}</span>
              </div>
            </div>
          </div>
          
          <div class="form-section">
            <h3>Professional Details</h3>
            
            <div class="form-group">
              <label for="specialty">Specialty</label>
              <select id="specialty" v-model="form.specialty">
                <option value="">Select specialty</option>
                <option value="General Practitioner">General Practitioner</option>
                <option value="Pulmonologist">Pulmonologist</option>
                <option value="Cardiologist">Cardiologist</option>
                <option value="Internal Medicine">Internal Medicine</option>
                <option value="Emergency Medicine">Emergency Medicine</option>
                <option value="Other">Other</option>
              </select>
            </div>
            
            <div v-if="form.specialty === 'Other'" class="form-group">
              <label for="otherSpecialty">Specify Specialty</label>
              <input 
                id="otherSpecialty"
                v-model="form.otherSpecialty"
                type="text"
                placeholder="Enter specialty"
              />
            </div>
            
            <div class="form-group">
              <label for="licenseNumber">License Number</label>
              <input 
                id="licenseNumber"
                v-model="form.licenseNumber"
                type="text"
                placeholder="Enter license number"
              />
            </div>
          </div>
          
          <div class="form-section">
            <h3>Access Settings</h3>
            
            <div class="form-group">
              <label for="status">Status</label>
              <select id="status" v-model="form.status">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
              </select>
            </div>
            
            <div class="form-group checkbox-group">
              <input 
                id="resetPassword"
                v-model="form.resetPassword"
                type="checkbox"
              />
              <label for="resetPassword">Send password reset email</label>
            </div>
          </div>
          
          <div class="form-actions">
            <button 
              type="button" 
              class="btn-cancel" 
              @click="$emit('close')"
              :disabled="isSubmitting"
            >
              Cancel
            </button>
            <button 
              type="submit" 
              class="btn-primary" 
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting">
                <i class="fas fa-spinner fa-spin"></i> Saving...
              </span>
              <span v-else>
                <i class="fas fa-save"></i> Save Changes
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useStore } from 'vuex';
import { doc, updateDoc } from 'firebase/firestore';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth, db } from '@/plugins/firebase/firebase';
import { createAuditLog } from '@/utils/auditLogger';

export default {
  name: 'EditPractitionerModal',
  
  props: {
    practitioner: {
      type: Object,
      required: true
    }
  },
  
  emits: ['close', 'practitioner-updated'],
  
  setup(props, { emit }) {
    const store = useStore();
    const isSubmitting = ref(false);
    
    const form = reactive({
      email: '',
      firstName: '',
      lastName: '',
      specialty: '',
      otherSpecialty: '',
      licenseNumber: '',
      status: 'active',
      resetPassword: false
    });
    
    const errors = reactive({
      firstName: '',
      lastName: ''
    });
    
    onMounted(() => {
      // Initialize form with practitioner data
      form.email = props.practitioner.email || '';
      
      // Handle name fields
      if (props.practitioner.name) {
        const nameParts = props.practitioner.name.split(' ');
        form.firstName = props.practitioner.firstName || nameParts[0] || '';
        form.lastName = props.practitioner.lastName || nameParts.slice(1).join(' ') || '';
      } else {
        form.firstName = props.practitioner.firstName || '';
        form.lastName = props.practitioner.lastName || '';
      }
      
      // Handle specialty
      const knownSpecialties = [
        'General Practitioner',
        'Pulmonologist',
        'Cardiologist',
        'Internal Medicine',
        'Emergency Medicine'
      ];
      
      if (props.practitioner.specialty) {
        if (knownSpecialties.includes(props.practitioner.specialty)) {
          form.specialty = props.practitioner.specialty;
        } else {
          form.specialty = 'Other';
          form.otherSpecialty = props.practitioner.specialty;
        }
      } else {
        form.specialty = '';
      }
      
      form.licenseNumber = props.practitioner.licenseNumber || '';
      form.status = props.practitioner.status || 'active';
    });
    
    const validateForm = () => {
      let isValid = true;
      
      // Reset errors
      errors.firstName = '';
      errors.lastName = '';
      
      // Validate first name
      if (!form.firstName) {
        errors.firstName = 'First name is required';
        isValid = false;
      }
      
      // Validate last name
      if (!form.lastName) {
        errors.lastName = 'Last name is required';
        isValid = false;
      }
      
      return isValid;
    };
    
    const handleSubmit = async () => {
      if (!validateForm()) {
        return;
      }
      
      isSubmitting.value = true;
      
      try {
        const now = new Date().toISOString();
        const fullName = `${form.firstName} ${form.lastName}`;
        
        // Determine specialty
        const specialty = form.specialty === 'Other' ? form.otherSpecialty : form.specialty;
        
        // Update practitioner document in Firestore
        const practitionerRef = doc(db, 'users_roles', props.practitioner.id);
        await updateDoc(practitionerRef, {
          name: fullName,
          firstName: form.firstName,
          lastName: form.lastName,
          specialty: specialty || null,
          licenseNumber: form.licenseNumber || null,
          status: form.status,
          updatedAt: now
        });
        
        // Send password reset email if requested
        if (form.resetPassword) {
          await sendPasswordResetEmail(auth, form.email);
        }
        
        // Create audit log
        await createAuditLog({
          action: 'update_practitioner',
          userId: props.practitioner.id,
          performedBy: store.state.auth.user.uid,
          details: {
            name: fullName,
            specialty: specialty || null,
            status: form.status,
            passwordReset: form.resetPassword
          }
        });
        
        emit('practitioner-updated');
      } catch (error) {
        console.error('Failed to update practitioner:', error);
        alert(`Error: ${error.message}`);
      } finally {
        isSubmitting.value = false;
      }
    };
    
    return {
      form,
      errors,
      isSubmitting,
      handleSubmit
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: rgba($primary-subtle, 0.1);
  border-bottom: 1px solid rgba($primary-light, 0.1);
  
  h2 {
    margin: 0;
    color: $primary;
    font-size: 1.25rem;
  }
  
  .close-button {
    background: none;
    border: none;
    color: $dark-grey;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
    
    &:hover {
      color: $primary;
    }
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  
  form {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.form-section {
  border: 1px solid rgba($dark-grey, 0.1);
  border-radius: 8px;
  padding: 16px;
  
  h3 {
    margin: 0 0 16px 0;
    color: $dark-grey;
    font-size: 1rem;
    font-weight: 600;
  }
}

.form-row {
  display: flex;
  gap: 16px;
  
  .form-group {
    flex: 1;
  }
}

.form-group {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  label {
    display: block;
    margin-bottom: 6px;
    color: $dark-grey;
    font-size: 0.9rem;
    font-weight: 500;
    
    .required {
      color: $danger;
    }
  }
  
  input[type="text"],
  input[type="email"],
  select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba($dark-grey, 0.2);
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    
    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba($primary, 0.1);
    }
    
    &.error {
      border-color: $danger;
    }
    
    &.disabled {
      background-color: rgba($dark-grey, 0.05);
      cursor: not-allowed;
    }
  }
  
  .helper-text {
    display: block;
    color: rgba($dark-grey, 0.6);
    font-size: 0.8rem;
    margin-top: 4px;
  }
  
  .error-message {
    display: block;
    color: $danger;
    font-size: 0.8rem;
    margin-top: 4px;
  }
  
  &.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    
    input[type="checkbox"] {
      margin: 0;
    }
    
    label {
      margin-bottom: 0;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
  
  button {
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
    
    i {
      font-size: 0.9rem;
    }
  }
  
  .btn-cancel {
    background-color: transparent;
    border: 1px solid rgba($dark-grey, 0.2);
    color: $dark-grey;
    
    &:hover:not(:disabled) {
      background-color: rgba($dark-grey, 0.05);
    }
  }
  
  .btn-primary {
    background-color: $primary;
    border: none;
    color: $white;
    
    &:hover:not(:disabled) {
      background-color: darken($primary, 10%);
    }
  }
}
</style>
