<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-container">
      <div class="modal-header">
        <h2>Practitioner Details</h2>
        <button class="close-button" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading practitioner details...</p>
        </div>
        
        <div v-else-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ error }}</p>
          <button @click="loadPractitionerDetails" class="btn-retry">Retry</button>
        </div>
        
        <div v-else class="practitioner-details">
          <div class="profile-header">
            <div class="profile-avatar">
              <img 
                :src="practitioner.photoURL || require('@/assets/DefaultUserIcon.webp')" 
                alt="Profile" 
                @error="handleImageError"
              />
            </div>
            <div class="profile-info">
              <h3>{{ practitioner.name || `${practitioner.firstName} ${practitioner.lastName}` || 'Unnamed' }}</h3>
              <p class="profile-role">
                <i class="fas fa-user-md"></i> {{ practitioner.specialty || 'General Practitioner' }}
              </p>
              <p class="profile-email">
                <i class="fas fa-envelope"></i> {{ practitioner.email }}
              </p>
              <div class="profile-status">
                <span :class="['status-badge', practitioner.status]">
                  {{ practitioner.status }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="details-section">
            <h4>Account Information</h4>
            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">User ID</span>
                <span class="detail-value">{{ practitioner.id }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">License Number</span>
                <span class="detail-value">{{ practitioner.licenseNumber || 'Not specified' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Created</span>
                <span class="detail-value">{{ formatDate(practitioner.created_at) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Last Updated</span>
                <span class="detail-value">{{ formatDate(practitioner.updatedAt) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Last Login</span>
                <span class="detail-value">{{ practitioner.last_login ? formatDate(practitioner.last_login) : 'Never' }}</span>
              </div>
            </div>
          </div>
          
          <div class="details-section">
            <div class="section-header">
              <h4>Assigned Patients</h4>
              <span class="patient-count">{{ patients.length }} patients</span>
            </div>
            
            <div v-if="isLoadingPatients" class="loading-container small">
              <div class="loading-spinner"></div>
              <p>Loading patients...</p>
            </div>
            
            <div v-else-if="!patients.length" class="empty-patients">
              <i class="fas fa-user-friends"></i>
              <p>No patients assigned to this practitioner</p>
            </div>
            
            <div v-else class="patients-list">
              <div v-for="patient in patients" :key="patient.id" class="patient-item">
                <div class="patient-avatar">
                  <img 
                    :src="patient.photoURL || require('@/assets/DefaultUserIcon.webp')" 
                    alt="Patient" 
                    @error="handleImageError"
                  />
                </div>
                <div class="patient-info">
                  <span class="patient-name">{{ patient.name || 'Unnamed Patient' }}</span>
                  <span class="patient-email">{{ patient.email }}</span>
                </div>
                <div class="patient-status">
                  <span :class="['status-badge', patient.status]">{{ patient.status }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="details-section">
            <h4>Activity Log</h4>
            
            <div v-if="isLoadingActivity" class="loading-container small">
              <div class="loading-spinner"></div>
              <p>Loading activity...</p>
            </div>
            
            <div v-else-if="!activityLog.length" class="empty-activity">
              <i class="fas fa-history"></i>
              <p>No recent activity</p>
            </div>
            
            <div v-else class="activity-list">
              <div v-for="activity in activityLog" :key="activity.id" class="activity-item">
                <div class="activity-icon">
                  <i :class="getActivityIcon(activity.action)"></i>
                </div>
                <div class="activity-content">
                  <span class="activity-description">{{ getActivityDescription(activity) }}</span>
                  <span class="activity-time">{{ formatDate(activity.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="btn-secondary" @click="$emit('close')">Close</button>
        <button class="btn-primary" @click="editPractitioner">
          <i class="fas fa-edit"></i> Edit Practitioner
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/dateFormatter';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/plugins/firebase/firebase';

export default {
  name: 'PractitionerDetailsModal',
  
  props: {
    practitioner: {
      type: Object,
      required: true
    }
  },
  
  emits: ['close', 'edit'],
  
  setup(props, { emit }) {
    const store = useStore();
    const isLoading = ref(false);
    const isLoadingPatients = ref(false);
    const isLoadingActivity = ref(false);
    const error = ref(null);
    const patients = ref([]);
    const activityLog = ref([]);
    
    const loadPractitionerDetails = async () => {
      isLoading.value = true;
      error.value = null;
      
      try {
        // Load patients and activity in parallel
        await Promise.all([
          loadPatients(),
          loadActivityLog()
        ]);
      } catch (err) {
        console.error('Failed to load practitioner details:', err);
        error.value = 'Failed to load practitioner details. Please try again.';
      } finally {
        isLoading.value = false;
      }
    };
    
    const loadPatients = async () => {
      isLoadingPatients.value = true;
      
      try {
        // Query patients collection for all patients assigned to this practitioner
        const patientsRef = collection(db, 'users_roles');
        const q = query(
          patientsRef,
          where('role', '==', 'patient'),
          where('practitionerId', '==', props.practitioner.id)
        );
        
        const querySnapshot = await getDocs(q);
        
        // Map the documents to an array of patient data
        patients.value = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      } catch (err) {
        console.error('Failed to load patients:', err);
      } finally {
        isLoadingPatients.value = false;
      }
    };
    
    const loadActivityLog = async () => {
      isLoadingActivity.value = true;
      
      try {
        // Query audit_logs collection for activities related to this practitioner
        const logsRef = collection(db, 'audit_logs');
        const q = query(
          logsRef,
          where('userId', '==', props.practitioner.id),
          orderBy('timestamp', 'desc'),
          limit(10)
        );
        
        const querySnapshot = await getDocs(q);
        
        // Map the documents to an array of activity data
        activityLog.value = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp
        }));
      } catch (err) {
        console.error('Failed to load activity log:', err);
      } finally {
        isLoadingActivity.value = false;
      }
    };
    
    const handleImageError = (e) => {
      e.target.src = require('@/assets/DefaultUserIcon.webp');
    };
    
    const editPractitioner = () => {
      emit('close');
      emit('edit', props.practitioner);
    };
    
    const getActivityIcon = (action) => {
      const icons = {
        'login': 'fas fa-sign-in-alt',
        'logout': 'fas fa-sign-out-alt',
        'update_profile': 'fas fa-user-edit',
        'password_reset': 'fas fa-key',
        'patient_assigned': 'fas fa-user-plus',
        'patient_removed': 'fas fa-user-minus',
        'view_patient': 'fas fa-eye',
        'create_report': 'fas fa-file-medical',
        'default': 'fas fa-history'
      };
      
      return icons[action] || icons.default;
    };
    
    const getActivityDescription = (activity) => {
      const descriptions = {
        'login': 'Logged in',
        'logout': 'Logged out',
        'update_profile': 'Updated profile',
        'password_reset': 'Reset password',
        'patient_assigned': `Patient ${activity.details?.patientName || 'Unknown'} assigned`,
        'patient_removed': `Patient ${activity.details?.patientName || 'Unknown'} removed`,
        'view_patient': `Viewed patient ${activity.details?.patientName || 'Unknown'}`,
        'create_report': `Created report for ${activity.details?.patientName || 'Unknown'}`
      };
      
      return descriptions[activity.action] || `${activity.action.replace('_', ' ')}`;
    };
    
    onMounted(() => {
      loadPractitionerDetails();
    });
    
    return {
      isLoading,
      isLoadingPatients,
      isLoadingActivity,
      error,
      patients,
      activityLog,
      loadPractitionerDetails,
      handleImageError,
      editPractitioner,
      getActivityIcon,
      getActivityDescription,
      formatDate
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/base/settings';

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: rgba($primary-subtle, 0.1);
  border-bottom: 1px solid rgba($primary-light, 0.1);
  
  h2 {
    margin: 0;
    color: $primary;
    font-size: 1.25rem;
  }
  
  .close-button {
    background: none;
    border: none;
    color: $dark-grey;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
    
    &:hover {
      color: $primary;
    }
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  &.small {
    padding: 20px;
    
    .loading-spinner {
      width: 24px;
      height: 24px;
      border-width: 2px;
      margin-bottom: 8px;
    }
    
    p {
      font-size: 0.8rem;
    }
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba($primary, 0.2);
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  p {
    color: $dark-grey;
    font-size: 0.9rem;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  
  i {
    font-size: 2rem;
    color: $danger;
    margin-bottom: 16px;
  }
  
  p {
    color: $dark-grey;
    margin-bottom: 16px;
  }
  
  .btn-retry {
    padding: 8px 16px;
    background-color: $primary;
    color: $white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}

.practitioner-details {
  .profile-header {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid rgba($dark-grey, 0.1);
    
    .profile-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid rgba($primary-light, 0.2);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .profile-info {
      flex: 1;
      
      h3 {
        margin: 0 0 8px 0;
        color: $dark-grey;
        font-size: 1.5rem;
      }
      
      .profile-role,
      .profile-email {
        margin: 0 0 8px 0;
        color: rgba($dark-grey, 0.8);
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: $primary;
          width: 16px;
        }
      }
      
      .profile-status {
        margin-top: 12px;
        
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 0.8rem;
          font-weight: 500;
          
          &.active {
            background-color: rgba($success, 0.1);
            color: $success;
          }
          
          &.pending {
            background-color: rgba($warning, 0.1);
            color: $warning;
          }
          
          &.inactive {
            background-color: rgba($danger, 0.1);
            color: $danger;
          }
        }
      }
    }
  }
  
  .details-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: rgba($primary-subtle, 0.05);
    border-radius: 8px;
    
    h4 {
      margin: 0 0 16px 0;
      color: $primary;
      font-size: 1.1rem;
      font-weight: 600;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .patient-count {
        background-color: rgba($primary, 0.1);
        color: $primary;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
    
    .details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
      
      .detail-item {
        display: flex;
        flex-direction: column;
        
        .detail-label {
          font-size: 0.8rem;
          color: rgba($dark-grey, 0.6);
          margin-bottom: 4px;
        }
        
        .detail-value {
          font-size: 0.9rem;
          color: $dark-grey;
          font-weight: 500;
        }
      }
    }
    
    .empty-patients,
    .empty-activity {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 24px;
      text-align: center;
      
      i {
        font-size: 2rem;
        color: rgba($dark-grey, 0.2);
        margin-bottom: 12px;
      }
      
      p {
        color: rgba($dark-grey, 0.6);
        margin: 0;
      }
    }
    
    .patients-list {
      .patient-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-bottom: 1px solid rgba($dark-grey, 0.1);
        
        &:last-child {
          border-bottom: none;
        }
        
        .patient-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .patient-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .patient-name {
            font-weight: 500;
            color: $dark-grey;
          }
          
          .patient-email {
            font-size: 0.8rem;
            color: rgba($dark-grey, 0.6);
          }
        }
        
        .patient-status {
          .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            
            &.active {
              background-color: rgba($success, 0.1);
              color: $success;
            }
            
            &.pending {
              background-color: rgba($warning, 0.1);
              color: $warning;
            }
            
            &.inactive {
              background-color: rgba($danger, 0.1);
              color: $danger;
            }
          }
        }
      }
    }
    
    .activity-list {
      .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px;
        border-bottom: 1px solid rgba($dark-grey, 0.1);
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba($primary-subtle, 0.1);
          border-radius: 50%;
          
          i {
            color: $primary;
            font-size: 0.9rem;
          }
        }
        
        .activity-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .activity-description {
            font-weight: 500;
            color: $dark-grey;
          }
          
          .activity-time {
            font-size: 0.8rem;
            color: rgba($dark-grey, 0.6);
          }
        }
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid rgba($dark-grey, 0.1);
  
  button {
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    
    i {
      font-size: 0.9rem;
    }
  }
  
  .btn-secondary {
    background-color: transparent;
    border: 1px solid rgba($dark-grey, 0.2);
    color: $dark-grey;
    
    &:hover {
      background-color: rgba($dark-grey, 0.05);
    }
  }
  
  .btn-primary {
    background-color: $primary;
    border: none;
    color: $white;
    
    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}
</style>
