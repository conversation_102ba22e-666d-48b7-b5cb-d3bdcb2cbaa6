// Button styles for RespiraSense
.submit-button-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 1.5rem;
}

.submit-button {
  min-width: 180px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: darken(#dc3545, 5%);
  }
  
  &:active {
    transform: translateY(1px);
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
  }
  
  &:disabled {
    background-color: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    box-shadow: none;
  }
}

// Outline variant
.submit-button.outline {
  background-color: #fff;
  color: #dc3545;
  border: 1px solid #dc3545;

  &:hover { 
    background-color: #f8f9fa; 
  }
  
  &:disabled { 
    background-color: #fff; 
    color: #a0aec0; 
    border-color: #e2e8f0;
  }
}
