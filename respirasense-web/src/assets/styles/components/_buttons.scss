
.btn {
    background-color: transparent;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 7px 10px 7px 10px;

    transition: color .3s ease;

   /*  &:focus {
        outline: none;
    }

    &:hover {
        color: darken($color: white, $amount: 10);
    } */

}


.btn-primary {
    background-color: $primary;
    border-radius: 2px;
    padding: 12px 16px;
    font-size: 16px;

    transition: background-color .3s ease;

    &:disabled {
        background-color: lighten($color: $primary, $amount: 15);
    }

    &:hover {
        background-color: darken($color: $primary, $amount: 15);
    }
}

.btn-block {
    width: 100%;
}

// Standardized button styles for RespiraSense
.submit-button-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 1.5rem;
}

.submit-button {
  min-width: 180px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: darken(#dc3545, 5%);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    background-color: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    box-shadow: none;
  }
}

// Outline variant
.submit-button.outline {
  background-color: #fff;
  color: #dc3545;
  border: 1px solid #dc3545;

  &:hover {
    background-color: #f8f9fa;
  }

  &:disabled {
    background-color: #fff;
    color: #a0aec0;
    border-color: #e2e8f0;
  }
}

.social-btn.google-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-top: 5px;
    width: 100%;
    height: 42px;
    background-color: $bg-button-google;
    border-radius: 2px;
    box-shadow:0px 2px 4px 0px #2e2e2e40;

    transition: box-shadow .3s ease;

    .social-icon-wrapper {
        position: absolute;
        margin-top: 1px;
        margin-left: 1px;
        width: 40px;
        height: 40px;
        border-radius: 2px;
        background-color: $white;
    }
    .social-icon {
        position: absolute;
        margin-top: 11px;
        margin-left: 11px;
        width: 18px;
        height: 18px;
    }
    .btn-text {
        width: 100%;
        text-align: center;
        font-size: 14px;
        letter-spacing: 0.2px;
    }
    &:hover {
        box-shadow: 0 0 1px $dark-grey;
    }
    &:active {
        background: $button-active-google;
    }
}

.btn-ck {
    padding: 15px;
    cursor: pointer;
    line-height: 1;
    text-transform: uppercase;
    align-items: center;
    justify-content: center;
    outline: none;
    background: white;
    border: 0;
    box-shadow: $box-shadow-ck-x $box-shadow-ck-y 0 0 rgba(0,0,0,.5);
    transform: translateX(0) translateY(0);
    display: inline-block;
    transition: .3s;

    &:hover {
        transform: translateX(4px) translateY(4px);
        box-shadow: calc(#{$box-shadow-ck-x}/2) calc(#{$box-shadow-ck-y}/2) 0 0 rgba(0,0,0,.5);
    }

    &.fill {
        @include setBackground($primary, $text-primary-color);

        &-secondary {
            @include setBackground($secondary, $text-secondary-color);
        }
        &-success {
            @include setBackground($success, $text-success);
        }
        &-danger {
            @include setBackground($danger, $text-white);
        }
        &-info {
            @include setBackground($info, $text-info);
        }
        &-warning {
            @include setBackground($warning, $text-warning);
        }
    }

    &.otl {
        @include buttonOutline($primary);

        &-secondary {
            @include buttonOutline($secondary);
        }
        &-success {
            @include buttonOutline($success);
        }
        &-danger {
            @include buttonOutline($danger);
        }
        &-info {
            @include buttonOutline($info);
        }
        &-warning {
            @include buttonOutline($warning);
        }
    }
}
