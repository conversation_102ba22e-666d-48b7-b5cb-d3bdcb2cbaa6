* {
    margin: 0px;
    box-sizing: border-box;
}

html, body {
    height: 100vh;
    background-color: $background-app;
}

main {
    width: 100%;
}
.m {
    &-auto{
        margin: auto; 
     }
    &-1{
       margin: .5rem; 
    }
    &-2{
       margin: 1rem; 
    }
    &-3{
       margin: 1.25rem; 
    }
    &-4{
        margin: 1.50rem; 
    }
    &-5{
        margin: 2rem; 
    }
}
.my {
    &-1{
       margin: .5rem 0 .5rem 0;
    }
    &-2{
       margin: 1rem 0 1rem 0;
    }
    &-3{
       margin: 1.25rem 0 1.25rem 0;
    }
    &-4{
       margin: 1.50rem 0 1.50rem 0;

    }
    &-5{
       margin: 2rem 0 2rem 0;
    }
}
.mr {
    &-1{
       margin-right: .5rem; 
    }
    &-2{
       margin-right: 1rem; 
    }
    &-3{
       margin-right: 1.25rem; 
    }
    &-4{
        margin-right: 1.50rem; 
    }
    &-5{
        margin-right: 2rem; 
    }
}

.mt {
    &-1{
       margin-top: .25rem; 
    }
    &-2{
       margin-top: .5rem; 
    }
    &-3{
       margin-top: 1rem; 
    }
    &-4{
        margin-top: 1.25rem; 
    }
    &-5{
        margin-top: 1.5rem; 
    }
}
.mx {
    &-1{
       margin-left: .25rem; 
       margin-right: .25rem; 
    }
    &-2{
       margin-left: .5rem; 
       margin-right: .5rem; 
    }
    &-3{
       margin-left: 1rem; 
       margin-right: 1rem; 
    }
    &-4{
        margin-left: 1.25rem; 
        margin-right: 1.25rem; 
    }
    &-5{
        margin-left: 1.5rem; 
        margin-right: 1.5rem; 
    }
}
.ml {
    &-1{
       margin-left: .25rem; 
    }
    &-2{
       margin-left: .5rem; 
    }
    &-3{
       margin-left: 1rem; 
    }
    &-4{
        margin-left: 1.25rem; 
    }
    &-5{
        margin-left: 1.5rem; 
    }
}
.mb-1 {
    margin-bottom: 5px;
}

.mb-5 {
    margin-bottom: 20px;
}

.pointer {
    cursor: pointer;
}

.text-center {
    text-align: center;
}

.ft-10 {
    font-size: 10pt;
}

.block{
    display: block;
}

.flex {
    display: flex;
    &-inline {
        display: inline-flex;
    }

    &.justify-center {
        justify-content: center;
    }

    &.justify-between {
        justify-content: space-between;
    }

    &.align-center{
        align-items: center;
    }

    &.flex-column {
        flex-direction: column;
    }
    &.flex-row {
        flex-direction: row;
    }
}

.bg {
    &-info {
        @include setBackground($info, $text-info);
      }
    
      &-success {
        @include setBackground($success, $text-success);
      }
    
      &-warning {
         @include setBackground($warning, $text-warning);
      }
    
      &-danger {
        @include setBackground($danger, $text-white);
    }
}
.text-danger {
    color: #b61440;
}
.alert {
    &-err {
        margin: 15px 0px 15px 0px;
        padding: .8rem;
        border-radius: 5px;
        border: 2px solid #b71540;
        background: #b7154038;
        color: #b71540;
    }
    &-success {
        margin: 15px 0px 15px 0px;
        padding: .8rem;
        border-radius: 5px;
        border: 2px solid #5bd83c;
        background: #caecc1a6;
        color: #5bd83c
    }
}
.text-muted {
    color: #6c757d
}
// width
.w-100 {
    width: 100%;
} 

$num: 5;
@while $num < 101 {
  .w-#{$num} {
    width: $num +0% !important;
  }
  $num: $num +5;
}
//Heigth
.h-auto {
    height: auto !important;
}
.font-weight-bold 
{ 
    font-weight: bold; 
}
.text-lowercase  { text-transform: lowercase !important; }
.text-uppercase  { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.align-self-center {
    align-self: center;
}

// .col {
//   padding: 10px;
//   margin: 5px 0;
//   text-align: center;
// }
  
//   /* GENERAL GRID STYLES */
  
//   .col {
//     flex-basis: 100%;
//   }
  
//   @media screen and (min-width: 980px) {
//     .row {
//       display: flex;
//       flex-direction: row;
//       flex-wrap: nowrap;
//     }
   
//     .col {
//       flex: 1;
//     }
   
//     .col-05 {
//       flex: 0.5;
//     }
  
//     .col-10 {
//       flex: 1;
//     }
  
//     .col-15 {
//       flex: 1.5;
//     }
    
//     .col-20 {
//       flex: 2;
//     }
    
//     .col-25 {
//       flex: 2.5;
//     }
    
//     .col-30 {
//       flex: 3;
//     }
    
//     .col-35 {
//       flex: 3.5;
//     }
  
//     .col-40 {
//       flex: 4;
//     }
    
//     .col-45 {
//       flex: 4.5;
//     }
    
//     .col-50 {
//       flex: 5;
//     }
    
//     .col-55 {
//       flex: 5.5;
//     }
    
//     .col-60 {
//       flex: 6;
//     }
    
//     .col-65 {
//       flex: 6.5;
//     }
    
//     .col-70 {
//       flex: 7;
//     }
    
//     .col-75 {
//       flex: 7.5;
//     }
    
//     .col-80 {
//       flex: 8;
//     }
    
//     .col-85 {
//       flex: 8.5;
//     }
    
//     .col-90 {
//       flex: 9;
//     }
    
//     .col-95 {
//       flex: 9.5;
//     }
  // }