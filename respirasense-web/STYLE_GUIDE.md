# RespiraSense Web Style Guide

This document outlines the styling guidelines for the RespiraSense Web application to ensure consistency across the codebase.

## Color Palette

### Primary Colors
- **Primary Red**: `#b71540` - Used for primary actions, links, and important UI elements
- **White**: `#fff` - Used for backgrounds, text on dark backgrounds

### Secondary Colors
- **Light Background**: `#f8f9fc` - Used for page backgrounds
- **Dark Grey**: `#363636` - Used for text
- **Light Grey**: `#d8d8d8` - Used for borders, dividers

### Alert Colors
- **Success**: `#4ca1a3` - Used for success messages
- **Warning**: `#5C62C5` - Used for warning messages
- **Danger**: `#b71540` - Used for error messages (same as primary red)
- **Info**: `#b71540` - Used for informational messages (same as primary red)

## Typography

- **Primary Font**: 'Lato Regular', Helvetica, sans-serif
- **Base Text Color**: `#2c3e50`
- **Muted Text Color**: `#6c757d`

## Components

### Buttons

#### Primary Button
```html
<button class="btn btn-primary">Primary Button</button>
```
- Red background (`#b71540`)
- White text
- No border

#### Outline Button
```html
<button class="btn-ck otl-danger">Outline Button</button>
```
- White background
- Red text and border

#### Filled Button
```html
<button class="btn-ck fill-danger">Filled Button</button>
```
- Red background
- White text

### Cards

```html
<div class="card">
  <div class="card-header">Header</div>
  <div class="card-body">Body</div>
  <div class="card-footer">Footer</div>
</div>
```
- White background
- Light shadow
- Rounded corners

### Alerts

```html
<div class="alert-err">Error message</div>
<div class="alert-success">Success message</div>
```

### Form Elements

#### Input Fields
```html
<div class="form-group">
  <label for="input">Label</label>
  <input type="text" id="input" />
</div>
```
- No border by default
- Red bottom border on focus

## Layout

### Containers
- `.page` - Main content container with padding

### Spacing
- Use margin utilities: `m-1`, `m-2`, `m-3`, `m-4`, `m-5`
- Use padding utilities: `p-1`, `p-2`, `p-3`, `p-4`, `p-5`

### Flexbox
- `.flex` - Display flex
- `.flex.justify-center` - Center items horizontally
- `.flex.align-center` - Center items vertically
- `.flex.flex-column` - Column direction

## Best Practices

1. **Use Variables**: Always use the SCSS variables defined in `_settings.scss` instead of hardcoding colors.

2. **Consistent Components**: Use the predefined components and styles for consistency.

3. **Responsive Design**: Ensure all components work well on different screen sizes.

4. **Accessibility**: Maintain good contrast ratios for text and interactive elements.

5. **Naming Conventions**: Follow the established naming conventions for classes.

## Examples

### Login Form
```html
<form class="login-form">
  <div class="form-group">
    <label for="email">Email Address</label>
    <input type="email" id="email" />
  </div>
  <div class="form-group">
    <label for="password">Password</label>
    <input type="password" id="password" />
  </div>
  <button class="btn-ck fill-danger w-50 m-auto">Sign in</button>
</form>
```

### Dashboard Card
```html
<div class="stat-card">
  <div class="stat-icon">
    <i class="fas fa-users"></i>
  </div>
  <div class="stat-content">
    <h3 class="stat-title">Total Patients</h3>
    <div class="stat-value">125</div>
  </div>
</div>
```

### Alert List
```html
<div class="alert-list">
  <div class="alert-item critical">
    <div class="alert-icon">
      <i class="fas fa-exclamation-circle"></i>
    </div>
    <div class="alert-content">
      <div class="alert-header">
        <span class="alert-title">Critical Alert</span>
        <span class="alert-time">12:30 PM</span>
      </div>
      <p class="alert-message">Patient needs attention</p>
    </div>
  </div>
</div>
```
