{"indexes": [{"collectionGroup": "users_roles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "alerts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.quantity_type", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "DESCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.category_type", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "ASCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.category_type", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "DESCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.activity_name", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "ASCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.quantity_type", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "ASCENDING"}]}, {"collectionGroup": "healthKit", "queryScope": "COLLECTION", "fields": [{"fieldPath": "body.activity_name", "order": "ASCENDING"}, {"fieldPath": "header.creation_date_time", "order": "DESCENDING"}]}, {"collectionGroup": "patient_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "practitionerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "patient_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "practitionerId", "order": "ASCENDING"}, {"fieldPath": "requestType", "order": "ASCENDING"}]}, {"collectionGroup": "patient_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "patientId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "requestType", "order": "ASCENDING"}]}, {"collectionGroup": "patient_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "patientEmail", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "patient_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "practitionerEmail", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "users_roles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "readings", "fieldPath": "timestamp", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "daily_metrics", "fieldPath": "lastUpdated", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}