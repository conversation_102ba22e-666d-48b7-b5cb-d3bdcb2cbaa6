{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "16"}, "main": "index.js", "dependencies": {"cors": "^2.8.5", "firebase-admin": "^10.0.2", "firebase-functions": "^3.18.0"}, "devDependencies": {"firebase-functions-test": "^0.2.0"}, "private": true}